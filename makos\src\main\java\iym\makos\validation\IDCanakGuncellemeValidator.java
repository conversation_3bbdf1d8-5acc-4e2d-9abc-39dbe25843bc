package iym.makos.validation;

import iym.common.model.api.GuncellemeTip;
import iym.common.model.api.HedefTip;
import iym.common.model.api.KararTuru;
import iym.common.model.entity.iym.Hedefler;
import iym.common.model.entity.iym.MahkemeKarar;
import iym.common.service.db.DbHedeflerService;
import iym.common.service.db.DbMahkemeKararService;
import iym.common.util.CommonUtils;
import iym.common.validation.ValidationResult;
import iym.makos.dto.id.IDCanakGuncellemeRequest;
import iym.makos.model.api.CanakGuncellemeDetay;
import iym.makos.model.api.CanakGuncellemeKararDetay;
import iym.makos.model.api.MahkemeKararDetay;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class IDCanakGuncellemeValidator extends MahkemeKararRequestValidatorBase<IDCanakGuncellemeRequest> {

    private final DbMahkemeKararService dbMahkemeKararService;
    private final DbHedeflerService dbHedeflerService;

    @Autowired
    public IDCanakGuncellemeValidator(DbMahkemeKararService dbMahkemeKararService,
                                      DbHedeflerService dbHedeflerService) {
        this.dbMahkemeKararService = dbMahkemeKararService;
        this.dbHedeflerService = dbHedeflerService;
    }

    @Override
    public ValidationResult validate(IDCanakGuncellemeRequest request) {

        try{
            ValidationResult validationResult = super.validate(request);
            if (!validationResult.isValid()) {
                return validationResult;
            }

            for (CanakGuncellemeKararDetay canakGuncellemeKararDetay : request.getCanakGuncellemeKararDetayListesi()) {

                MahkemeKararDetay iliskiliMahkemeKararDetay = canakGuncellemeKararDetay.getMahkemeKararDetay();
                if (iliskiliMahkemeKararDetay == null) {
                    validationResult.addFailedReason("İlişkili mahkeme karar boş olamaz.");
                } else {

                    Optional<MahkemeKarar> iliskiliMahkemeKararOpt = dbMahkemeKararService.findBy(iliskiliMahkemeKararDetay.getMahkemeIlIlceKodu()
                            , iliskiliMahkemeKararDetay.getMahkemeKodu()
                            , iliskiliMahkemeKararDetay.getMahkemeKararNo()
                            , iliskiliMahkemeKararDetay.getSorusturmaNo());

                    if (iliskiliMahkemeKararOpt.isEmpty()) {
                        String errorStr = String.format("Güncelle Konu Mahkeme Karar Bulunamadı: Mahheme İl/İlçe Kodu: %s Mahkeme Kodu: %s, Karar No: %s, Soruşturma No :%s"
                                , iliskiliMahkemeKararDetay.getMahkemeIlIlceKodu()
                                , iliskiliMahkemeKararDetay.getMahkemeKodu()
                                , iliskiliMahkemeKararDetay.getMahkemeKararNo()
                                , iliskiliMahkemeKararDetay.getSorusturmaNo());
                        validationResult.addFailedReason(errorStr);
                    } else {

                        MahkemeKarar iliskiliMahkemeKarar = iliskiliMahkemeKararOpt.get();

                        List<CanakGuncellemeDetay> guncellemeList = canakGuncellemeKararDetay.getCanakGuncellemeDetayList();
                        for (CanakGuncellemeDetay canakGuncellemeDetay : guncellemeList) {
                            GuncellemeTip tip = canakGuncellemeDetay.getGuncellemeTip();
                            String canakNo = canakGuncellemeDetay.getCanakHedefDetay().getCanakHedefNo();
                            String hedefNo = canakGuncellemeDetay.getCanakHedefDetay().getHedef().getHedefNo();
                            HedefTip hedefTip = canakGuncellemeDetay.getCanakHedefDetay().getHedef().getHedefTip();

                            //Sistemde bu hedef var mi?
                            Optional<Hedefler> existingHedefBilgisiOpt = dbHedeflerService.findByMahkemeKararIdAndHedefNoAndHedefTipi(iliskiliMahkemeKarar.getId(), hedefNo, hedefTip);

                            if (existingHedefBilgisiOpt.isEmpty()) {
                                validationResult.addFailedReason(hedefNo + " numaralı hedef  ilişkli mahkeme kararda bulunamadı.");
                            } else {

                                if (tip == GuncellemeTip.EKLE) {
                                    if (CommonUtils.isNullOrEmpty(canakNo)) {
                                        validationResult.addFailedReason("Ekleme durumunda canak numarası boş olamaz");
                                    } else {
                                        String existingHedefNo = existingHedefBilgisiOpt.get().getCanakNo();
                                        if (!CommonUtils.isNullOrEmpty(existingHedefNo) && existingHedefNo.equals(canakNo)) {
                                            validationResult.addFailedReason(canakNo + " var olan CANAK numaradan faklı olmalıdır.");
                                        }
                                    }
                                } else {
                                    if (!CommonUtils.isNullOrEmpty(canakNo)) {
                                        validationResult.addFailedReason("Çıkarılacak canak numarası için çanak numarası boş olmalıdır.");
                                    }
                                }
                            }
                        }
                    }
                }
            }

            return validationResult;
        }catch (Exception e) {
            log.error("Validation failed", e);
            return new ValidationResult("Validation failed. Internal error");
        }

    }

    @Override
    public KararTuru getRelatedKararTuru() {
        return KararTuru.ILETISIMIN_DENETLENMESI_CANAK_GUNCELLEME;
    }

}

