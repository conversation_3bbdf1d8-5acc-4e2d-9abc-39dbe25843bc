package iym.makos.mapper;

import iym.common.model.entity.iym.HedeflerTalep;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import iym.makos.dto.HedeflerTalepDTO;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

class HedeflerTalepMapperTest {

    private HedeflerTalepMapper hedeflerTalepMapper;
    private HedeflerTalep hedeflerTalep;
    private HedeflerTalepDTO hedeflerTalepDTO;
    private Date testDate;

    @BeforeEach
    void setUp() {
        hedeflerTalepMapper = new HedeflerTalepMapper();
        testDate = new Date();

        hedeflerTalep = HedeflerTalep.builder()
                .id(1L)
                .birimKod(100L)
                .kullaniciId(200L)
                .tekMasaKulId(300L)
                .hedefNo("HEDEF-001")
                .hedefAdi("Test Hedef")
                .hedefSoyadi("Test Soyad")
                .baslamaTarihi(testDate)
                .suresi(30L)
                .sureTipi(1L)
                .uzatmaSayisi(0L)
                .durumu("AKTIF")
                .aciklama("Test açıklama")
                .mahkemeKararTalepId(500L)
                .hedefAidiyatId(600L)
                .grupKod(700L)
                .aidiyatKod("AIDIYAT-01")
                .uniqKod(800L)
                .kayitTarihi(testDate)
                .tanimlamaTarihi(testDate)
                .kapatmaKararId(null)
                .kapatmaTarihi(null)
                .imha("HAYIR")
                .imhaTarihi(null)
                .uzatmaId(null)
                .acilmi("E")
                .hedef118Adi("Test 118 Adı")
                .hedef118Soyadi("Test 118 Soyadı")
                .hedef118Adres("Test 118 Adres")
                .hedefTipi(1L)
                .canakNo("CANAK-001")
                .build();

        hedeflerTalepDTO = HedeflerTalepDTO.builder()
                .id(1L)
                .birimKod(100L)
                .kullaniciId(200L)
                .tekMasaKulId(300L)
                .hedefNo("HEDEF-001")
                .hedefAdi("Test Hedef")
                .hedefSoyadi("Test Soyad")
                .baslamaTarihi(testDate)
                .suresi(30L)
                .sureTipi(1L)
                .uzatmaSayisi(0L)
                .durumu("AKTIF")
                .aciklama("Test açıklama")
                .mahkemeKararId(500L)
                .hedefAidiyatId(600L)
                .grupKod(700L)
                .aidiyatKod("AIDIYAT-01")
                .uniqKod(800L)
                .kayitTarihi(testDate)
                .tanimlamaTarihi(testDate)
                .kapatmaKararId(null)
                .kapatmaTarihi(null)
                .imha("HAYIR")
                .imhaTarihi(null)
                .uzatmaId(null)
                .acilmi("E")
                .hedef118Adi("Test 118 Adı")
                .hedef118Soyadi("Test 118 Soyadı")
                .hedef118Adres("Test 118 Adres")
                .hedefTipi(1L)
                .canakNo("CANAK-001")
                .build();
    }

    @Test
    void toDto_shouldMapEntityToDto() {
        // When
        HedeflerTalepDTO result = hedeflerTalepMapper.toDto(hedeflerTalep);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(hedeflerTalep.getId());
        assertThat(result.getBirimKod()).isEqualTo(hedeflerTalep.getBirimKod());
        assertThat(result.getKullaniciId()).isEqualTo(hedeflerTalep.getKullaniciId());
        assertThat(result.getTekMasaKulId()).isEqualTo(hedeflerTalep.getTekMasaKulId());
        assertThat(result.getHedefNo()).isEqualTo(hedeflerTalep.getHedefNo());
        assertThat(result.getHedefAdi()).isEqualTo(hedeflerTalep.getHedefAdi());
        assertThat(result.getHedefSoyadi()).isEqualTo(hedeflerTalep.getHedefSoyadi());
        assertThat(result.getBaslamaTarihi()).isEqualTo(hedeflerTalep.getBaslamaTarihi());
        assertThat(result.getSuresi()).isEqualTo(hedeflerTalep.getSuresi());
        assertThat(result.getSureTipi()).isEqualTo(hedeflerTalep.getSureTipi());
        assertThat(result.getUzatmaSayisi()).isEqualTo(hedeflerTalep.getUzatmaSayisi());
        assertThat(result.getDurumu()).isEqualTo(hedeflerTalep.getDurumu());
        assertThat(result.getAciklama()).isEqualTo(hedeflerTalep.getAciklama());
        assertThat(result.getMahkemeKararId()).isEqualTo(hedeflerTalep.getMahkemeKararTalepId());
        assertThat(result.getHedefAidiyatId()).isEqualTo(hedeflerTalep.getHedefAidiyatId());
        assertThat(result.getGrupKod()).isEqualTo(hedeflerTalep.getGrupKod());
        assertThat(result.getAidiyatKod()).isEqualTo(hedeflerTalep.getAidiyatKod());
        assertThat(result.getUniqKod()).isEqualTo(hedeflerTalep.getUniqKod());
        assertThat(result.getKayitTarihi()).isEqualTo(hedeflerTalep.getKayitTarihi());
        assertThat(result.getTanimlamaTarihi()).isEqualTo(hedeflerTalep.getTanimlamaTarihi());
        assertThat(result.getKapatmaKararId()).isEqualTo(hedeflerTalep.getKapatmaKararId());
        assertThat(result.getKapatmaTarihi()).isEqualTo(hedeflerTalep.getKapatmaTarihi());
        assertThat(result.getImha()).isEqualTo(hedeflerTalep.getImha());
        assertThat(result.getImhaTarihi()).isEqualTo(hedeflerTalep.getImhaTarihi());
        assertThat(result.getUzatmaId()).isEqualTo(hedeflerTalep.getUzatmaId());
        assertThat(result.getAcilmi()).isEqualTo(hedeflerTalep.getAcilmi());
        assertThat(result.getHedef118Adi()).isEqualTo(hedeflerTalep.getHedef118Adi());
        assertThat(result.getHedef118Soyadi()).isEqualTo(hedeflerTalep.getHedef118Soyadi());
        assertThat(result.getHedef118Adres()).isEqualTo(hedeflerTalep.getHedef118Adres());
        assertThat(result.getHedefTipi()).isEqualTo(hedeflerTalep.getHedefTipi());
        assertThat(result.getCanakNo()).isEqualTo(hedeflerTalep.getCanakNo());
    }

    @Test
    void toDto_shouldReturnNullWhenEntityIsNull() {
        // When
        HedeflerTalepDTO result = hedeflerTalepMapper.toDto(null);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void toEntity_shouldMapDtoToEntity() {
        // When
        HedeflerTalep result = hedeflerTalepMapper.toEntity(hedeflerTalepDTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(hedeflerTalepDTO.getId());
        assertThat(result.getBirimKod()).isEqualTo(hedeflerTalepDTO.getBirimKod());
        assertThat(result.getKullaniciId()).isEqualTo(hedeflerTalepDTO.getKullaniciId());
        assertThat(result.getTekMasaKulId()).isEqualTo(hedeflerTalepDTO.getTekMasaKulId());
        assertThat(result.getHedefNo()).isEqualTo(hedeflerTalepDTO.getHedefNo());
        assertThat(result.getHedefAdi()).isEqualTo(hedeflerTalepDTO.getHedefAdi());
        assertThat(result.getHedefSoyadi()).isEqualTo(hedeflerTalepDTO.getHedefSoyadi());
        assertThat(result.getBaslamaTarihi()).isEqualTo(hedeflerTalepDTO.getBaslamaTarihi());
        assertThat(result.getSuresi()).isEqualTo(hedeflerTalepDTO.getSuresi());
        assertThat(result.getSureTipi()).isEqualTo(hedeflerTalepDTO.getSureTipi());
        assertThat(result.getUzatmaSayisi()).isEqualTo(hedeflerTalepDTO.getUzatmaSayisi());
        assertThat(result.getDurumu()).isEqualTo(hedeflerTalepDTO.getDurumu());
        assertThat(result.getAciklama()).isEqualTo(hedeflerTalepDTO.getAciklama());
        assertThat(result.getMahkemeKararTalepId()).isEqualTo(hedeflerTalepDTO.getMahkemeKararId());
        assertThat(result.getHedefAidiyatId()).isEqualTo(hedeflerTalepDTO.getHedefAidiyatId());
        assertThat(result.getGrupKod()).isEqualTo(hedeflerTalepDTO.getGrupKod());
        assertThat(result.getAidiyatKod()).isEqualTo(hedeflerTalepDTO.getAidiyatKod());
        assertThat(result.getUniqKod()).isEqualTo(hedeflerTalepDTO.getUniqKod());
        assertThat(result.getKayitTarihi()).isEqualTo(hedeflerTalepDTO.getKayitTarihi());
        assertThat(result.getTanimlamaTarihi()).isEqualTo(hedeflerTalepDTO.getTanimlamaTarihi());
        assertThat(result.getKapatmaKararId()).isEqualTo(hedeflerTalepDTO.getKapatmaKararId());
        assertThat(result.getKapatmaTarihi()).isEqualTo(hedeflerTalepDTO.getKapatmaTarihi());
        assertThat(result.getImha()).isEqualTo(hedeflerTalepDTO.getImha());
        assertThat(result.getImhaTarihi()).isEqualTo(hedeflerTalepDTO.getImhaTarihi());
        assertThat(result.getUzatmaId()).isEqualTo(hedeflerTalepDTO.getUzatmaId());
        assertThat(result.getAcilmi()).isEqualTo(hedeflerTalepDTO.getAcilmi());
        assertThat(result.getHedef118Adi()).isEqualTo(hedeflerTalepDTO.getHedef118Adi());
        assertThat(result.getHedef118Soyadi()).isEqualTo(hedeflerTalepDTO.getHedef118Soyadi());
        assertThat(result.getHedef118Adres()).isEqualTo(hedeflerTalepDTO.getHedef118Adres());
        assertThat(result.getHedefTipi()).isEqualTo(hedeflerTalepDTO.getHedefTipi());
        assertThat(result.getCanakNo()).isEqualTo(hedeflerTalepDTO.getCanakNo());
    }

    @Test
    void toEntity_shouldReturnNullWhenDtoIsNull() {
        // When
        HedeflerTalep result = hedeflerTalepMapper.toEntity(null);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void updateEntityFromDto_shouldUpdateEntityWithDtoValues() {
        // Given
        HedeflerTalep existingEntity = HedeflerTalep.builder()
                .id(1L)
                .birimKod(100L)
                .kullaniciId(200L)
                .hedefNo("HEDEF-001")
                .durumu("AKTIF")
                .build();

        HedeflerTalepDTO updatedDto = HedeflerTalepDTO.builder()
                .id(1L)
                .birimKod(101L)
                .kullaniciId(201L)
                .hedefNo("HEDEF-002")
                .durumu("PASIF")
                .build();

        // When
        HedeflerTalep result = hedeflerTalepMapper.updateEntityFromDto(existingEntity, updatedDto);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(1L);
        assertThat(result.getBirimKod()).isEqualTo(101L);
        assertThat(result.getKullaniciId()).isEqualTo(201L);
        assertThat(result.getHedefNo()).isEqualTo("HEDEF-002");
        assertThat(result.getDurumu()).isEqualTo("PASIF");
    }

    @Test
    void updateEntityFromDto_shouldReturnEntityWhenDtoIsNull() {
        // Given
        HedeflerTalep existingEntity = HedeflerTalep.builder()
                .id(1L)
                .birimKod(100L)
                .kullaniciId(200L)
                .hedefNo("HEDEF-001")
                .durumu("AKTIF")
                .build();

        // When
        HedeflerTalep result = hedeflerTalepMapper.updateEntityFromDto(existingEntity, null);

        // Then
        assertThat(result).isEqualTo(existingEntity);
    }

    @Test
    void updateEntityFromDto_shouldReturnNullWhenEntityIsNull() {
        // When
        HedeflerTalep result = hedeflerTalepMapper.updateEntityFromDto(null, hedeflerTalepDTO);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void toDtoList_shouldMapEntityListToDtoList() {
        // Given
        List<HedeflerTalep> entityList = Arrays.asList(hedeflerTalep, hedeflerTalep);

        // When
        List<HedeflerTalepDTO> result = hedeflerTalepMapper.toDtoList(entityList);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(2);
        assertThat(result.get(0).getId()).isEqualTo(hedeflerTalep.getId());
        assertThat(result.get(1).getId()).isEqualTo(hedeflerTalep.getId());
    }

    @Test
    void toDtoList_shouldReturnNullWhenEntityListIsNull() {
        // When
        List<HedeflerTalepDTO> result = hedeflerTalepMapper.toDtoList(null);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void toEntityList_shouldMapDtoListToEntityList() {
        // Given
        List<HedeflerTalepDTO> dtoList = Arrays.asList(hedeflerTalepDTO, hedeflerTalepDTO);

        // When
        List<HedeflerTalep> result = hedeflerTalepMapper.toEntityList(dtoList);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(2);
        assertThat(result.get(0).getId()).isEqualTo(hedeflerTalepDTO.getId());
        assertThat(result.get(1).getId()).isEqualTo(hedeflerTalepDTO.getId());
    }

    @Test
    void toEntityList_shouldReturnNullWhenDtoListIsNull() {
        // When
        List<HedeflerTalep> result = hedeflerTalepMapper.toEntityList(null);

        // Then
        assertThat(result).isNull();
    }
}
