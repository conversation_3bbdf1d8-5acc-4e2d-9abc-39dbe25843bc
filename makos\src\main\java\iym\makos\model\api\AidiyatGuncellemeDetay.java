package iym.makos.model.api;

import iym.common.model.api.GuncellemeTip;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
@ToString
@EqualsAndHashCode
public class AidiyatGuncellemeDetay {

  @NotNull
  private GuncellemeTip guncellemeTip;

  @NotNull
  private String aidiyatKodu;

}

