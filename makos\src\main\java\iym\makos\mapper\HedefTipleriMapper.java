package iym.makos.mapper;

import iym.common.model.entity.iym.HedefTipleri;
import org.springframework.stereotype.Component;
import iym.makos.dto.HedefTipleriDTO;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper for HedefTipleri entity and DTO
 */
@Component
public class HedefTipleriMapper {

    /**
     * Convert entity to DTO
     * @param entity HedefTipleri entity
     * @return HedefTipleriDTO
     */
    public HedefTipleriDTO toDto(HedefTipleri entity) {
        if (entity == null) {
            return null;
        }

        return HedefTipleriDTO.builder()
                .id(entity.getId())
                .hedefKodu(entity.getHedefKodu())
                .hedefTipi(entity.getHedefTipi())
                .sonlandirmami(entity.getSonlandirmami())
                .karsiligi(entity.getKarsiligi())
                .sno(entity.getSno())
                .hedefTanim(entity.getHedefTanim())
                .durum(entity.getDurum())
                .hitapTip(entity.getHitapTip())
                .hitapIcerikTip(entity.getHitapIcerikTip())
                .hitapIcindemi(entity.getHitapIcindemi())
                .hitapEh(entity.getHitapEh())
                .minl(entity.getMinl())
                .maxl(entity.getMaxl())
                .imhaYapilsinmi(entity.getImhaYapilsinmi())
                .tasinabilirmi(entity.getTasinabilirmi())
                .aktifmi(entity.getAktifmi())
                .hitapaGonderilecekmi(entity.getHitapaGonderilecekmi())
                .build();
    }

    /**
     * Convert DTO to entity
     * @param dto HedefTipleriDTO
     * @return HedefTipleri entity
     */
    public HedefTipleri toEntity(HedefTipleriDTO dto) {
        if (dto == null) {
            return null;
        }

        return HedefTipleri.builder()
                .id(dto.getId())
                .hedefKodu(dto.getHedefKodu())
                .hedefTipi(dto.getHedefTipi())
                .sonlandirmami(dto.getSonlandirmami())
                .karsiligi(dto.getKarsiligi())
                .sno(dto.getSno())
                .hedefTanim(dto.getHedefTanim())
                .durum(dto.getDurum())
                .hitapTip(dto.getHitapTip())
                .hitapIcerikTip(dto.getHitapIcerikTip())
                .hitapIcindemi(dto.getHitapIcindemi())
                .hitapEh(dto.getHitapEh())
                .minl(dto.getMinl())
                .maxl(dto.getMaxl())
                .imhaYapilsinmi(dto.getImhaYapilsinmi())
                .tasinabilirmi(dto.getTasinabilirmi())
                .aktifmi(dto.getAktifmi())
                .hitapaGonderilecekmi(dto.getHitapaGonderilecekmi())
                .build();
    }

    /**
     * Update entity from DTO
     * @param entity HedefTipleri entity to update
     * @param dto HedefTipleriDTO with new values
     * @return Updated HedefTipleri entity
     */
    public HedefTipleri updateEntityFromDto(HedefTipleri entity, HedefTipleriDTO dto) {
        if (entity == null || dto == null) {
            return entity;
        }

        entity.setId(dto.getId());
        // Note: We don't update hedefKodu as it's the primary key
        entity.setHedefTipi(dto.getHedefTipi());
        entity.setSonlandirmami(dto.getSonlandirmami());
        entity.setKarsiligi(dto.getKarsiligi());
        entity.setSno(dto.getSno());
        entity.setHedefTanim(dto.getHedefTanim());
        entity.setDurum(dto.getDurum());
        entity.setHitapTip(dto.getHitapTip());
        entity.setHitapIcerikTip(dto.getHitapIcerikTip());
        entity.setHitapIcindemi(dto.getHitapIcindemi());
        entity.setHitapEh(dto.getHitapEh());
        entity.setMinl(dto.getMinl());
        entity.setMaxl(dto.getMaxl());
        entity.setImhaYapilsinmi(dto.getImhaYapilsinmi());
        entity.setTasinabilirmi(dto.getTasinabilirmi());
        entity.setAktifmi(dto.getAktifmi());
        entity.setHitapaGonderilecekmi(dto.getHitapaGonderilecekmi());

        return entity;
    }

    /**
     * Convert list of entities to list of DTOs
     * @param entityList List of HedefTipleri entities
     * @return List of HedefTipleriDTO
     */
    public List<HedefTipleriDTO> toDtoList(List<HedefTipleri> entityList) {
        if (entityList == null) {
            return null;
        }

        return entityList.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }

    /**
     * Convert list of DTOs to list of entities
     * @param dtoList List of HedefTipleriDTO
     * @return List of HedefTipleri entities
     */
    public List<HedefTipleri> toEntityList(List<HedefTipleriDTO> dtoList) {
        if (dtoList == null) {
            return null;
        }

        return dtoList.stream()
                .map(this::toEntity)
                .collect(Collectors.toList());
    }
}
