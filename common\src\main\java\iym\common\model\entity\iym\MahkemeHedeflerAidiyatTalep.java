package iym.common.model.entity.iym;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * Entity class for MAHKEME_HEDEFLER_AIDIYAT_TALEP table
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode

@Entity(name = "MahkemeHedeflerAidiyatTalep")
@Table(name = "MAHKEME_HEDEFLER_AIDIYAT_TALEP")
public class MahkemeHedeflerAidiyatTalep implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "MAH_HED_AID_TALEP_SEQ")
    @SequenceGenerator(name = "MAH_HED_AID_TALEP_SEQ", sequenceName = "MAH_HED_AID_TALEP_SEQ", allocationSize = 1)
    private Long id;

    @Column(name = "HEDEF_ID", nullable = false)
    @NotNull
    private Long hedefId;

    @Column(name = "AIDIYAT_KOD", nullable = false, length = 15)
    @NotNull
    @Size(max = 15)
    private String aidiyatKod;

    @Column(name = "TARIH", nullable = false)
    @NotNull
    @Temporal(TemporalType.TIMESTAMP)
    private Date tarih;

    @Column(name = "MAHKEME_KARAR_ID", nullable = false)
    @NotNull
    private Long mahkemeKararId;

    @Column(name = "DURUMU", length = 10)
    @Size(max = 10)
    private String durumu;

    @Column(name = "KULLANICI_ID")
    private Long kullaniciId;
}
