package iym.makos.model.api;

import io.swagger.v3.oas.annotations.media.Schema;
import iym.common.model.api.HedefTip;
import iym.common.model.api.SureTip;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@Jacksonized
@ToString
@EqualsAndHashCode
public class HedefID {

  @NotNull
  private String hedef;

  @NotNull
  private HedefTip hedefTip;

  @NotNull
  private String hedefAd;

  @NotNull
  private String hedefSoyad;

  @NotNull
  private LocalDateTime baslamaTarihi;

  @NotNull
  private SureTip sureTip;

  @NotNull
  private Integer sure;

  @Schema(description = "Uzatilan/Sonlandirilan Hedefin ilgili <PERSON>. Sadece uzatma/sonlandirma kararlarinda gerekli")
  private MahkemeKararDetay ilgiliMahkemeKararDetayi;

  @Schema(description = "Uzatma Sayisi. Sadece uzatma kararlarinda gerekli")
  private Integer uzatmaSayisi;

  private List<String> hedefAidiyatKodlari;

  private String canakNo;

}

