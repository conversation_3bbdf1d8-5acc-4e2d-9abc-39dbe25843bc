/*
 * MAKOS OpenAPI definition
 * MAKOS Application
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package iym.makos.api.client.gen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import iym.makos.api.client.gen.model.EvrakDetay;
import iym.makos.api.client.gen.model.HedefAdSoyadGuncellemeKararDetay;
import iym.makos.api.client.gen.model.MahkemeKararBilgisi;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * Mahkeme Karar Detaylari
 */
@JsonPropertyOrder({
  IDHedefAdSoyadGuncellemeRequest.JSON_PROPERTY_ID,
  IDHedefAdSoyadGuncellemeRequest.JSON_PROPERTY_EVRAK_DETAY,
  IDHedefAdSoyadGuncellemeRequest.JSON_PROPERTY_MAHKEME_KARAR_BILGISI,
  IDHedefAdSoyadGuncellemeRequest.JSON_PROPERTY_HEDEF_AD_SOYAD_GUNCELLEME_KARAR_DETAY_LISTESI
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class IDHedefAdSoyadGuncellemeRequest {
  public static final String JSON_PROPERTY_ID = "id";
  private UUID id;

  public static final String JSON_PROPERTY_EVRAK_DETAY = "evrakDetay";
  private EvrakDetay evrakDetay;

  public static final String JSON_PROPERTY_MAHKEME_KARAR_BILGISI = "mahkemeKararBilgisi";
  private MahkemeKararBilgisi mahkemeKararBilgisi;

  public static final String JSON_PROPERTY_HEDEF_AD_SOYAD_GUNCELLEME_KARAR_DETAY_LISTESI = "hedefAdSoyadGuncellemeKararDetayListesi";
  private List<HedefAdSoyadGuncellemeKararDetay> hedefAdSoyadGuncellemeKararDetayListesi = new ArrayList<>();

  public IDHedefAdSoyadGuncellemeRequest() {
  }

  public IDHedefAdSoyadGuncellemeRequest id(UUID id) {
    
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_ID)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public UUID getId() {
    return id;
  }


  @JsonProperty(JSON_PROPERTY_ID)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setId(UUID id) {
    this.id = id;
  }


  public IDHedefAdSoyadGuncellemeRequest evrakDetay(EvrakDetay evrakDetay) {
    
    this.evrakDetay = evrakDetay;
    return this;
  }

   /**
   * Get evrakDetay
   * @return evrakDetay
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_EVRAK_DETAY)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public EvrakDetay getEvrakDetay() {
    return evrakDetay;
  }


  @JsonProperty(JSON_PROPERTY_EVRAK_DETAY)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setEvrakDetay(EvrakDetay evrakDetay) {
    this.evrakDetay = evrakDetay;
  }


  public IDHedefAdSoyadGuncellemeRequest mahkemeKararBilgisi(MahkemeKararBilgisi mahkemeKararBilgisi) {
    
    this.mahkemeKararBilgisi = mahkemeKararBilgisi;
    return this;
  }

   /**
   * Get mahkemeKararBilgisi
   * @return mahkemeKararBilgisi
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_BILGISI)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public MahkemeKararBilgisi getMahkemeKararBilgisi() {
    return mahkemeKararBilgisi;
  }


  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_BILGISI)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setMahkemeKararBilgisi(MahkemeKararBilgisi mahkemeKararBilgisi) {
    this.mahkemeKararBilgisi = mahkemeKararBilgisi;
  }


  public IDHedefAdSoyadGuncellemeRequest hedefAdSoyadGuncellemeKararDetayListesi(List<HedefAdSoyadGuncellemeKararDetay> hedefAdSoyadGuncellemeKararDetayListesi) {
    
    this.hedefAdSoyadGuncellemeKararDetayListesi = hedefAdSoyadGuncellemeKararDetayListesi;
    return this;
  }

  public IDHedefAdSoyadGuncellemeRequest addHedefAdSoyadGuncellemeKararDetayListesiItem(HedefAdSoyadGuncellemeKararDetay hedefAdSoyadGuncellemeKararDetayListesiItem) {
    if (this.hedefAdSoyadGuncellemeKararDetayListesi == null) {
      this.hedefAdSoyadGuncellemeKararDetayListesi = new ArrayList<>();
    }
    this.hedefAdSoyadGuncellemeKararDetayListesi.add(hedefAdSoyadGuncellemeKararDetayListesiItem);
    return this;
  }

   /**
   * Güncelleme yapılacak hedefler için mahkeme karar bilgisi ve karara ait güncellenecek ad, soyad bilgileri
   * @return hedefAdSoyadGuncellemeKararDetayListesi
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_HEDEF_AD_SOYAD_GUNCELLEME_KARAR_DETAY_LISTESI)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public List<HedefAdSoyadGuncellemeKararDetay> getHedefAdSoyadGuncellemeKararDetayListesi() {
    return hedefAdSoyadGuncellemeKararDetayListesi;
  }


  @JsonProperty(JSON_PROPERTY_HEDEF_AD_SOYAD_GUNCELLEME_KARAR_DETAY_LISTESI)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setHedefAdSoyadGuncellemeKararDetayListesi(List<HedefAdSoyadGuncellemeKararDetay> hedefAdSoyadGuncellemeKararDetayListesi) {
    this.hedefAdSoyadGuncellemeKararDetayListesi = hedefAdSoyadGuncellemeKararDetayListesi;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    IDHedefAdSoyadGuncellemeRequest idHedefAdSoyadGuncellemeRequest = (IDHedefAdSoyadGuncellemeRequest) o;
    return Objects.equals(this.id, idHedefAdSoyadGuncellemeRequest.id) &&
        Objects.equals(this.evrakDetay, idHedefAdSoyadGuncellemeRequest.evrakDetay) &&
        Objects.equals(this.mahkemeKararBilgisi, idHedefAdSoyadGuncellemeRequest.mahkemeKararBilgisi) &&
        Objects.equals(this.hedefAdSoyadGuncellemeKararDetayListesi, idHedefAdSoyadGuncellemeRequest.hedefAdSoyadGuncellemeKararDetayListesi);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, evrakDetay, mahkemeKararBilgisi, hedefAdSoyadGuncellemeKararDetayListesi);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class IDHedefAdSoyadGuncellemeRequest {\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    evrakDetay: ").append(toIndentedString(evrakDetay)).append("\n");
    sb.append("    mahkemeKararBilgisi: ").append(toIndentedString(mahkemeKararBilgisi)).append("\n");
    sb.append("    hedefAdSoyadGuncellemeKararDetayListesi: ").append(toIndentedString(hedefAdSoyadGuncellemeKararDetayListesi)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

