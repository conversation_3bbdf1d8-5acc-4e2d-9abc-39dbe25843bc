package iym.db.jpa.service.impl;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.MahkemeAidiyatTalep;
import iym.common.service.db.DbMahkemeAidiyatTalepService;
import iym.db.jpa.dao.MahkemeAidiyatTalepRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Service implementation for MahkemeAidiyatTalep entity
 */
@Service
public class DbMahkemeAidiyatTalepServiceImpl extends GenericDbServiceImpl<MahkemeAidiyatTalep, Long> implements DbMahkemeAidiyatTalepService {

    private final MahkemeAidiyatTalepRepo mahkemeAidiyatTalepRepo;

    @Autowired
    public DbMahkemeAidiyatTalepServiceImpl(MahkemeAidiyatTalepRepo repository) {
        super(repository);
        this.mahkemeAidiyatTalepRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public List<MahkemeAidiyatTalep> findByMahkemeKararTalepId(Long mahkemeKararTalepId) {
        return mahkemeAidiyatTalepRepo.findByMahkemeKararTalepId(mahkemeKararTalepId);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<MahkemeAidiyatTalep> findById(Long id){
        return mahkemeAidiyatTalepRepo.findById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<MahkemeAidiyatTalep> findByMahkemeKararTalepIdAndAidiyatKod(Long mahkemeKararTalepId, String aidiyatKodu){
        return mahkemeAidiyatTalepRepo.findByMahkemeKararTalepIdAndAidiyatKod(mahkemeKararTalepId, aidiyatKodu);
    }




}
