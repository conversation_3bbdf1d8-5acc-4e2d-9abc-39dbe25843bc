package iym.common.model.entity.iym;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * Entity class for MAHKEME_KARAR_TALEP table
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode

@Entity(name = "MahkemeKarar")
@Table(name = "MAHKEME_KARAR")
public class MahkemeKarar implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "EVRAK_ID", nullable = false)
    @NotNull
    private Long evrakId;

    @Column(name = "KULLANICI_ID", nullable = false)
    @NotNull
    private Long kullaniciId;

    @Column(name = "KAYIT_TARIHI", nullable = false)
    @NotNull
    @Temporal(TemporalType.TIMESTAMP)
    private Date kayitTarihi;

    @Column(name = "DURUM", length = 20)
    @Size(max = 20)
    private String durum;

    @Column(name = "HUKUK_BIRIM", length = 50)
    @Size(max = 50)
    private String hukukBirim;

    @Column(name = "KARAR_TIP", length = 20)
    @Size(max = 20)
    private String kararTip;

    @Column(name = "MAH_KARAR_BAS_TAR")
    @Temporal(TemporalType.TIMESTAMP)
    private Date mahKararBasTar;

    @Column(name = "MAH_KARAR_BITIS_TAR")
    @Temporal(TemporalType.TIMESTAMP)
    private Date mahKararBitisTar;

    @Column(name = "MAHKEME_ADI", length = 250)
    @Size(max = 250)
    private String mahkemeAdi;

    @Column(name = "MAHKEME_KARAR_NO", length = 50)
    @Size(max = 50)
    private String mahkemeKararNo;

    @Column(name = "MAHKEME_ILI", length = 4, nullable = false)
    @NotNull
    @Size(max = 4)
    private String mahkemeIlIlceKodu;

    @Column(name = "ACIKLAMA", length = 500)
    @Size(max = 500)
    private String aciklama;

    @Column(name = "HAKIM_SICIL_NO", length = 20)
    @Size(max = 20)
    private String hakimSicilNo;

    @Column(name = "SORUSTURMA_NO", length = 50)
    @Size(max = 50)
    private String sorusturmaNo;


    @Column(name = "MAHKEME_KODU", length = 10)
    @Size(max = 10)
    private String mahkemeKodu;
}
