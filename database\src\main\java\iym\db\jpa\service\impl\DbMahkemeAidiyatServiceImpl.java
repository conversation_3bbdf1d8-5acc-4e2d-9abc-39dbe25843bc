package iym.db.jpa.service.impl;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.MahkemeAidiyat;
import iym.common.service.db.DbMahkemeAidiyatService;
import iym.db.jpa.dao.MahkemeAidiyatRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Service implementation for MahkemeAidiyat entity
 */
@Service
public class DbMahkemeAidiyatServiceImpl extends GenericDbServiceImpl<MahkemeAidiyat, Long> implements DbMahkemeAidiyatService {

    private final MahkemeAidiyatRepo mahkemeAidiyatRepo;

    @Autowired
    public DbMahkemeAidiyatServiceImpl(MahkemeAidiyatRepo mahkemeAidiyatRepo) {
        super(mahkemeAidiyatRepo);
        this.mahkemeAidiyatRepo = mahkemeAidiyatRepo;
    }

    @Override
    @Transactional(readOnly = true)
    public List<MahkemeAidiyat> findByMahkemeKararId(Long mahkemeKararId) {
        return mahkemeAidiyatRepo.findByMahkemeKararId(mahkemeKararId);
    }


    @Override
    @Transactional(readOnly = true)
    public Optional<MahkemeAidiyat> findByMahkemeKararIdAndAidiyatKod(Long mahkemeKararId, String aidiyatKod) {
        return mahkemeAidiyatRepo.findByMahkemeKararIdAndAidiyatKod(mahkemeKararId, aidiyatKod);
    }
}
