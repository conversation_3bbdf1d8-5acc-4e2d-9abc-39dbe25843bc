/*
 * MAKOS OpenAPI definition
 * MAKOS Application
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package iym.makos.api.client.gen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import iym.makos.api.client.gen.model.Hedef;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * HedefWithAdSoyad
 */
@JsonPropertyOrder({
  HedefWithAdSoyad.JSON_PROPERTY_HEDEF,
  HedefWithAdSoyad.JSON_PROPERTY_HEDEF_AD,
  HedefWithAdSoyad.JSON_PROPERTY_HEDEF_SOYAD
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class HedefWithAdSoyad {
  public static final String JSON_PROPERTY_HEDEF = "hedef";
  private Hedef hedef;

  public static final String JSON_PROPERTY_HEDEF_AD = "hedefAd";
  private String hedefAd;

  public static final String JSON_PROPERTY_HEDEF_SOYAD = "hedefSoyad";
  private String hedefSoyad;

  public HedefWithAdSoyad() {
  }

  public HedefWithAdSoyad hedef(Hedef hedef) {
    
    this.hedef = hedef;
    return this;
  }

   /**
   * Get hedef
   * @return hedef
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_HEDEF)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public Hedef getHedef() {
    return hedef;
  }


  @JsonProperty(JSON_PROPERTY_HEDEF)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setHedef(Hedef hedef) {
    this.hedef = hedef;
  }


  public HedefWithAdSoyad hedefAd(String hedefAd) {
    
    this.hedefAd = hedefAd;
    return this;
  }

   /**
   * Get hedefAd
   * @return hedefAd
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_HEDEF_AD)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getHedefAd() {
    return hedefAd;
  }


  @JsonProperty(JSON_PROPERTY_HEDEF_AD)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setHedefAd(String hedefAd) {
    this.hedefAd = hedefAd;
  }


  public HedefWithAdSoyad hedefSoyad(String hedefSoyad) {
    
    this.hedefSoyad = hedefSoyad;
    return this;
  }

   /**
   * Get hedefSoyad
   * @return hedefSoyad
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_HEDEF_SOYAD)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getHedefSoyad() {
    return hedefSoyad;
  }


  @JsonProperty(JSON_PROPERTY_HEDEF_SOYAD)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setHedefSoyad(String hedefSoyad) {
    this.hedefSoyad = hedefSoyad;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    HedefWithAdSoyad hedefWithAdSoyad = (HedefWithAdSoyad) o;
    return Objects.equals(this.hedef, hedefWithAdSoyad.hedef) &&
        Objects.equals(this.hedefAd, hedefWithAdSoyad.hedefAd) &&
        Objects.equals(this.hedefSoyad, hedefWithAdSoyad.hedefSoyad);
  }

  @Override
  public int hashCode() {
    return Objects.hash(hedef, hedefAd, hedefSoyad);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class HedefWithAdSoyad {\n");
    sb.append("    hedef: ").append(toIndentedString(hedef)).append("\n");
    sb.append("    hedefAd: ").append(toIndentedString(hedefAd)).append("\n");
    sb.append("    hedefSoyad: ").append(toIndentedString(hedefSoyad)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

