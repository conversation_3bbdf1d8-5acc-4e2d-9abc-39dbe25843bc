package iym.common.model.entity.postgresql;

import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * PostgreSQL entity for ulkeler table
 * This entity is specifically configured for PostgreSQL database
 * 
 * Table structure:
 * - id: BIGINT GENERATED BY DEFAULT AS IDENTITY
 * - is_deleted: BOOLEAN
 * - created_at: TIMESTAMP
 * - updated_at: TIMESTAMP
 * - deleted_at: TIMESTAMP
 * - created_by: VARCHAR(255)
 * - updated_by: VARCHAR(255)
 * - deleted_by: VARCHAR(255)
 * - name: VARCHA<PERSON>(255)
 * - code: VARCHAR(255)
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode

@Entity(name = "UlkelerPostgreSQL")
@Table(name = "ulkeler", schema = "public")
public class Ulkeler implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "is_deleted")
    private Boolean isDeleted;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @Column(name = "deleted_at")
    private LocalDateTime deletedAt;

    @Column(name = "created_by")
    @Size(max = 255)
    private String createdBy;

    @Column(name = "updated_by")
    @Size(max = 255)
    private String updatedBy;

    @Column(name = "deleted_by")
    @Size(max = 255)
    private String deletedBy;

    @Column(name = "name")
    @Size(max = 255)
    private String name;

    @Column(name = "code")
    @Size(max = 255)
    private String code;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
        if (isDeleted == null) {
            isDeleted = false;
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }

    /**
     * Soft delete method
     */
    public void softDelete(String deletedBy) {
        this.isDeleted = true;
        this.deletedAt = LocalDateTime.now();
        this.deletedBy = deletedBy;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * Check if entity is deleted
     */
    public boolean isDeleted() {
        return Boolean.TRUE.equals(isDeleted);
    }
}
