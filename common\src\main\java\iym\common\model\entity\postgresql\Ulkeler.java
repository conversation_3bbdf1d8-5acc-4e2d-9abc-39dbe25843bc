package iym.common.model.entity.postgresql;

import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

/**
 * PostgreSQL entity for ulkeler table
 * This entity is specifically configured for PostgreSQL database
 *
 * Table structure:
 * - id: BIGINT GENERATED BY DEFAULT AS IDENTITY
 * - is_deleted: BO<PERSON>EAN
 * - created_at: TIMESTAMP
 * - updated_at: TIMESTAMP
 * - deleted_at: TIMESTAMP
 * - created_by: VARCHAR(255)
 * - updated_by: VARCHAR(255)
 * - deleted_by: VARCHAR(255)
 * - name: VA<PERSON><PERSON><PERSON>(255)
 * - code: VA<PERSON><PERSON><PERSON>(255)
 */
@Entity
@Table(name = "ulkeler")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@SQLDelete(sql = "UPDATE ulkeler SET is_deleted = true, deleted_at = now() WHERE id = ?")
@Where(clause = "is_deleted = false")
public class <PERSON><PERSON><PERSON> extends BaseEntity {

    private String name;
    private String code;
}
