package iym.common.model.api;

import lombok.Getter;

import java.util.Optional;

@Getter
public enum KullaniciKurum {

    ADLI(1, "ADLI"),
    EMNIYET(2, "EMNIYET"),
    MIT(3, "MIT"),
    JANDARMA(4, "JANDARMA"),
    BTK(5, "BTK"),
    EMNIYET_SIBER(8, "EMNIYET SIBER");

    private final int value;

    private final String name;

    KullaniciKurum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public static Optional<KullaniciKurum> convert(int kurum){

        for (KullaniciKurum type : KullaniciKurum.values()){
            if (type.value == kurum)
                return Optional.of(type);
        }

        return Optional.empty();
    }

}
