package iym.makos.model.api;

import iym.common.model.api.Hedef;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
@ToString
@EqualsAndHashCode
public class CanakHedefDetay {

  @NotNull
  @Valid
  private Hedef hedef;

  @NotNull
  private String canakHedefNo;

}

