openapi: 3.0.1
info:
  title: MAKOS OpenAPI definition
  description: MAKOS Application
  version: v1.0
servers:
  - url: http://localhost:5000/makosapi
    description: Generated server url
security:
  - BasicAuth: []
paths:
  /mahkemeKarar/mahkemeKoduGuncelle:
    post:
      tags:
        - mahkeme-karar-controller
      operationId: mahkemeKoduGuncelle
      requestBody:
        content:
          multipart/form-data:
            schema:
              required:
                - mahkemeKararDetay
                - mahkemeKararDosyasi
              type: object
              properties:
                mahkemeKararDosyasi:
                  type: string
                  format: binary
                mahkemeKararDetay:
                  $ref: '#/components/schemas/IDMahkemeKoduGuncellemeRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: object
  /mahkemeKarar/kararGonderIT:
    post:
      tags:
        - mahkeme-karar-controller
      operationId: kararGonderIT
      requestBody:
        content:
          multipart/form-data:
            schema:
              required:
                - mahkemeKararDetayIT
                - mahkemeKararDosyasiIT
              type: object
              properties:
                mahkemeKararDosyasiIT:
                  type: string
                  format: binary
                mahkemeKararDetayIT:
                  $ref: '#/components/schemas/ITKararRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ITKararResponse'
  /mahkemeKarar/kararGonderID:
    post:
      tags:
        - mahkeme-karar-controller
      operationId: kararGonderID
      requestBody:
        content:
          multipart/form-data:
            schema:
              required:
                - mahkemeKararDetayID
                - mahkemeKararDosyasiID
              type: object
              properties:
                mahkemeKararDosyasiID:
                  type: string
                  format: binary
                mahkemeKararDetayID:
                  $ref: '#/components/schemas/IDKararRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/IDKararResponse'
  /mahkemeKarar/hedefAdSoyadGuncelle:
    post:
      tags:
        - mahkeme-karar-controller
      operationId: hedefAdSoyadGuncelle
      requestBody:
        content:
          multipart/form-data:
            schema:
              required:
                - mahkemeKararDetay
                - mahkemeKararDosyasi
              type: object
              properties:
                mahkemeKararDosyasi:
                  type: string
                  format: binary
                mahkemeKararDetay:
                  $ref: '#/components/schemas/IDHedefAdSoyadGuncellemeRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: object
  /mahkemeKarar/canakNoGuncelle:
    post:
      tags:
        - mahkeme-karar-controller
      operationId: canakNoGuncelle
      requestBody:
        content:
          multipart/form-data:
            schema:
              required:
                - mahkemeKararDetay
                - mahkemeKararDosyasi
              type: object
              properties:
                mahkemeKararDosyasi:
                  type: string
                  format: binary
                mahkemeKararDetay:
                  $ref: '#/components/schemas/IDCanakGuncellemeRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: object
  /mahkemeKarar/aidiyatBilgisiGuncelle:
    post:
      tags:
        - mahkeme-karar-controller
      operationId: aidiyatBilgisiGuncelle
      requestBody:
        content:
          multipart/form-data:
            schema:
              required:
                - mahkemeKararDetay
                - mahkemeKararDosyasi
              type: object
              properties:
                mahkemeKararDosyasi:
                  type: string
                  format: binary
                mahkemeKararDetay:
                  $ref: '#/components/schemas/IDAidiyatBilgisiGuncellemeRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: object
  /check/healthCheck:
    get:
      tags:
        - health-check-controller
      operationId: healthCheck
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/HealthCheckResponse'
  /check/healthCheckQueryAdmin:
    get:
      tags:
        - health-check-controller
      operationId: healthCheckQueryAdmin
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/HealthCheckResponse'
  /check/healthCheckAuthorized:
    get:
      tags:
        - health-check-controller
      operationId: healthCheckAuthorized
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/HealthCheckResponse'
  /check/healthCheckAdmin:
    get:
      tags:
        - health-check-controller
      operationId: healthCheckAdmin
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/HealthCheckResponse'
components:
  schemas:
    EvrakDetay:
      required:
        - evrakKurum
        - evrakNo
        - evrakTarihi
        - evrakTuru
      type: object
      properties:
        evrakNo:
          maxLength: 50
          minLength: 0
          type: string
        evrakTarihi:
          type: string
          format: date-time
        evrakKurum:
          type: string
          enum:
            - "01"
            - "02"
            - "03"
            - "08"
            - "18"
            - "19"
            - "23"
            - "25"
            - "26"
        evrakTuru:
          type: string
          enum:
            - "0"
            - "1"
            - "2"
        havaleBirimi:
          type: string
        aciklama:
          type: string
        geldigiIl:
          type: string
        acilmi:
          type: boolean
        evrakKonusu:
          type: string
    IDMahkemeKoduGuncellemeRequest:
      required:
        - evrakDetay
        - id
        - mahkemeKararBilgisi
        - mahkemeKoduGuncellemeDetayListesi
      type: object
      properties:
        id:
          type: string
          format: uuid
        evrakDetay:
          $ref: '#/components/schemas/EvrakDetay'
        mahkemeKararBilgisi:
          $ref: '#/components/schemas/MahkemeKararBilgisi'
        mahkemeKoduGuncellemeDetayListesi:
          maxItems: **********
          minItems: 1
          type: array
          description: Güncelleme yapılacak mahkeme karar bilgisi ve karara ait güncellenecek
            yeni kod/il bilgileri
          items:
            $ref: '#/components/schemas/MahkemeKoduGuncellemeDetay'
      description: Mahkeme Karar Detaylari
    MahkemeKararBilgisi:
      required:
        - mahkemeKararDetay
        - mahkemeKararTipi
      type: object
      properties:
        mahkemeKararTipi:
          type: string
          enum:
            - "100"
            - "150"
            - "151"
            - "200"
            - "300"
            - "350"
            - "400"
            - "410"
            - "450"
            - "510"
            - "520"
            - "530"
            - "600"
            - "700"
            - "710"
            - "720"
            - "730"
            - "800"
            - "900"
            - "910"
            - "920"
        mahkemeKararDetay:
          $ref: '#/components/schemas/MahkemeKararDetay'
        mahkemeKararNo:
          type: string
        sorusturmaNo:
          type: string
        mahkemeKodu:
          type: string
        ilIlceKodu:
          type: string
        aciklama:
          type: string
      description: Güncellemeyi talep eden  mahkeme karar bilgileri
    MahkemeKararDetay:
      required:
        - mahkemeIlIlceKodu
        - mahkemeKodu
      type: object
      properties:
        mahkemeKodu:
          type: string
        mahkemeIlIlceKodu:
          type: string
        mahkemeKararNo:
          type: string
        sorusturmaNo:
          type: string
        aciklama:
          type: string
      description: Aidiyat değişikliği yapılacak mahkeme karar bilgileri
    MahkemeKoduGuncellemeDetay:
      required:
        - mahkemeKararDetay
        - yeniMahkemeKodu
      type: object
      properties:
        mahkemeKararDetay:
          $ref: '#/components/schemas/MahkemeKararDetay'
        yeniMahkemeKodu:
          type: string
      description: Güncelleme yapılacak mahkeme karar bilgisi ve karara ait güncellenecek
        yeni kod/il bilgileri
    Hedef:
      required:
        - hedefNo
        - hedefTip
      type: object
      properties:
        hedefNo:
          type: string
        hedefTip:
          type: string
          enum:
            - "10"
            - "20"
            - "30"
            - "40"
            - "41"
            - "42"
            - "43"
            - "44"
            - "50"
            - "51"
            - "52"
            - "53"
            - "54"
            - "55"
            - "56"
            - "60"
            - "70"
            - "71"
            - "80"
            - "81"
            - "82"
            - "83"
            - "90"
            - "91"
            - "92"
            - "99"
            - "200"
            - "210"
            - "201"
            - "211"
    HedefDetayIT:
      required:
        - baslamaTarihi
        - bitisTarihi
        - hedef
        - sorguTipi
        - tespitTuru
      type: object
      properties:
        sorguTipi:
          type: string
          enum:
            - "1"
            - "2"
            - "3"
        hedef:
          $ref: '#/components/schemas/Hedef'
        karsiHedef:
          $ref: '#/components/schemas/Hedef'
        baslamaTarihi:
          type: string
          format: date-time
        bitisTarihi:
          type: string
          format: date-time
        tespitTuru:
          type: string
        tespitTuruDetay:
          type: string
        aciklama:
          type: string
    ITKararRequest:
      required:
        - evrakDetay
        - id
        - kararTuru
        - mahkemeKararDetay
      type: object
      properties:
        id:
          type: string
          format: uuid
        kararTuru:
          type: string
          enum:
            - "0"
        evrakDetay:
          $ref: '#/components/schemas/EvrakDetay'
        mahkemeKararDetay:
          $ref: '#/components/schemas/MahkemeKararIT'
      description: IT Mahkeme Karar Detaylari
    MahkemeKararIT:
      required:
        - hedefDetayListesi
        - mahkemeKararBilgisi
      type: object
      properties:
        mahkemeKararBilgisi:
          $ref: '#/components/schemas/MahkemeKararBilgisi'
        hedefDetayListesi:
          maxItems: **********
          minItems: 1
          type: array
          items:
            $ref: '#/components/schemas/HedefDetayIT'
    ITKararResponse:
      required:
        - requestId
        - responseCode
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        responseCode:
          type: string
          enum:
            - SUCCESS
            - FAILED
        responseMessage:
          type: string
    HedefDetayID:
      required:
        - baslamaTarihi
        - hedefNoAdSoyad
        - sure
        - sureTip
      type: object
      properties:
        hedefNoAdSoyad:
          $ref: '#/components/schemas/HedefWithAdSoyad'
        baslamaTarihi:
          type: string
          format: date-time
        sureTip:
          type: string
          enum:
            - "1"
            - "2"
            - "0"
        sure:
          type: integer
          format: int32
        ilgiliMahkemeKararDetayi:
          $ref: '#/components/schemas/MahkemeKararDetay'
        uzatmaSayisi:
          type: integer
          description: Uzatma Sayisi. Sadece uzatma kararlarinda gerekli
          format: int32
        hedefAidiyatKodlari:
          type: array
          items:
            type: string
        canakNo:
          type: string
          description: Canak numarası. Sadece yeni kararda girilebilir. Zorunlu olmayan
            alan
    HedefWithAdSoyad:
      required:
        - hedef
        - hedefAd
        - hedefSoyad
      type: object
      properties:
        hedef:
          $ref: '#/components/schemas/Hedef'
        hedefAd:
          type: string
        hedefSoyad:
          type: string
    IDKararRequest:
      required:
        - evrakDetay
        - hedefDetayListesi
        - id
        - kararTuru
        - mahkemeKararDetayi
      type: object
      properties:
        id:
          type: string
          format: uuid
        kararTuru:
          type: string
          enum:
            - "0"
            - "1"
            - "2"
        evrakDetay:
          $ref: '#/components/schemas/EvrakDetay'
        mahkemeKararDetayi:
          $ref: '#/components/schemas/MahkemeKararID'
        hedefDetayListesi:
          maxItems: **********
          minItems: 1
          type: array
          items:
            $ref: '#/components/schemas/HedefDetayID'
      description: ID Mahkeme Karar Detaylari
    MahkemeKararID:
      required:
        - mahkemeKararBilgisi
      type: object
      properties:
        mahkemeKararBilgisi:
          $ref: '#/components/schemas/MahkemeKararBilgisi'
        aidiyatKodlari:
          type: array
          items:
            type: string
        sucTipiKodlari:
          type: array
          items:
            type: string
    ApiResponse:
      required:
        - responseCode
      type: object
      properties:
        responseCode:
          type: string
          enum:
            - SUCCESS
            - FAILED
        responseMessage:
          type: string
    IDKararResponse:
      required:
        - requestId
        - response
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: '#/components/schemas/ApiResponse'
    HedefAdSoyadGuncellemeKararDetay:
      required:
        - hedefAdSoyadListesi
        - mahkemeKararDetay
      type: object
      properties:
        mahkemeKararDetay:
          $ref: '#/components/schemas/MahkemeKararDetay'
        hedefAdSoyadListesi:
          maxItems: **********
          minItems: 1
          type: array
          items:
            $ref: '#/components/schemas/HedefWithAdSoyad'
      description: "Güncelleme yapılacak hedefler için mahkeme karar bilgisi ve karara\
        \ ait güncellenecek ad, soyad bilgileri"
    IDHedefAdSoyadGuncellemeRequest:
      required:
        - evrakDetay
        - hedefAdSoyadGuncellemeKararDetayListesi
        - id
        - mahkemeKararBilgisi
      type: object
      properties:
        id:
          type: string
          format: uuid
        evrakDetay:
          $ref: '#/components/schemas/EvrakDetay'
        mahkemeKararBilgisi:
          $ref: '#/components/schemas/MahkemeKararBilgisi'
        hedefAdSoyadGuncellemeKararDetayListesi:
          maxItems: **********
          minItems: 1
          type: array
          description: "Güncelleme yapılacak hedefler için mahkeme karar bilgisi ve\
            \ karara ait güncellenecek ad, soyad bilgileri"
          items:
            $ref: '#/components/schemas/HedefAdSoyadGuncellemeKararDetay'
      description: Mahkeme Karar Detaylari
    CanakGuncellemeDetay:
      required:
        - canakHedefDetay
        - guncellemeTip
      type: object
      properties:
        guncellemeTip:
          type: string
          enum:
            - "0"
            - "1"
        canakHedefDetay:
          $ref: '#/components/schemas/CanakHedefDetay'
    CanakGuncellemeKararDetay:
      required:
        - canakGuncellemeDetayList
      type: object
      properties:
        mahkemeKararDetay:
          $ref: '#/components/schemas/MahkemeKararDetay'
        canakGuncellemeDetayList:
          maxItems: **********
          minItems: 1
          type: array
          items:
            $ref: '#/components/schemas/CanakGuncellemeDetay'
      description: Güncelleme yapılacak mahkeme karar bilgisi ve karara ait güncellenecek
        aidiyat bilgileri
    CanakHedefDetay:
      required:
        - canakHedefNo
        - hedef
      type: object
      properties:
        hedef:
          $ref: '#/components/schemas/Hedef'
        canakHedefNo:
          type: string
    IDCanakGuncellemeRequest:
      required:
        - canakGuncellemeKararDetayListesi
        - evrakDetay
        - id
        - kararTuru
        - mahkemeKararBilgisi
      type: object
      properties:
        id:
          type: string
          format: uuid
        kararTuru:
          type: string
          enum:
            - "0"
            - "1"
            - "2"
        evrakDetay:
          $ref: '#/components/schemas/EvrakDetay'
        mahkemeKararBilgisi:
          $ref: '#/components/schemas/MahkemeKararBilgisi'
        canakGuncellemeKararDetayListesi:
          maxItems: **********
          minItems: 1
          type: array
          description: Güncelleme yapılacak mahkeme karar bilgisi ve karara ait güncellenecek
            aidiyat bilgileri
          items:
            $ref: '#/components/schemas/CanakGuncellemeKararDetay'
      description: Mahkeme Karar Detaylari
    AidiyatGuncellemeDetay:
      required:
        - aidiyatKodu
        - guncellemeTip
      type: object
      properties:
        guncellemeTip:
          type: string
          enum:
            - "0"
            - "1"
        aidiyatKodu:
          type: string
    AidiyatGuncellemeKararDetay:
      required:
        - aidiyatGuncellemeDetayList
        - mahkemeKararDetay
      type: object
      properties:
        mahkemeKararDetay:
          $ref: '#/components/schemas/MahkemeKararDetay'
        aidiyatGuncellemeDetayList:
          maxItems: **********
          minItems: 1
          type: array
          items:
            $ref: '#/components/schemas/AidiyatGuncellemeDetay'
      description: Güncelleme yapılacak mahkeme karar bilgisi ve karara ait güncellenecek
        aidiyat bilgileri
    IDAidiyatBilgisiGuncellemeRequest:
      required:
        - aidiyatGuncellemeKararDetayListesi
        - evrakDetay
        - id
        - mahkemeKararBilgisi
      type: object
      properties:
        id:
          type: string
          format: uuid
        evrakDetay:
          $ref: '#/components/schemas/EvrakDetay'
        mahkemeKararBilgisi:
          $ref: '#/components/schemas/MahkemeKararBilgisi'
        aidiyatGuncellemeKararDetayListesi:
          maxItems: **********
          minItems: 1
          type: array
          description: Güncelleme yapılacak mahkeme karar bilgisi ve karara ait güncellenecek
            aidiyat bilgileri
          items:
            $ref: '#/components/schemas/AidiyatGuncellemeKararDetay'
      description: Mahkeme Karar Detaylari
    HealthCheckResponse:
      required:
        - response
      type: object
      properties:
        response:
          $ref: '#/components/schemas/ApiResponse'
  securitySchemes:
    BasicAuth:
      type: http
      scheme: basic
