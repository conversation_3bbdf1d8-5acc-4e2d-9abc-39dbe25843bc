package iym.makos.service;

import iym.common.model.entity.iym.HedeflerAidiyatTalep;
import iym.common.service.db.DbHedeflerAidiyatTalepService;
import iym.makos.mapper.HedeflerAidiyatTalepMapper;
import iym.makos.service.makos.HedeflerAidiyatTalepService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.web.server.ResponseStatusException;
import iym.makos.dto.HedeflerAidiyatTalepDTO;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class HedeflerAidiyatTalepServiceTest {

    @Mock
    private DbHedeflerAidiyatTalepService dbHedeflerAidiyatTalepService;

    @Mock
    private HedeflerAidiyatTalepMapper hedeflerAidiyatTalepMapper;

    @InjectMocks
    private HedeflerAidiyatTalepService hedeflerAidiyatTalepService;

    private HedeflerAidiyatTalep hedeflerAidiyatTalep;
    private HedeflerAidiyatTalepDTO hedeflerAidiyatTalepDTO;
    private List<HedeflerAidiyatTalep> hedeflerAidiyatTalepList;
    private List<HedeflerAidiyatTalepDTO> hedeflerAidiyatTalepDTOList;
    private Date testDate;

    @BeforeEach
    void setUp() {
        testDate = new Date();

        hedeflerAidiyatTalep = HedeflerAidiyatTalep.builder()
                .id(1L)
                .hedefTalepId(100L)
                .aidiyatKod("AIDIYAT1")
                .tarih(testDate)
                .kullaniciId(200L)
                .durumu("AKTIF")
                .build();

        HedeflerAidiyatTalep hedeflerAidiyatTalep2 = HedeflerAidiyatTalep.builder()
                .id(2L)
                .hedefTalepId(101L)
                .aidiyatKod("AIDIYAT2")
                .tarih(testDate)
                .kullaniciId(201L)
                .durumu("AKTIF")
                .build();

        hedeflerAidiyatTalepDTO = HedeflerAidiyatTalepDTO.builder()
                .id(1L)
                .hedefTalepId(100L)
                .aidiyatKod("AIDIYAT1")
                .tarih(testDate)
                .kullaniciId(200L)
                .durumu("AKTIF")
                .build();

        HedeflerAidiyatTalepDTO hedeflerAidiyatTalepDTO2 = HedeflerAidiyatTalepDTO.builder()
                .id(2L)
                .hedefTalepId(101L)
                .aidiyatKod("AIDIYAT2")
                .tarih(testDate)
                .kullaniciId(201L)
                .durumu("AKTIF")
                .build();

        hedeflerAidiyatTalepList = Arrays.asList(hedeflerAidiyatTalep, hedeflerAidiyatTalep2);
        hedeflerAidiyatTalepDTOList = Arrays.asList(hedeflerAidiyatTalepDTO, hedeflerAidiyatTalepDTO2);
    }

    @Test
    void findAll_shouldReturnAllHedeflerAidiyatTalep() {
        // Given
        when(dbHedeflerAidiyatTalepService.findAll()).thenReturn(hedeflerAidiyatTalepList);
        when(hedeflerAidiyatTalepMapper.toDtoList(hedeflerAidiyatTalepList)).thenReturn(hedeflerAidiyatTalepDTOList);

        // When
        List<HedeflerAidiyatTalepDTO> result = hedeflerAidiyatTalepService.findAll();

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(2);
        assertThat(result).isEqualTo(hedeflerAidiyatTalepDTOList);
        verify(dbHedeflerAidiyatTalepService).findAll();
        verify(hedeflerAidiyatTalepMapper).toDtoList(hedeflerAidiyatTalepList);
    }

    @Test
    void findAll_withPageable_shouldReturnPageOfHedeflerAidiyatTalep() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);
        Page<HedeflerAidiyatTalep> hedeflerAidiyatTalepPage = new PageImpl<>(hedeflerAidiyatTalepList, pageable, hedeflerAidiyatTalepList.size());
        
        when(dbHedeflerAidiyatTalepService.findAll(pageable)).thenReturn(hedeflerAidiyatTalepPage);
        when(hedeflerAidiyatTalepMapper.toDtoList(hedeflerAidiyatTalepList)).thenReturn(hedeflerAidiyatTalepDTOList);

        // When
        Page<HedeflerAidiyatTalepDTO> result = hedeflerAidiyatTalepService.findAll(pageable);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getContent()).isEqualTo(hedeflerAidiyatTalepDTOList);
        assertThat(result.getTotalElements()).isEqualTo(2);
        verify(dbHedeflerAidiyatTalepService).findAll(pageable);
        verify(hedeflerAidiyatTalepMapper).toDtoList(hedeflerAidiyatTalepList);
    }

    @Test
    void findById_shouldReturnHedeflerAidiyatTalep_whenExists() {
        // Given
        when(dbHedeflerAidiyatTalepService.findById(1L)).thenReturn(Optional.of(hedeflerAidiyatTalep));
        when(hedeflerAidiyatTalepMapper.toDto(hedeflerAidiyatTalep)).thenReturn(hedeflerAidiyatTalepDTO);

        // When
        HedeflerAidiyatTalepDTO result = hedeflerAidiyatTalepService.findById(1L);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEqualTo(hedeflerAidiyatTalepDTO);
        verify(dbHedeflerAidiyatTalepService).findById(1L);
        verify(hedeflerAidiyatTalepMapper).toDto(hedeflerAidiyatTalep);
    }

    @Test
    void findById_shouldThrowException_whenNotExists() {
        // Given
        when(dbHedeflerAidiyatTalepService.findById(1L)).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResponseStatusException.class, () -> hedeflerAidiyatTalepService.findById(1L));
        verify(dbHedeflerAidiyatTalepService).findById(1L);
        verify(hedeflerAidiyatTalepMapper, never()).toDto(any());
    }

    @Test
    void findByHedefId_shouldReturnHedeflerAidiyatTalepList() {
        // Given
        when(dbHedeflerAidiyatTalepService.findByHedefTalepId(100L)).thenReturn(Arrays.asList(hedeflerAidiyatTalep));
        when(hedeflerAidiyatTalepMapper.toDtoList(Arrays.asList(hedeflerAidiyatTalep))).thenReturn(Arrays.asList(hedeflerAidiyatTalepDTO));

        // When
        List<HedeflerAidiyatTalepDTO> result = hedeflerAidiyatTalepService.findByHedefId(100L);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(1);
        assertThat(result.get(0)).isEqualTo(hedeflerAidiyatTalepDTO);
        verify(dbHedeflerAidiyatTalepService).findByHedefTalepId(100L);
        verify(hedeflerAidiyatTalepMapper).toDtoList(Arrays.asList(hedeflerAidiyatTalep));
    }

    @Test
    void findByHedefIdAndAidiyatKod_shouldReturnHedeflerAidiyatTalep_whenExists() {
        // Given
        when(dbHedeflerAidiyatTalepService.findByHedefTalepIdAndAidiyatKod(100L, "AIDIYAT1")).thenReturn(Optional.of(hedeflerAidiyatTalep));
        when(hedeflerAidiyatTalepMapper.toDto(hedeflerAidiyatTalep)).thenReturn(hedeflerAidiyatTalepDTO);

        // When
        HedeflerAidiyatTalepDTO result = hedeflerAidiyatTalepService.findByHedefTalepIdAndAidiyatKod(100L, "AIDIYAT1");

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEqualTo(hedeflerAidiyatTalepDTO);
        verify(dbHedeflerAidiyatTalepService).findByHedefTalepIdAndAidiyatKod(100L, "AIDIYAT1");
        verify(hedeflerAidiyatTalepMapper).toDto(hedeflerAidiyatTalep);
    }

    @Test
    void findByHedefIdAndAidiyatKod_shouldThrowException_whenNotExists() {
        // Given
        when(dbHedeflerAidiyatTalepService.findByHedefTalepIdAndAidiyatKod(100L, "AIDIYAT1")).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResponseStatusException.class, () -> hedeflerAidiyatTalepService.findByHedefTalepIdAndAidiyatKod(100L, "AIDIYAT1"));
        verify(dbHedeflerAidiyatTalepService).findByHedefTalepIdAndAidiyatKod(100L, "AIDIYAT1");
        verify(hedeflerAidiyatTalepMapper, never()).toDto(any());
    }

    @Test
    void create_shouldCreateHedeflerAidiyatTalep() {
        // Given
        when(dbHedeflerAidiyatTalepService.findByHedefTalepIdAndAidiyatKod(100L, "AIDIYAT1")).thenReturn(Optional.empty());
        when(hedeflerAidiyatTalepMapper.toEntity(hedeflerAidiyatTalepDTO)).thenReturn(hedeflerAidiyatTalep);
        when(hedeflerAidiyatTalepMapper.toDto(hedeflerAidiyatTalep)).thenReturn(hedeflerAidiyatTalepDTO);

        // When
        HedeflerAidiyatTalepDTO result = hedeflerAidiyatTalepService.create(hedeflerAidiyatTalepDTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEqualTo(hedeflerAidiyatTalepDTO);
        verify(dbHedeflerAidiyatTalepService).findByHedefTalepIdAndAidiyatKod(100L, "AIDIYAT1");
        verify(hedeflerAidiyatTalepMapper).toEntity(hedeflerAidiyatTalepDTO);
        verify(dbHedeflerAidiyatTalepService).save(hedeflerAidiyatTalep);
        verify(hedeflerAidiyatTalepMapper).toDto(hedeflerAidiyatTalep);
    }

    @Test
    void create_shouldThrowException_whenHedeflerAidiyatTalepAlreadyExists() {
        // Given
        when(dbHedeflerAidiyatTalepService.findByHedefTalepIdAndAidiyatKod(100L, "AIDIYAT1")).thenReturn(Optional.of(hedeflerAidiyatTalep));

        // When/Then
        assertThrows(ResponseStatusException.class, () -> hedeflerAidiyatTalepService.create(hedeflerAidiyatTalepDTO));
        verify(dbHedeflerAidiyatTalepService).findByHedefTalepIdAndAidiyatKod(100L, "AIDIYAT1");
        verify(hedeflerAidiyatTalepMapper, never()).toEntity(any());
        verify(dbHedeflerAidiyatTalepService, never()).save(any());
        verify(hedeflerAidiyatTalepMapper, never()).toDto(any());
    }

    @Test
    void update_shouldUpdateHedeflerAidiyatTalep() {
        // Given
        when(dbHedeflerAidiyatTalepService.findById(1L)).thenReturn(Optional.of(hedeflerAidiyatTalep));
        when(dbHedeflerAidiyatTalepService.findByHedefTalepIdAndAidiyatKod(100L, "AIDIYAT1")).thenReturn(Optional.of(hedeflerAidiyatTalep));
        when(hedeflerAidiyatTalepMapper.updateEntityFromDto(hedeflerAidiyatTalep, hedeflerAidiyatTalepDTO)).thenReturn(hedeflerAidiyatTalep);
        when(hedeflerAidiyatTalepMapper.toDto(hedeflerAidiyatTalep)).thenReturn(hedeflerAidiyatTalepDTO);

        // When
        HedeflerAidiyatTalepDTO result = hedeflerAidiyatTalepService.update(1L, hedeflerAidiyatTalepDTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEqualTo(hedeflerAidiyatTalepDTO);
        verify(dbHedeflerAidiyatTalepService).findById(1L);
        verify(dbHedeflerAidiyatTalepService).findByHedefTalepIdAndAidiyatKod(100L, "AIDIYAT1");
        verify(hedeflerAidiyatTalepMapper).updateEntityFromDto(hedeflerAidiyatTalep, hedeflerAidiyatTalepDTO);
        verify(dbHedeflerAidiyatTalepService).update(hedeflerAidiyatTalep);
        verify(hedeflerAidiyatTalepMapper).toDto(hedeflerAidiyatTalep);
    }

    @Test
    void update_shouldThrowException_whenHedeflerAidiyatTalepNotExists() {
        // Given
        when(dbHedeflerAidiyatTalepService.findById(1L)).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResponseStatusException.class, () -> hedeflerAidiyatTalepService.update(1L, hedeflerAidiyatTalepDTO));
        verify(dbHedeflerAidiyatTalepService).findById(1L);
        verify(dbHedeflerAidiyatTalepService, never()).findByHedefTalepIdAndAidiyatKod(anyLong(), anyString());
        verify(hedeflerAidiyatTalepMapper, never()).updateEntityFromDto(any(), any());
        verify(dbHedeflerAidiyatTalepService, never()).update(any());
        verify(hedeflerAidiyatTalepMapper, never()).toDto(any());
    }

    @Test
    void delete_shouldDeleteHedeflerAidiyatTalep() {
        // Given
        when(dbHedeflerAidiyatTalepService.findById(1L)).thenReturn(Optional.of(hedeflerAidiyatTalep));

        // When
        hedeflerAidiyatTalepService.delete(1L);

        // Then
        verify(dbHedeflerAidiyatTalepService).findById(1L);
        verify(dbHedeflerAidiyatTalepService).delete(hedeflerAidiyatTalep);
    }

    @Test
    void delete_shouldThrowException_whenHedeflerAidiyatTalepNotExists() {
        // Given
        when(dbHedeflerAidiyatTalepService.findById(1L)).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResponseStatusException.class, () -> hedeflerAidiyatTalepService.delete(1L));
        verify(dbHedeflerAidiyatTalepService).findById(1L);
        verify(dbHedeflerAidiyatTalepService, never()).delete(any());
    }
}
