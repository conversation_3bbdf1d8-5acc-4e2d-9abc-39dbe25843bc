package iym.makos.service.makos;

import iym.common.model.entity.iym.HtsHedeflerTalep;
import iym.common.model.entity.iym.HtsMahkemeKararTalep;
import iym.common.service.db.DbHtsHedeflerTalepService;
import iym.common.service.db.DbHtsMahkemeKararTalepService;
import iym.makos.dto.HtsHedeflerTalepDTO;
import iym.makos.mapper.HtsHedeflerTalepMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;

/**
 * Service for HtsHedeflerTalep operations
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class HtsHedeflerTalepService {

    private final DbHtsHedeflerTalepService dbHtsHedeflerTalepService;
    private final DbHtsMahkemeKararTalepService dbHtsMahkemeKararTalepService;
    private final HtsHedeflerTalepMapper htsHedeflerTalepMapper;

    /**
     * Get all HTS hedefler talep records
     * @return List of HtsHedeflerTalepDTO
     */
    @Transactional(readOnly = true)
    public List<HtsHedeflerTalepDTO> findAll() {
        List<HtsHedeflerTalep> htsHedeflerTalepList = dbHtsHedeflerTalepService.findAll();
        return htsHedeflerTalepMapper.toDtoList(htsHedeflerTalepList);
    }

    /**
     * Get all HTS hedefler talep records with pagination
     * @param pageable Pagination information
     * @return Page of HtsHedeflerTalepDTO
     */
    @Transactional(readOnly = true)
    public Page<HtsHedeflerTalepDTO> findAll(Pageable pageable) {
        Page<HtsHedeflerTalep> htsHedeflerTalepPage = dbHtsHedeflerTalepService.findAll(pageable);
        List<HtsHedeflerTalepDTO> dtoList = htsHedeflerTalepMapper.toDtoList(htsHedeflerTalepPage.getContent());
        return new PageImpl<>(dtoList, pageable, htsHedeflerTalepPage.getTotalElements());
    }

    /**
     * Get HTS hedefler talep by id
     * @param id HtsHedeflerTalep id
     * @return HtsHedeflerTalepDTO
     */
    @Transactional(readOnly = true)
    public HtsHedeflerTalepDTO findById(Long id) {
        HtsHedeflerTalep htsHedeflerTalep = dbHtsHedeflerTalepService.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "HTS hedefler talep bulunamadı: " + id));
        return htsHedeflerTalepMapper.toDto(htsHedeflerTalep);
    }

    /**
     * Get HTS hedefler talep by mahkeme karar id
     * @param mahkemeKararId Mahkeme karar id
     * @return List of HtsHedeflerTalepDTO
     */
    @Transactional(readOnly = true)
    public List<HtsHedeflerTalepDTO> findByMahkemeKararId(Long mahkemeKararId) {
        List<HtsHedeflerTalep> htsHedeflerTalepList = dbHtsHedeflerTalepService.findByMahkemeKararId(mahkemeKararId);
        return htsHedeflerTalepMapper.toDtoList(htsHedeflerTalepList);
    }

    /**
     * Create new HTS hedefler talep
     * @param htsHedeflerTalepDTO HtsHedeflerTalepDTO
     * @return Created HtsHedeflerTalepDTO
     */
    @Transactional
    public HtsHedeflerTalepDTO create(HtsHedeflerTalepDTO htsHedeflerTalepDTO) {
        // Check if mahkeme karar exists
        HtsMahkemeKararTalep htsMahkemeKararTalep = dbHtsMahkemeKararTalepService.findById(htsHedeflerTalepDTO.getMahkemeKararId())
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "HTS mahkeme karar talep bulunamadı: " + htsHedeflerTalepDTO.getMahkemeKararId()));

        // Set default durumu if not provided
        if (htsHedeflerTalepDTO.getDurumu() == null) {
            htsHedeflerTalepDTO.setDurumu("AKTIF");
        }

        HtsHedeflerTalep htsHedeflerTalep = htsHedeflerTalepMapper.toEntity(htsHedeflerTalepDTO);
        dbHtsHedeflerTalepService.save(htsHedeflerTalep);
        log.info("HTS hedefler talep oluşturuldu: {}", htsHedeflerTalep.getId());
        return htsHedeflerTalepMapper.toDto(htsHedeflerTalep);
    }



    /**
     * Delete HTS hedefler talep
     * @param id HtsHedeflerTalep id
     */
    @Transactional
    public void delete(Long id) {
        HtsHedeflerTalep htsHedeflerTalep = dbHtsHedeflerTalepService.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "HTS hedefler talep bulunamadı: " + id));
        dbHtsHedeflerTalepService.delete(htsHedeflerTalep);
        log.info("HTS hedefler talep silindi: {}", id);
    }
}
