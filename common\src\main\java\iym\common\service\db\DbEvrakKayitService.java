package iym.common.service.db;

import iym.common.model.entity.iym.EvrakKayit;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Service interface for EvrakKayit entity
 */
public interface DbEvrakKayitService extends GenericDbService<EvrakKayit, Long> {
    
    boolean existsByEvrakSiraNo(String evrakSiraNo);
    

    
    List<EvrakKayit> findByEvrakTipi(String evrakTipi);
    
    List<EvrakKayit> findByGirisTarihBetween(Date startDate, Date endDate);
    
    List<EvrakKayit> findByDurumu(String durumu);
    
    List<EvrakKayit> findByHavaleBirim(String havaleBirim);
    
    List<EvrakKayit> findByAcilmi(String acilmi);
}
