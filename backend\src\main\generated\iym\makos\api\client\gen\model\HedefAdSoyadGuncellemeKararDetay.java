/*
 * MAKOS OpenAPI definition
 * MAKOS Application
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package iym.makos.api.client.gen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import iym.makos.api.client.gen.model.HedefWithAdSoyad;
import iym.makos.api.client.gen.model.MahkemeKararDetay;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * G<PERSON>ncelleme yapılacak hedefler için mahkeme karar bilgisi ve karara ait güncellenecek ad, soyad bilgileri
 */
@JsonPropertyOrder({
  HedefAdSoyadGuncellemeKararDetay.JSON_PROPERTY_MAHKEME_KARAR_DETAY,
  HedefAdSoyadGuncellemeKararDetay.JSON_PROPERTY_HEDEF_AD_SOYAD_LISTESI
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class HedefAdSoyadGuncellemeKararDetay {
  public static final String JSON_PROPERTY_MAHKEME_KARAR_DETAY = "mahkemeKararDetay";
  private MahkemeKararDetay mahkemeKararDetay;

  public static final String JSON_PROPERTY_HEDEF_AD_SOYAD_LISTESI = "hedefAdSoyadListesi";
  private List<HedefWithAdSoyad> hedefAdSoyadListesi = new ArrayList<>();

  public HedefAdSoyadGuncellemeKararDetay() {
  }

  public HedefAdSoyadGuncellemeKararDetay mahkemeKararDetay(MahkemeKararDetay mahkemeKararDetay) {
    
    this.mahkemeKararDetay = mahkemeKararDetay;
    return this;
  }

   /**
   * Get mahkemeKararDetay
   * @return mahkemeKararDetay
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_DETAY)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public MahkemeKararDetay getMahkemeKararDetay() {
    return mahkemeKararDetay;
  }


  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_DETAY)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setMahkemeKararDetay(MahkemeKararDetay mahkemeKararDetay) {
    this.mahkemeKararDetay = mahkemeKararDetay;
  }


  public HedefAdSoyadGuncellemeKararDetay hedefAdSoyadListesi(List<HedefWithAdSoyad> hedefAdSoyadListesi) {
    
    this.hedefAdSoyadListesi = hedefAdSoyadListesi;
    return this;
  }

  public HedefAdSoyadGuncellemeKararDetay addHedefAdSoyadListesiItem(HedefWithAdSoyad hedefAdSoyadListesiItem) {
    if (this.hedefAdSoyadListesi == null) {
      this.hedefAdSoyadListesi = new ArrayList<>();
    }
    this.hedefAdSoyadListesi.add(hedefAdSoyadListesiItem);
    return this;
  }

   /**
   * Get hedefAdSoyadListesi
   * @return hedefAdSoyadListesi
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_HEDEF_AD_SOYAD_LISTESI)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public List<HedefWithAdSoyad> getHedefAdSoyadListesi() {
    return hedefAdSoyadListesi;
  }


  @JsonProperty(JSON_PROPERTY_HEDEF_AD_SOYAD_LISTESI)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setHedefAdSoyadListesi(List<HedefWithAdSoyad> hedefAdSoyadListesi) {
    this.hedefAdSoyadListesi = hedefAdSoyadListesi;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    HedefAdSoyadGuncellemeKararDetay hedefAdSoyadGuncellemeKararDetay = (HedefAdSoyadGuncellemeKararDetay) o;
    return Objects.equals(this.mahkemeKararDetay, hedefAdSoyadGuncellemeKararDetay.mahkemeKararDetay) &&
        Objects.equals(this.hedefAdSoyadListesi, hedefAdSoyadGuncellemeKararDetay.hedefAdSoyadListesi);
  }

  @Override
  public int hashCode() {
    return Objects.hash(mahkemeKararDetay, hedefAdSoyadListesi);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class HedefAdSoyadGuncellemeKararDetay {\n");
    sb.append("    mahkemeKararDetay: ").append(toIndentedString(mahkemeKararDetay)).append("\n");
    sb.append("    hedefAdSoyadListesi: ").append(toIndentedString(hedefAdSoyadListesi)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

