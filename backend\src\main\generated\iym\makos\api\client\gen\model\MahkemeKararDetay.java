/*
 * MAKOS OpenAPI definition
 * MAKOS Application
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package iym.makos.api.client.gen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * Aidiyat değ<PERSON>şik<PERSON>ği yapılacak mahkeme karar bilgileri
 */
@JsonPropertyOrder({
  MahkemeKararDetay.JSON_PROPERTY_MAHKEME_KODU,
  MahkemeKararDetay.JSON_PROPERTY_MAHKEME_IL_ILCE_KODU,
  MahkemeKararDetay.JSON_PROPERTY_MAHKEME_KARAR_NO,
  MahkemeKararDetay.JSON_PROPERTY_SORUSTURMA_NO,
  MahkemeKararDetay.JSON_PROPERTY_ACIKLAMA
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class MahkemeKararDetay {
  public static final String JSON_PROPERTY_MAHKEME_KODU = "mahkemeKodu";
  private String mahkemeKodu;

  public static final String JSON_PROPERTY_MAHKEME_IL_ILCE_KODU = "mahkemeIlIlceKodu";
  private String mahkemeIlIlceKodu;

  public static final String JSON_PROPERTY_MAHKEME_KARAR_NO = "mahkemeKararNo";
  private String mahkemeKararNo;

  public static final String JSON_PROPERTY_SORUSTURMA_NO = "sorusturmaNo";
  private String sorusturmaNo;

  public static final String JSON_PROPERTY_ACIKLAMA = "aciklama";
  private String aciklama;

  public MahkemeKararDetay() {
  }

  public MahkemeKararDetay mahkemeKodu(String mahkemeKodu) {
    
    this.mahkemeKodu = mahkemeKodu;
    return this;
  }

   /**
   * Get mahkemeKodu
   * @return mahkemeKodu
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_MAHKEME_KODU)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getMahkemeKodu() {
    return mahkemeKodu;
  }


  @JsonProperty(JSON_PROPERTY_MAHKEME_KODU)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setMahkemeKodu(String mahkemeKodu) {
    this.mahkemeKodu = mahkemeKodu;
  }


  public MahkemeKararDetay mahkemeIlIlceKodu(String mahkemeIlIlceKodu) {
    
    this.mahkemeIlIlceKodu = mahkemeIlIlceKodu;
    return this;
  }

   /**
   * Get mahkemeIlIlceKodu
   * @return mahkemeIlIlceKodu
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_MAHKEME_IL_ILCE_KODU)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getMahkemeIlIlceKodu() {
    return mahkemeIlIlceKodu;
  }


  @JsonProperty(JSON_PROPERTY_MAHKEME_IL_ILCE_KODU)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setMahkemeIlIlceKodu(String mahkemeIlIlceKodu) {
    this.mahkemeIlIlceKodu = mahkemeIlIlceKodu;
  }


  public MahkemeKararDetay mahkemeKararNo(String mahkemeKararNo) {
    
    this.mahkemeKararNo = mahkemeKararNo;
    return this;
  }

   /**
   * Get mahkemeKararNo
   * @return mahkemeKararNo
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_NO)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getMahkemeKararNo() {
    return mahkemeKararNo;
  }


  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_NO)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMahkemeKararNo(String mahkemeKararNo) {
    this.mahkemeKararNo = mahkemeKararNo;
  }


  public MahkemeKararDetay sorusturmaNo(String sorusturmaNo) {
    
    this.sorusturmaNo = sorusturmaNo;
    return this;
  }

   /**
   * Get sorusturmaNo
   * @return sorusturmaNo
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SORUSTURMA_NO)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getSorusturmaNo() {
    return sorusturmaNo;
  }


  @JsonProperty(JSON_PROPERTY_SORUSTURMA_NO)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSorusturmaNo(String sorusturmaNo) {
    this.sorusturmaNo = sorusturmaNo;
  }


  public MahkemeKararDetay aciklama(String aciklama) {
    
    this.aciklama = aciklama;
    return this;
  }

   /**
   * Get aciklama
   * @return aciklama
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ACIKLAMA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getAciklama() {
    return aciklama;
  }


  @JsonProperty(JSON_PROPERTY_ACIKLAMA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAciklama(String aciklama) {
    this.aciklama = aciklama;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    MahkemeKararDetay mahkemeKararDetay = (MahkemeKararDetay) o;
    return Objects.equals(this.mahkemeKodu, mahkemeKararDetay.mahkemeKodu) &&
        Objects.equals(this.mahkemeIlIlceKodu, mahkemeKararDetay.mahkemeIlIlceKodu) &&
        Objects.equals(this.mahkemeKararNo, mahkemeKararDetay.mahkemeKararNo) &&
        Objects.equals(this.sorusturmaNo, mahkemeKararDetay.sorusturmaNo) &&
        Objects.equals(this.aciklama, mahkemeKararDetay.aciklama);
  }

  @Override
  public int hashCode() {
    return Objects.hash(mahkemeKodu, mahkemeIlIlceKodu, mahkemeKararNo, sorusturmaNo, aciklama);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MahkemeKararDetay {\n");
    sb.append("    mahkemeKodu: ").append(toIndentedString(mahkemeKodu)).append("\n");
    sb.append("    mahkemeIlIlceKodu: ").append(toIndentedString(mahkemeIlIlceKodu)).append("\n");
    sb.append("    mahkemeKararNo: ").append(toIndentedString(mahkemeKararNo)).append("\n");
    sb.append("    sorusturmaNo: ").append(toIndentedString(sorusturmaNo)).append("\n");
    sb.append("    aciklama: ").append(toIndentedString(aciklama)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

