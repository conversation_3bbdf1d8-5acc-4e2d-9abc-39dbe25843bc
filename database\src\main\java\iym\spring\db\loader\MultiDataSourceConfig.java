package iym.spring.db.loader;

import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;
import jakarta.persistence.EntityManagerFactory;
import java.util.HashMap;
import java.util.Map;

/**
 * Multi-DataSource Configuration for Oracle (primary) and PostgreSQL (secondary)
 * This configuration allows the application to work with both databases simultaneously
 * Only activated when PostgreSQL properties are present
 */
@Configuration
@EnableTransactionManagement
@EnableJpaAuditing
@ComponentScan({"iym.db", "iym.common.service", "iym.db.jpa.service"})
@Slf4j
public class MultiDataSourceConfig {

    /**
     * Primary DataSource - Oracle
     * This will be used as the default datasource for existing functionality
     */
    @Primary
    @Bean(name = "oracleDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.oracle")
    public DataSource oracleDataSource() {
        log.info("Configuring Oracle DataSource (Primary)");
        return DataSourceBuilder.create()
                .type(HikariDataSource.class)
                .build();
    }

    /**
     * Secondary DataSource - PostgreSQL
     * This will be used for new PostgreSQL functionality
     */
    @Bean(name = "postgresqlDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.postgresql")
    @ConditionalOnProperty(prefix = "spring.datasource.postgresql", name = "url")
    public DataSource postgresqlDataSource() {
        log.info("Configuring PostgreSQL DataSource (Secondary)");
        return DataSourceBuilder.create()
                .type(HikariDataSource.class)
                .build();
    }

    /**
     * Primary EntityManagerFactory - Oracle
     */
    @Primary
    @Bean(name = "oracleEntityManagerFactory")
    public LocalContainerEntityManagerFactoryBean oracleEntityManagerFactory(
            @Qualifier("oracleDataSource") DataSource dataSource) {
        
        log.info("Configuring Oracle EntityManagerFactory");
        
        LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
        em.setDataSource(dataSource);
        em.setPackagesToScan("iym.common.model.entity.iym", "iym.common.model.entity.makos");
        
        HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
        em.setJpaVendorAdapter(vendorAdapter);
        
        Map<String, Object> properties = new HashMap<>();
        properties.put("hibernate.dialect", "org.hibernate.dialect.Oracle12cDialect");
        properties.put("hibernate.hbm2ddl.auto", "none");
        properties.put("hibernate.show_sql", "true");
        properties.put("hibernate.format_sql", "true");
        properties.put("hibernate.default_schema", "iym");
        properties.put("hibernate.physical_naming_strategy", "org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl");
        em.setJpaPropertyMap(properties);
        
        return em;
    }

    /**
     * Secondary EntityManagerFactory - PostgreSQL
     */
    @Bean(name = "postgresqlEntityManagerFactory")
    @ConditionalOnProperty(prefix = "spring.datasource.postgresql", name = "url")
    public LocalContainerEntityManagerFactoryBean postgresqlEntityManagerFactory(
            @Qualifier("postgresqlDataSource") DataSource dataSource) {
        
        log.info("Configuring PostgreSQL EntityManagerFactory");
        
        LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
        em.setDataSource(dataSource);
        em.setPackagesToScan("iym.common.model.entity.postgresql");
        
        HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
        em.setJpaVendorAdapter(vendorAdapter);
        
        Map<String, Object> properties = new HashMap<>();
        properties.put("hibernate.dialect", "org.hibernate.dialect.PostgreSQLDialect");
        properties.put("hibernate.hbm2ddl.auto", "none");
        properties.put("hibernate.show_sql", "true");
        properties.put("hibernate.format_sql", "true");
        properties.put("hibernate.default_schema", "public");
        properties.put("hibernate.physical_naming_strategy", "org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl");
        em.setJpaPropertyMap(properties);
        
        return em;
    }

    /**
     * Primary TransactionManager - Oracle
     */
    @Primary
    @Bean(name = "oracleTransactionManager")
    public PlatformTransactionManager oracleTransactionManager(
            @Qualifier("oracleEntityManagerFactory") EntityManagerFactory entityManagerFactory) {

        log.info("Configuring Oracle TransactionManager");
        JpaTransactionManager transactionManager = new JpaTransactionManager(entityManagerFactory);
        transactionManager.setDataSource(oracleDataSource());
        return transactionManager;
    }

    /**
     * Secondary TransactionManager - PostgreSQL
     */
    @Bean(name = "postgresqlTransactionManager")
    public PlatformTransactionManager postgresqlTransactionManager(
            @Qualifier("postgresqlEntityManagerFactory") EntityManagerFactory entityManagerFactory) {

        log.info("Configuring PostgreSQL TransactionManager");
        JpaTransactionManager transactionManager = new JpaTransactionManager(entityManagerFactory);
        transactionManager.setDataSource(postgresqlDataSource());
        return transactionManager;
    }

    /**
     * Oracle JPA Repositories Configuration
     */
    @Configuration
    @EnableJpaRepositories(
            basePackages = "iym.db.jpa.dao",
            entityManagerFactoryRef = "oracleEntityManagerFactory",
            transactionManagerRef = "oracleTransactionManager"
    )
    static class OracleJpaRepositoriesConfig {
        // Oracle repositories configuration
    }

    /**
     * PostgreSQL JPA Repositories Configuration
     */
    @Configuration
    @EnableJpaRepositories(
            basePackages = "iym.db.postgresql.dao",
            entityManagerFactoryRef = "postgresqlEntityManagerFactory",
            transactionManagerRef = "postgresqlTransactionManager"
    )
    static class PostgreSQLJpaRepositoriesConfig {
        // PostgreSQL repositories configuration
    }
}
