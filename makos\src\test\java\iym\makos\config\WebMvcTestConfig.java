package iym.makos.config;

import iym.common.service.db.DbEvrakKayitService;
import iym.common.service.db.DbIllerService;
import iym.common.service.db.DbMahkemeAidiyatTalepService;
import iym.common.service.db.DbMahkemeKararTalepService;
import org.mockito.Mockito;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;

/**
 * Test configuration specifically for WebMvcTest
 * This configuration excludes database components and provides mock beans
 */
@TestConfiguration
@Profile("webmvc-test")
@EnableAutoConfiguration(exclude = {
    DataSourceAutoConfiguration.class,
    DataSourceTransactionManagerAutoConfiguration.class,
    HibernateJpaAutoConfiguration.class
})
@ComponentScan(basePackages = {"iym.makos.controller"}, excludeFilters = {
    @ComponentScan.Filter(pattern = "iym.spring.db.*"),
    @ComponentScan.Filter(pattern = "iym.db.*")
})
public class WebMvcTestConfig {
    
    /**
     * Create a mock DbEvrakKayitService for tests
     */
    @Bean
    @Primary
    public DbEvrakKayitService dbEvrakKayitService() {
        return Mockito.mock(DbEvrakKayitService.class);
    }
    
    /**
     * Create a mock DbMahkemeKararTalepService for tests
     */
    @Bean
    @Primary
    public DbMahkemeKararTalepService dbMahkemeKararTalepService() {
        return Mockito.mock(DbMahkemeKararTalepService.class);
    }
    
    /**
     * Create a mock DbMahkemeAidiyatTalepService for tests
     */
    @Bean
    @Primary
    public DbMahkemeAidiyatTalepService dbMahkemeAidiyatTalepService() {
        return Mockito.mock(DbMahkemeAidiyatTalepService.class);
    }
    
    /**
     * Create a mock DbIllerService for tests
     */
    @Bean
    @Primary
    public DbIllerService dbIllerService() {
        return Mockito.mock(DbIllerService.class);
    }
}
