package iym.common.service.db;

import iym.common.model.entity.iym.MahkemeHedeflerAidiyatTalep;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Service interface for MahkemeHedeflerAidiyatTalep entity
 */
public interface DbMahkemeHedeflerAidiyatTalepService extends GenericDbService<MahkemeHedeflerAidiyatTalep, Long> {

    List<MahkemeHedeflerAidiyatTalep> findByHedefId(Long hedefId);
    
    List<MahkemeHedeflerAidiyatTalep> findByMahkemeKararId(Long mahkemeKararId);
    
    List<MahkemeHedeflerAidiyatTalep> findByAidiyatKod(String aidiyatKod);
    
    List<MahkemeHedeflerAidiyatTalep> findByDurumu(String durumu);
    
    List<MahkemeHedeflerAidiyatTalep> findByKullaniciId(Long kullaniciId);
    
    List<MahkemeHedeflerAidiyatTalep> findByTarihBetween(Date startDate, Date endDate);
    
    Optional<MahkemeHedeflerAidiyatTalep> findByHedefIdAndAidiyatKodAndMahkemeKararId(
            Long hedefId, 
            String aidiyatKod, 
            Long mahkemeKararId);
    
    List<MahkemeHedeflerAidiyatTalep> findByHedefIdAndMahkemeKararId(Long hedefId, Long mahkemeKararId);
    
    List<MahkemeHedeflerAidiyatTalep> findByHedefIdAndAidiyatKod(Long hedefId, String aidiyatKod);
    
    List<MahkemeHedeflerAidiyatTalep> findByMahkemeKararIdAndAidiyatKod(Long mahkemeKararId, String aidiyatKod);
}
