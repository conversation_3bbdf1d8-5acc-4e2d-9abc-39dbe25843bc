package iym.makos.model.api;

import iym.common.model.api.EvrakKurum;
import iym.common.model.api.EvrakTuru;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

import java.time.LocalDateTime;

@Data
@Builder
@Jacksonized
@ToString
@EqualsAndHashCode
public class EvrakDetay {

  @NotNull
  @Size(max = 50, message = "Evrak No 50 karakterden fazla olamaz")
  private String evrakNo;

  @NotNull
  private LocalDateTime evrakTarihi;

  @NotNull
  private String evrakKurumKodu;

  @NotNull
  private EvrakTuru evrakTuru;

  private String havaleBirimi;

  private String aciklama;

  @NotNull
  private String geldigiIlIlceKodu;

  private boolean acilmi;

  private String evrakKonusu;

}

