-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;

-- Create sequence for EVRAK_SIRANO if it doesn't exist
DECLARE
  seq_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO seq_exists FROM user_sequences WHERE sequence_name = 'EVRAK_SIRANO_SEQ';
  IF seq_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE SEQUENCE iym.EVRAK_SIRANO_SEQ
      START WITH 1
      INCREMENT BY 1
      NOCACHE
      NOCYCLE';
  END IF;
END;
/

-- Create EVRAK_SIRANO table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM USER_TABLES WHERE table_name = 'EVRAK_SIRANO';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.EVRAK_SIRANO (
      ID NUMBER NOT NULL,
      YIL NUMBER NOT NULL,
      SIRA_NO NUMBER NOT NULL,
      CONSTRAINT EVRAK_SIRANO_ID_IDX PRIMARY KEY (YIL, SIRA_NO) ENABLE
    )';

  END IF;
END;
/

-- Insert sample data if table is empty
DECLARE
  row_count NUMBER;
BEGIN
  SELECT COUNT(*) INTO row_count FROM iym.EVRAK_SIRANO;
  IF row_count = 0 THEN
    -- Make sure we have evrak and users in the respective tables
    DECLARE
      evrak_count NUMBER;
      user_count NUMBER;
    BEGIN
          INSERT INTO iym.EVRAK_SIRANO (ID, YIL, SIRA_NO) VALUES (1, 2025, 1);
    END;
  END IF;
END;
/

COMMIT;
