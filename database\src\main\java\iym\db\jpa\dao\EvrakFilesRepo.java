package iym.db.jpa.dao;

import iym.common.model.entity.iym.EvrakFiles;
import iym.common.model.entity.iym.Hedefler;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for HedeflerTalep entity
 */
@Repository
public interface EvrakFilesRepo extends JpaRepository<EvrakFiles, Long> {

    List<EvrakFiles> findByEvrakId(Long evrakId);

}
