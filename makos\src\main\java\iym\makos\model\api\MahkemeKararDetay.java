package iym.makos.model.api;

import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
@ToString
@EqualsAndHashCode
public class MahkemeKararDetay {

  @NotNull
  private String mahkemeKodu;

  @NotNull
  private String mahkemeIlIlceKodu;

  private String mahkemeKararNo;

  private String sorusturmaNo;

  private String aciklama;

}

