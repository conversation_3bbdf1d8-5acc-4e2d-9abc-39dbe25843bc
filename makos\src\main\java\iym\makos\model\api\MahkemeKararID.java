package iym.makos.model.api;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

import java.util.List;

@Data
@Builder
@Jacksonized
@ToString
@EqualsAndHashCode
public class MahkemeKararID {

  @NotNull
  @Valid
  @Schema(description = "Mahkeme karar bilgileri")
  private MahkemeKararBilgisi mahkemeKararBilgisi;

  private List<String> aidiyatKodlari;

  private List<String> sucTipiKodlari;

}

