package iym.makos.service;

import iym.common.model.entity.iym.HtsMahkemeKararTalep;
import iym.common.model.entity.iym.HtsHedeflerTalep;
import iym.common.service.db.DbHtsMahkemeKararTalepService;
import iym.common.service.db.DbHtsHedeflerTalepService;
import iym.makos.mapper.HtsHedeflerTalepMapper;
import iym.makos.service.makos.HtsHedeflerTalepService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.web.server.ResponseStatusException;
import iym.makos.dto.HtsHedeflerTalepDTO;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class HtsHedeflerTalepServiceTest {

    @Mock
    private DbHtsHedeflerTalepService dbHtsHedeflerTalepService;

    @Mock
    private DbHtsMahkemeKararTalepService dbHtsMahkemeKararTalepService;

    @Mock
    private HtsHedeflerTalepMapper htsHedeflerTalepMapper;

    @InjectMocks
    private HtsHedeflerTalepService htsHedeflerTalepService;

    private HtsHedeflerTalep htsHedeflerTalep;
    private HtsHedeflerTalepDTO htsHedeflerTalepDTO;
    private HtsMahkemeKararTalep htsMahkemeKararTalep;
    private List<HtsHedeflerTalep> htsHedeflerTalepList;
    private List<HtsHedeflerTalepDTO> htsHedeflerTalepDTOList;
    private Date testDate;

    @BeforeEach
    void setUp() {
        testDate = new Date();

        htsMahkemeKararTalep = HtsMahkemeKararTalep.builder()
                .id(100L)
                .build();

        htsHedeflerTalep = HtsHedeflerTalep.builder()
                .id(1L)
                .mahkemeKararId(100L)
                .hedefNo("5551234567")
                .karsiHedefNo("5559876543")
                .sorguTipi("ARAMA")
                .baslangicTarihi(testDate)
                .bitisTarihi(testDate)
                .tespitTuru("DETAYLI")
                .kullaniciId("1")
                .durumu("AKTIF")
                .build();

        HtsHedeflerTalep htsHedeflerTalep2 = HtsHedeflerTalep.builder()
                .id(2L)
                .mahkemeKararId(101L)
                .hedefNo("5551234568")
                .karsiHedefNo("5559876544")
                .sorguTipi("SMS")
                .baslangicTarihi(testDate)
                .bitisTarihi(testDate)
                .tespitTuru("ÖZET")
                .kullaniciId("2")
                .durumu("AKTIF")
                .build();

        htsHedeflerTalepDTO = HtsHedeflerTalepDTO.builder()
                .id(1L)
                .mahkemeKararId(100L)
                .hedefNo("5551234567")
                .karsiHedefNo("5559876543")
                .sorguTipi("ARAMA")
                .baslangicTarihi(testDate)
                .bitisTarihi(testDate)
                .tespitTuru("DETAYLI")
                .kullaniciId("1")
                .durumu("AKTIF")
                .build();

        HtsHedeflerTalepDTO htsHedeflerTalepDTO2 = HtsHedeflerTalepDTO.builder()
                .id(2L)
                .mahkemeKararId(101L)
                .hedefNo("5551234568")
                .karsiHedefNo("5559876544")
                .sorguTipi("SMS")
                .baslangicTarihi(testDate)
                .bitisTarihi(testDate)
                .tespitTuru("ÖZET")
                .kullaniciId("2")
                .durumu("AKTIF")
                .build();

        htsHedeflerTalepList = Arrays.asList(htsHedeflerTalep, htsHedeflerTalep2);
        htsHedeflerTalepDTOList = Arrays.asList(htsHedeflerTalepDTO, htsHedeflerTalepDTO2);
    }

    @Test
    void findAll_shouldReturnAllHtsHedeflerTalep() {
        // Given
        when(dbHtsHedeflerTalepService.findAll()).thenReturn(htsHedeflerTalepList);
        when(htsHedeflerTalepMapper.toDtoList(htsHedeflerTalepList)).thenReturn(htsHedeflerTalepDTOList);

        // When
        List<HtsHedeflerTalepDTO> result = htsHedeflerTalepService.findAll();

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(2);
        assertThat(result).isEqualTo(htsHedeflerTalepDTOList);
        verify(dbHtsHedeflerTalepService).findAll();
        verify(htsHedeflerTalepMapper).toDtoList(htsHedeflerTalepList);
    }

    @Test
    void findAll_withPageable_shouldReturnPageOfHtsHedeflerTalep() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);
        Page<HtsHedeflerTalep> htsHedeflerTalepPage = new PageImpl<>(htsHedeflerTalepList, pageable, htsHedeflerTalepList.size());
        
        when(dbHtsHedeflerTalepService.findAll(pageable)).thenReturn(htsHedeflerTalepPage);
        when(htsHedeflerTalepMapper.toDtoList(htsHedeflerTalepList)).thenReturn(htsHedeflerTalepDTOList);

        // When
        Page<HtsHedeflerTalepDTO> result = htsHedeflerTalepService.findAll(pageable);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getContent()).isEqualTo(htsHedeflerTalepDTOList);
        assertThat(result.getTotalElements()).isEqualTo(2);
        verify(dbHtsHedeflerTalepService).findAll(pageable);
        verify(htsHedeflerTalepMapper).toDtoList(htsHedeflerTalepList);
    }

    @Test
    void findById_shouldReturnHtsHedeflerTalep_whenExists() {
        // Given
        when(dbHtsHedeflerTalepService.findById(1L)).thenReturn(Optional.of(htsHedeflerTalep));
        when(htsHedeflerTalepMapper.toDto(htsHedeflerTalep)).thenReturn(htsHedeflerTalepDTO);

        // When
        HtsHedeflerTalepDTO result = htsHedeflerTalepService.findById(1L);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEqualTo(htsHedeflerTalepDTO);
        verify(dbHtsHedeflerTalepService).findById(1L);
        verify(htsHedeflerTalepMapper).toDto(htsHedeflerTalep);
    }

    @Test
    void findById_shouldThrowException_whenNotExists() {
        // Given
        when(dbHtsHedeflerTalepService.findById(1L)).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResponseStatusException.class, () -> htsHedeflerTalepService.findById(1L));
        verify(dbHtsHedeflerTalepService).findById(1L);
        verify(htsHedeflerTalepMapper, never()).toDto(any());
    }

    @Test
    void findByMahkemeKararId_shouldReturnHtsHedeflerTalepList() {
        // Given
        when(dbHtsHedeflerTalepService.findByMahkemeKararId(100L)).thenReturn(Arrays.asList(htsHedeflerTalep));
        when(htsHedeflerTalepMapper.toDtoList(Arrays.asList(htsHedeflerTalep))).thenReturn(Arrays.asList(htsHedeflerTalepDTO));

        // When
        List<HtsHedeflerTalepDTO> result = htsHedeflerTalepService.findByMahkemeKararId(100L);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(1);
        assertThat(result.get(0)).isEqualTo(htsHedeflerTalepDTO);
        verify(dbHtsHedeflerTalepService).findByMahkemeKararId(100L);
        verify(htsHedeflerTalepMapper).toDtoList(Arrays.asList(htsHedeflerTalep));
    }

    @Test
    void create_shouldCreateHtsHedeflerTalep() {
        // Given
        when(dbHtsMahkemeKararTalepService.findById(100L)).thenReturn(Optional.of(htsMahkemeKararTalep));
        when(htsHedeflerTalepMapper.toEntity(htsHedeflerTalepDTO)).thenReturn(htsHedeflerTalep);
        when(htsHedeflerTalepMapper.toDto(htsHedeflerTalep)).thenReturn(htsHedeflerTalepDTO);

        // When
        HtsHedeflerTalepDTO result = htsHedeflerTalepService.create(htsHedeflerTalepDTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEqualTo(htsHedeflerTalepDTO);
        verify(dbHtsMahkemeKararTalepService).findById(100L);
        verify(htsHedeflerTalepMapper).toEntity(htsHedeflerTalepDTO);
        verify(dbHtsHedeflerTalepService).save(htsHedeflerTalep);
        verify(htsHedeflerTalepMapper).toDto(htsHedeflerTalep);
    }

    @Test
    void create_shouldThrowException_whenMahkemeKararNotExists() {
        // Given
        when(dbHtsMahkemeKararTalepService.findById(100L)).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResponseStatusException.class, () -> htsHedeflerTalepService.create(htsHedeflerTalepDTO));
        verify(dbHtsMahkemeKararTalepService).findById(100L);
        verify(htsHedeflerTalepMapper, never()).toEntity(any());
        verify(dbHtsHedeflerTalepService, never()).save(any());
        verify(htsHedeflerTalepMapper, never()).toDto(any());
    }



    @Test
    void delete_shouldDeleteHtsHedeflerTalep() {
        // Given
        when(dbHtsHedeflerTalepService.findById(1L)).thenReturn(Optional.of(htsHedeflerTalep));

        // When
        htsHedeflerTalepService.delete(1L);

        // Then
        verify(dbHtsHedeflerTalepService).findById(1L);
        verify(dbHtsHedeflerTalepService).delete(htsHedeflerTalep);
    }

    @Test
    void delete_shouldThrowException_whenHtsHedeflerTalepNotExists() {
        // Given
        when(dbHtsHedeflerTalepService.findById(1L)).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResponseStatusException.class, () -> htsHedeflerTalepService.delete(1L));
        verify(dbHtsHedeflerTalepService).findById(1L);
        verify(dbHtsHedeflerTalepService, never()).delete(any());
    }
}
