package iym.makos.controller;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import iym.common.service.db.DbIllerService;
import iym.common.util.CommonUtils;
import iym.makos.config.security.UserDetailsImpl;
import iym.makos.dto.id.*;
import iym.makos.dto.it.ITKararRequest;
import iym.makos.dto.it.ITKararResponse;
import iym.makos.model.MakosApiResponse;
import iym.makos.model.MakosResponseCode;
import iym.makos.processor.IMakosRequestProcessor;
import iym.makos.processor.ProcessorFactory;
import iym.makos.service.makos.MahkemeKararTalepService;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/mahkemeKarar")
@Slf4j
public class MahkemeKararController {

    @Autowired
    private DbIllerService illerService;

    @Autowired
    private MahkemeKararTalepService mahkemeKararTalepService;

    @Autowired
    private ProcessorFactory processorFactory;

    /*
     * curl -i -X 'POST' -H 'Authorization: Basic YWRtaW46cGFzc3dvcmRk' \
     * 	-F 'file=@D:/test/deneme.txt;type=multipart/form-data' \
     * 	-F 'mahkemeKararDosyasi={"owner": "owner", "fileName":"fileName"};type=application/json' \
     * 	'http://localhost:8080/api/yeniKararID'
     *
     * @param file
     * @param fileDetails
     * @return
     */
    @PostMapping(path = "/yeniKararID", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<IDYeniKararResponse> yeniKararID(
            //@Parameter(required = true, description = "Yuklenecek dosya")
            @NotNull @RequestPart(value = "mahkemeKararDosyasiID") MultipartFile mahkemeKararDosyasiID,

            @Parameter(required = true, description = "ID Mahkeme Karar Detaylari", schema = @Schema(implementation = IDYeniKararRequest.class, type = "string"))
            @Valid @RequestPart(value = "jsonData") IDYeniKararRequest request,
            Authentication authentication
    ) {

        try {
            UserDetailsImpl user = (UserDetailsImpl) authentication.getPrincipal();

            log.info("IDYeniKararRequest received:{}, user:{}", request, user.getUsername());

            IMakosRequestProcessor<IDYeniKararRequest, IDYeniKararResponse> processor = processorFactory.getProcessor(IDYeniKararRequest.class, IDYeniKararResponse.class);
            IDYeniKararResponse response = processor.process(request, user);

            String fullPath = CommonUtils.dosyayiGeciciKlasoreKaydet(mahkemeKararDosyasiID.getOriginalFilename(), mahkemeKararDosyasiID.getBytes());

            log.info("IDYeniKararRequest processed, id:{}, user:{}, response:{}", request.getId(), user.getUsername(), response);
            return ResponseEntity.status(MakosResponseCode.toHttpStatus(response.getResponse().getResponseCode()))
                    .body(response);

            /*
            String fullPath = CommonUtils.dosyayiGeciciKlasoreKaydet(mahkemeKararDosyasiID.getOriginalFilename(), mahkemeKararDosyasiID.getBytes());

            // TODO kullaniciId ???
            ApiResponse saveResult = mahkemeKararTalepService.saveMahkemeKararRequest(request, user.getId(), mahkemeKararDosyasiID.getOriginalFilename());

            ResponseCode responseCode = saveResult.getResponseCode();

            Map<String, String> map = new HashMap<>();
            //map.put("ID request", JsonUtils.prettyPrintFromJsonObject(request));
            map.put("fileName", mahkemeKararDosyasiID.getName());
            map.put("originalFilename", mahkemeKararDosyasiID.getOriginalFilename());
            map.put("fileSize", mahkemeKararDosyasiID.getSize() + " bytes");
            map.put("fileContentType", mahkemeKararDosyasiID.getContentType());
            map.put("fileContent", new String(mahkemeKararDosyasiID.getBytes()));

            // TODO Upload File and save information to db
            IDUzatmaKarariResponse response = IDUzatmaKarariResponse.builder()
                    .response(ApiResponse.builder()
                            .responseCode(responseCode)
                            .responseMessage(map.toString())
                            .build())
                    .build();
            return ResponseEntity.ok(response);
             */

        } catch (Exception e) {
            log.error("IDYeniKararRequest failed. id:{}", request.getId(), e);
            IDYeniKararResponse response = IDYeniKararResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Internal Error")
                            .build())
                    .build();
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }

    }

    /*
     * curl -i -X 'POST' -H 'Authorization: Basic YWRtaW46cGFzc3dvcmRk' \
     * 	-F 'file=@D:/test/deneme.txt;type=multipart/form-data' \
     * 	-F 'mahkemeKararDosyasi={"owner": "owner", "fileName":"fileName"};type=application/json' \
     * 	'http://localhost:8080/api/uzatmaKarariID'
     *
     * @param file
     * @param fileDetails
     * @return
     */
    @PostMapping(path = "/uzatmaKarariID", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<IDUzatmaKarariResponse> uzatmaKarariID(
            //@Parameter(required = true, description = "Yuklenecek dosya")
            @NotNull @RequestPart(value = "mahkemeKararDosyasiID") MultipartFile mahkemeKararDosyasiID,

            @Parameter(required = true, description = "ID Mahkeme Karar Detaylari", schema = @Schema(implementation = IDUzatmaKarariRequest.class, type = "string"))
            @Valid @RequestPart(value = "mahkemeKararDetayID") IDUzatmaKarariRequest request,
            Authentication authentication
    ) {
        try {
            UserDetailsImpl user = (UserDetailsImpl) authentication.getPrincipal();

            log.info("IDUzatmaKarariRequest received:{}, user:{}", request, user.getUsername());

            IMakosRequestProcessor<IDUzatmaKarariRequest, IDUzatmaKarariResponse> processor = processorFactory.getProcessor(IDUzatmaKarariRequest.class, IDUzatmaKarariResponse.class);
            IDUzatmaKarariResponse response = processor.process(request, user);

            log.info("IDUzatmaKarariRequest processed, id:{}, user:{}, response:{}", request.getId(), user.getUsername(), response);
            return ResponseEntity.status(MakosResponseCode.toHttpStatus(response.getResponse().getResponseCode()))
                    .body(response);

        } catch (Exception e) {
            log.error("IDYeniKararRequest failed. id:{}", request.getId(), e);
            IDUzatmaKarariResponse response = IDUzatmaKarariResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Internal Error")
                            .build())
                    .build();
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /*
     * curl -i -X 'POST' -H 'Authorization: Basic YWRtaW46cGFzc3dvcmRk' \
     * 	-F 'file=@D:/test/deneme.txt;type=multipart/form-data' \
     * 	-F 'mahkemeKararDosyasi={"owner": "owner", "fileName":"fileName"};type=application/json' \
     * 	'http://localhost:8080/api/sonlandirmaKarariID'
     *
     * @param file
     * @param fileDetails
     * @return
     */
    @PostMapping(path = "/sonlandirmaKarariID", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<IDSonlandirmaKarariResponse> sonlandirmaKarariID(
            //@Parameter(required = true, description = "Yuklenecek dosya")
            @NotNull @RequestPart(value = "mahkemeKararDosyasiID") MultipartFile mahkemeKararDosyasiID,

            @Parameter(required = true, description = "ID Mahkeme Karar Detaylari", schema = @Schema(implementation = IDSonlandirmaKarariRequest.class, type = "string"))
            @Valid @RequestPart(value = "mahkemeKararDetayID") IDSonlandirmaKarariRequest request,
            Authentication authentication
    ) {
        try {
            UserDetailsImpl user = (UserDetailsImpl) authentication.getPrincipal();

            log.info("IDSonlandirmaKarariRequest received:{}, user:{}", request, user.getUsername());

            IMakosRequestProcessor<IDSonlandirmaKarariRequest, IDSonlandirmaKarariResponse> processor = processorFactory.getProcessor(IDSonlandirmaKarariRequest.class, IDSonlandirmaKarariResponse.class);
            IDSonlandirmaKarariResponse response = processor.process(request, user);

            log.info("IDSonlandirmaKarariRequest processed, id:{}, user:{}, response:{}", request.getId(), user.getUsername(), response);
            return ResponseEntity.status(MakosResponseCode.toHttpStatus(response.getResponse().getResponseCode()))
                    .body(response);

        } catch (Exception e) {
            log.error("IDSonlandirmaKarariRequest failed. id:{}", request.getId(), e);
            IDSonlandirmaKarariResponse response = IDSonlandirmaKarariResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Internal Error")
                            .build())
                    .build();
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /*
     * curl -i -X 'POST' -H 'Authorization: Basic YWRtaW46cGFzc3dvcmRk' \
     * 	-F 'file=@D:/test/deneme.txt;type=multipart/form-data' \
     * 	-F 'mahkemeKararDosyasi={"owner": "owner", "fileName":"fileName"};type=application/json' \
     * 	'http://localhost:8080/api/kararGonderID'
     *
     * @param file
     * @param fileDetails
     * @return
     */
    @PostMapping(path = "/kararGonderIT", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<ITKararResponse> kararGonderIT(

            @NotNull @RequestPart(value = "mahkemeKararDosyasiIT") MultipartFile mahkemeKararDosyasiIT,

            @Parameter(required = true, description = "IT Mahkeme Karar Detaylari", schema = @Schema(implementation = ITKararRequest.class, type = "string"))
            @Valid @RequestPart(value = "mahkemeKararDetayIT") ITKararRequest request,
            Authentication authentication
    ) {

        try {
            UserDetailsImpl user = (UserDetailsImpl) authentication.getPrincipal();

            log.info("ITKararRequest received:{}, user:{}", request, user.getUsername());

            IMakosRequestProcessor<ITKararRequest, ITKararResponse> processor = processorFactory.getProcessor(ITKararRequest.class, ITKararResponse.class);
            ITKararResponse response = processor.process(request, user);

            log.info("ITKararRequest processed, id:{}, user:{}, response:{}", request.getId(), user.getUsername(), response);
            return ResponseEntity.status(MakosResponseCode.toHttpStatus(response.getResponse().getResponseCode()))
                    .body(response);

        } catch (Exception e) {
            log.error("ITKararRequest failed. id:{}", request.getId(), e);
            ITKararResponse response = ITKararResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Internal Error")
                            .build())
                    .build();
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }

    }

    @PostMapping(path = "/aidiyatBilgisiGuncelle", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<IDAidiyatBilgisiGuncellemeResponse> aidiyatBilgisiGuncelle(

            @NotNull @RequestPart(value = "mahkemeKararDosyasi") MultipartFile mahkemeKararDosyasiID,

            @Parameter(required = true, description = "Mahkeme Karar Detaylari", schema = @Schema(implementation = IDAidiyatBilgisiGuncellemeRequest.class, type = "string"))
            @Valid @RequestPart(value = "mahkemeKararDetay") IDAidiyatBilgisiGuncellemeRequest request,
            Authentication authentication
    ) {

        try {
            UserDetailsImpl user = (UserDetailsImpl) authentication.getPrincipal();

            log.info("IDAidiyatBilgisiGuncellemeRequest received:{}, user:{}", request, user.getUsername());

            IMakosRequestProcessor<IDAidiyatBilgisiGuncellemeRequest, IDAidiyatBilgisiGuncellemeResponse> processor = processorFactory.getProcessor(IDAidiyatBilgisiGuncellemeRequest.class, IDAidiyatBilgisiGuncellemeResponse.class);
            IDAidiyatBilgisiGuncellemeResponse response = processor.process(request, user);

            log.info("IDAidiyatBilgisiGuncellemeRequest processed, id:{}, user:{}, response:{}", request.getId(), user.getUsername(), response);
            return ResponseEntity.status(MakosResponseCode.toHttpStatus(response.getResponse().getResponseCode()))
                    .body(response);

        } catch (Exception e) {
            log.error("IDAidiyatBilgisiGuncellemeRequest failed. id:{}", request.getId(), e);
            IDAidiyatBilgisiGuncellemeResponse response = IDAidiyatBilgisiGuncellemeResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Internal Error")
                            .build())
                    .build();
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping(path = "/hedefAdSoyadGuncelle", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<IDHedefAdSoyadGuncellemeResponse> hedefAdSoyadGuncelle(

            @NotNull @RequestPart(value = "mahkemeKararDosyasi") MultipartFile mahkemeKararDosyasiID,

            @Parameter(required = true, description = "Mahkeme Karar Detaylari", schema = @Schema(implementation = IDHedefAdSoyadGuncellemeRequest.class, type = "string"))
            @Valid @RequestPart(value = "mahkemeKararDetay") IDHedefAdSoyadGuncellemeRequest request,
            Authentication authentication
    ) {

        try {
            UserDetailsImpl user = (UserDetailsImpl) authentication.getPrincipal();

            log.info("IDHedefAdSoyadGuncellemeRequest received:{}, user:{}", request, user.getUsername());

            IMakosRequestProcessor<IDHedefAdSoyadGuncellemeRequest, IDHedefAdSoyadGuncellemeResponse> processor = processorFactory.getProcessor(IDHedefAdSoyadGuncellemeRequest.class, IDHedefAdSoyadGuncellemeResponse.class);
            IDHedefAdSoyadGuncellemeResponse response = processor.process(request, user);

            log.info("IDHedefAdSoyadGuncellemeRequest processed, id:{}, user:{}, response:{}", request.getId(), user.getUsername(), response);
            return ResponseEntity.status(MakosResponseCode.toHttpStatus(response.getResponse().getResponseCode()))
                    .body(response);

        } catch (Exception e) {
            log.error("IDHedefAdSoyadGuncellemeRequest failed. id:{}", request.getId(), e);
            IDHedefAdSoyadGuncellemeResponse response = IDHedefAdSoyadGuncellemeResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Internal Error")
                            .build())
                    .build();
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }

    }

    @PostMapping(path = "/mahkemeKoduGuncelle", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<IDMahkemeKoduGuncellemeResponse> mahkemeKoduGuncelle(

            @NotNull @RequestPart(value = "mahkemeKararDosyasi") MultipartFile mahkemeKararDosyasiID,

            @Parameter(required = true, description = "Mahkeme Karar Detaylari", schema = @Schema(implementation = IDMahkemeKoduGuncellemeRequest.class, type = "string"))
            @Valid @RequestPart(value = "mahkemeKararDetay") IDMahkemeKoduGuncellemeRequest request,
            Authentication authentication
    ) {

        try {
            UserDetailsImpl user = (UserDetailsImpl) authentication.getPrincipal();

            log.info("IDMahkemeKoduGuncellemeRequest received:{}, user:{}", request, user.getUsername());

            IMakosRequestProcessor<IDMahkemeKoduGuncellemeRequest, IDMahkemeKoduGuncellemeResponse> processor = processorFactory.getProcessor(IDMahkemeKoduGuncellemeRequest.class, IDMahkemeKoduGuncellemeResponse.class);
            IDMahkemeKoduGuncellemeResponse response = processor.process(request, user);

            log.info("IDMahkemeKoduGuncellemeRequest processed, id:{}, user:{}, response:{}", request.getId(), user.getUsername(), response);
            return ResponseEntity.status(MakosResponseCode.toHttpStatus(response.getResponse().getResponseCode()))
                    .body(response);

        } catch (Exception e) {
            log.error("IDMahkemeKoduGuncellemeRequest failed. id:{}", request.getId(), e);
            IDMahkemeKoduGuncellemeResponse response = IDMahkemeKoduGuncellemeResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Internal Error")
                            .build())
                    .build();
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }

    }

    @PostMapping(path = "/canakNoGuncelle", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<IDCanakGuncellemeResponse> canakNoGuncelle(

            @NotNull @RequestPart(value = "mahkemeKararDosyasi") MultipartFile mahkemeKararDosyasiID,

            @Parameter(required = true, description = "Mahkeme Karar Detaylari", schema = @Schema(implementation = IDCanakGuncellemeRequest.class, type = "string"))
            @Valid @RequestPart(value = "mahkemeKararDetay") IDCanakGuncellemeRequest request,
            Authentication authentication
    ) {

        try {
            UserDetailsImpl user = (UserDetailsImpl) authentication.getPrincipal();

            log.info("IDCanakGuncellemeRequest received:{}, user:{}", request, user.getUsername());

            IMakosRequestProcessor<IDCanakGuncellemeRequest, IDCanakGuncellemeResponse> processor = processorFactory.getProcessor(IDCanakGuncellemeRequest.class, IDCanakGuncellemeResponse.class);
            IDCanakGuncellemeResponse response = processor.process(request, user);

            log.info("IDCanakGuncellemeRequest processed, id:{}, user:{}, response:{}", request.getId(), user.getUsername(), response);
            return ResponseEntity.status(MakosResponseCode.toHttpStatus(response.getResponse().getResponseCode()))
                    .body(response);

        } catch (Exception e) {
            log.error("IDCanakGuncellemeRequest failed. id:{}", request.getId(), e);
            IDCanakGuncellemeResponse response = IDCanakGuncellemeResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("Internal Error")
                            .build())
                    .build();
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

}