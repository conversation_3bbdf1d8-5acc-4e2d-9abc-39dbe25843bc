{"info": {"_postman_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890", "name": "IYM Backend JWT Authentication", "description": "Complete JWT Authentication API collection for IYM Backend\n\nThis collection includes:\n- User Registration\n- User Login (JWT Token Generation)\n- Protected Endpoint Access\n- Token Validation Examples\n\nBase URL: http://localhost:4000\n\nSeeded User:\n- Username: iym_admin, Password: 123456 (already seeded in database)\n\nAdditional users can be created via the Register endpoint.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Authentication", "item": [{"name": "Register New User", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has success message\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"userName\": \"new_user\",\n    \"password\": \"newuser123\",\n    \"role\": \"ROLE_ADMIN\",\n    \"kurum\": \"BTK\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/register", "host": ["{{baseUrl}}"], "path": ["auth", "register"]}, "description": "Register a new user in the system. This endpoint creates a new IymUser with the provided credentials."}, "response": []}, {"name": "Login - Get JWT Token", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has token\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.token).to.exist;", "    pm.expect(jsonData.token).to.be.a('string');", "    ", "    // Store token for subsequent requests", "    pm.environment.set(\"jwt_token\", jsonData.token);", "    pm.environment.set(\"user_id\", jsonData.userId);", "    pm.environment.set(\"username\", jsonData.username);", "});", "", "pm.test(\"Response has user info\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.userId).to.exist;", "    pm.expect(jsonData.username).to.exist;", "    pm.expect(jsonData.roles).to.exist;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"iym_admin\",\n    \"password\": \"123456\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}, "description": "Login with username and password to get JW<PERSON> token. The token will be automatically stored in environment variables for use in subsequent requests."}, "response": [{"name": "Successful Login", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"iym_admin\",\n    \"password\": \"123456\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n    \"token\": \"eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJpeW1fYWRtaW4iLCJpYXQiOjE3NTAwOTIzOTMsImV4cCI6MTc1MDE3ODc5M30.gqG4CTowfZy2qE1kkj9XHXsCh0UFx-7fHej128ogWnO37jxfpea9myk5aL2Nqq83h1qlfPQFzQRPuVJVt0DBkQ\",\n    \"userId\": \"37b2b08c-edf5-3e85-e060-17ac020000ba\",\n    \"username\": \"iym_admin\",\n    \"actingUserName\": null,\n    \"roles\": [\"ROLE_ADMIN\"],\n    \"kurum\": \"BTK\"\n}"}]}, {"name": "Login - IYM Admin User", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has token\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.token).to.exist;", "    ", "    // Store admin token", "    pm.environment.set(\"admin_jwt_token\", jsonData.token);", "    pm.environment.set(\"admin_user_id\", jsonData.userId);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"iym_admin\",\n    \"password\": \"{{iym_admin_password}}\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}, "description": "Login as IYM admin user (seeded in database) to get JWT token with admin privileges."}, "response": []}], "description": "Authentication endpoints for user registration and login"}, {"name": "Protected Endpoints", "item": [{"name": "Change Password", "event": [{"listen": "prerequest", "script": {"exec": ["// Check if JWT token exists", "if (!pm.environment.get(\"jwt_token\")) {", "    throw new Error(\"JWT token not found. Please login first.\");", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Password changed successfully\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.eql(true);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"iym_admin\",\n    \"password\": \"newpassword123\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/changePassword", "host": ["{{baseUrl}}"], "path": ["auth", "changePassword"]}, "description": "Change user password. Requires valid JWT token in Authorization header."}, "response": []}], "description": "Protected endpoints that require JWT authentication"}, {"name": "API Documentation", "item": [{"name": "Swagger UI", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/swagger-ui.html", "host": ["{{baseUrl}}"], "path": ["swagger-ui.html"]}, "description": "Access Swagger UI for API documentation"}, "response": []}, {"name": "API Docs JSON", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api-docs", "host": ["{{baseUrl}}"], "path": ["api-docs"]}, "description": "Get API documentation in JSON format"}, "response": []}], "description": "API documentation endpoints"}, {"name": "Public Endpoints", "item": [{"name": "Health Check - Public", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/health/check", "host": ["{{baseUrl}}"], "path": ["health", "check"]}, "description": "Public health check endpoint"}, "response": []}, {"name": "Test Anonymous Access", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Anonymous access works\", function () {", "    // Should work without authentication", "    pm.response.to.have.status(200);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/public/test", "host": ["{{baseUrl}}"], "path": ["public", "test"]}, "description": "Test anonymous access to public endpoints"}, "response": []}], "description": "Public endpoints that don't require authentication"}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Set base URL if not already set", "if (!pm.environment.get(\"baseUrl\")) {", "    pm.environment.set(\"baseUrl\", \"http://localhost:4000\");", "}"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Global test to check response time", "pm.test(\"Response time is less than 5000ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});"]}}], "variable": [{"key": "baseUrl", "value": "http://localhost:4000", "type": "string"}]}