package iym.makos.dto.auth;

import iym.common.validation.ValidationResult;
import iym.makos.model.MakosRequest;
import iym.makos.validator.MakosRequestValid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;
import lombok.extern.slf4j.Slf4j;

/**
 * Login request DTO for MAKOS authentication
 */
@Data
@Builder
@Jacksonized
@ToString
@EqualsAndHashCode
@MakosRequestValid
@Slf4j
public class LoginRequest implements MakosRequest {

    @NotNull
    @NotBlank
    private String username;

    @NotNull
    @NotBlank
    private String password;

    @Override
    public ValidationResult isValid() {
        log.trace("Checking if LoginRequest is valid");
        
        ValidationResult validationResult = new ValidationResult(true);
        
        try {
            if (username == null || username.trim().isEmpty()) {
                validationResult.addFailedReason("Username cannot be null or empty");
            }
            
            if (password == null || password.trim().isEmpty()) {
                validationResult.addFailedReason("Password cannot be null or empty");
            }
        } catch (Exception e) {
            log.error("Validation failed", e);
            validationResult.addFailedReason("Validation error: " + e.getMessage());
        }
        
        return validationResult;
    }
}
