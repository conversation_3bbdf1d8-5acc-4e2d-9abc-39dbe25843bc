package iym.common.model.entity.iym;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * Entity class for EVRAK_KAYIT table
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode

@Entity(name = "EvrakKayit")
@Table(name = "EVRAK_KAYIT")
public class EvrakKayit implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "EVRAK_KAYIT_SEQ")
    @SequenceGenerator(name = "EVRAK_KAYIT_SEQ", sequenceName = "EVRAK_KAYIT_SEQ", allocationSize = 1)
    private Long id;

    @Column(name = "EVRAK_SIRA_NO", length = 30, unique = true)
    private String evrakSiraNo;

    @Column(name = "EVRAK_NO", length = 50)
    @Size(max = 50, message = "Evrak No 50 karakterden fazla olamaz")
    private String evrakNo;

    @Column(name = "GIRIS_TARIH", nullable = false)
    @NotNull
    @Temporal(TemporalType.TIMESTAMP)
    private Date girisTarih;

    @Column(name = "EVRAK_TARIHI", nullable = false)
    @NotNull
    @Temporal(TemporalType.TIMESTAMP)
    private Date evrakTarihi;

    @Column(name = "EVRAK_GELDIGI_KURUM", length = 10)
    private String evrakGeldigiKurumKodu;

    @Column(name = "KAY_KULLANICI")
    private Long kayKullaniciId;

    @Column(name = "EVRAK_TIPI", length = 10)
    private String evrakTipi;

    @Column(name = "HAVALE_BIRIM", length = 10)
    private String havaleBirim;

    @Column(name = "ACIKLAMA", length = 4000)
    @Size(max = 4000)
    private String aciklama;

    @Column(name = "GEL_IL", length = 4)
    private String geldigiIlIlceKodu;

    @Column(name = "EVRAK_KONUSU", length = 200)
    @Size(max = 200)
    private String evrakKonusu;

    @Column(name = "ARSIV_DOSYA_NO", length = 20)
    private String arsivDosyaNo;

    @Column(name = "DURUMU", length = 20)
    private String durumu;

    @Column(name = "EVRAK_YONU", length = 100)
    private String evrakYonu;

    @Column(name = "ONAY_TARIHI")
    @Temporal(TemporalType.TIMESTAMP)
    private Date onayTarihi;

    @Column(name = "ACILMI", length = 1, columnDefinition = "CHAR(1)")
    private String acilmi;

    @Column(name = "SORUSTURMA_NO", length = 20)
    private String sorusturmaNo;

    @Column(name = "MAHKEME_KARAR_NO", length = 20)
    private String mahkemeKararNo;

    @Column(name = "UNIQ_COL")
    private Long uniqCol;

    @Column(name = "ELDEN_TESLIM", length = 1)
    private String eldenTeslim;

    @Column(name = "TEKITMI", length = 1, columnDefinition = "CHAR(1)")
    private String tekitmi;

    @Column(name = "ASIL_EVRAK", length = 1, columnDefinition = "CHAR(1)")
    private String asilEvrak;

    @Column(name = "ONCELIK", length = 50)
    private String oncelik;

    @Column(name = "EVRAK_GELDIGI_KURUM_ESKI", length = 10)
    private String evrakGeldigiKurumEski;

    @Column(name = "EVRAK_GRUP", length = 300)
    private String evrakGrup;

    @Column(name = "ONAMA", length = 1)
    private String onama;
}
