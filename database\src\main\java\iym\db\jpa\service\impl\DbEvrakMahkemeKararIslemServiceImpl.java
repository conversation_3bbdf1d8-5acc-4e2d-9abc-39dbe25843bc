package iym.db.jpa.service.impl;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.EvrakMahkemeKararIslem;
import iym.common.service.db.DbEvrakMahkemeKararIslemService;
import iym.db.jpa.dao.EvrakMahkemeKararIslemRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Service implementation for EvrakMahkemeKararIslem entity
 */
@Service
public class DbEvrakMahkemeKararIslemServiceImpl extends GenericDbServiceImpl<EvrakMahkemeKararIslem, Long> implements DbEvrakMahkemeKararIslemService {

    private final EvrakMahkemeKararIslemRepo evrakMahkemeKararIslemRepo;

    @Autowired
    public DbEvrakMahkemeKararIslemServiceImpl(EvrakMahkemeKararIslemRepo repository) {
        super(repository);
        this.evrakMahkemeKararIslemRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public List<EvrakMahkemeKararIslem> findByKurum(String kurum) {
        return evrakMahkemeKararIslemRepo.findByKurum(kurum);
    }



    @Override
    @Transactional(readOnly = true)
    public List<EvrakMahkemeKararIslem> findByKurumAndSeviye(String kurum, String seviye) {
        return evrakMahkemeKararIslemRepo.findByKurumAndSeviye(kurum, seviye);
    }
}
