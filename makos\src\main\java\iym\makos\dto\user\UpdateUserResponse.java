package iym.makos.dto.user;

import iym.common.model.api.ApiResponseBase;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;



/**
 * Update user response DTO for MAKOS user management
 */
@Data
@SuperBuilder
@ToString
@EqualsAndHashCode(callSuper = true)
public class UpdateUserResponse extends ApiResponseBase {

    private Long id;
    // Inherits ApiResponse response from ApiResponseBase
}
