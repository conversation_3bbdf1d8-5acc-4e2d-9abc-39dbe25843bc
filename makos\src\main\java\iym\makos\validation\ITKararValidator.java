package iym.makos.validation;

import iym.common.model.api.KararTuru;
import iym.common.service.db.DbMahkemeAidiyatService;
import iym.common.service.db.DbMahkemeKararService;
import iym.common.validation.ValidationResult;
import iym.makos.dto.it.ITKararRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ITKararValidator extends MahkemeKararRequestValidatorBase<ITKararRequest> {

    private final DbMahkemeKararService dbMahkemeKararService;
    private final DbMahkemeAidiyatService dbMahkemeAidiyatService;

    @Autowired
    public ITKararValidator(DbMahkemeKararService dbMahkemeKararService,
                            DbMahkemeAidiyatService dbMahkemeAidiyatService) {
        this.dbMahkemeKararService = dbMahkemeKararService;
        this.dbMahkemeAidiyatService = dbMahkemeAidiyatService;

    }

    @Override
    public ValidationResult validate(ITKararRequest request) {
        try{

            ValidationResult validationResult = super.validate(request);
            if (!validationResult.isValid()) {
                return validationResult;
            }

            // TODO

            return validationResult;
        }
         catch (Exception e) {
            log.error("Validation failed", e);
            return new ValidationResult("Validation failed. Internal error");
        }
    }

    @Override
    public KararTuru getRelatedKararTuru() {
        return KararTuru.ILETISIMIN_TESPITI;
    }

}

