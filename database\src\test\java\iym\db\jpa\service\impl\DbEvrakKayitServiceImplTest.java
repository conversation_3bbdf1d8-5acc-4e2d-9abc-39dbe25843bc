package iym.db.jpa.service.impl;

import iym.common.model.entity.iym.EvrakKayit;
import iym.db.jpa.dao.EvrakKayitRepo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DbEvrakKayitServiceImplTest {

    @Mock
    private EvrakKayitRepo evrakKayitRepo;

    @InjectMocks
    private DbEvrakKayitServiceImpl dbEvrakKayitService;

    private EvrakKayit evrakKayit;
    private Date testDate;

    @BeforeEach
    void setUp() {
        testDate = new Date();
        evrakKayit = EvrakKayit.builder()
                .id(1L)
                .evrakSiraNo("TEST-002")
                .evrakNo("TEST-EVRAK-002")
                .girisTarih(testDate)
                .evrakTarihi(testDate)
                .evrakGeldigiKurumKodu("02")
                .evrakTipi("ILETISIMIN_DENETLENMESI")
                .havaleBirim("02")
                .aciklama("Test açıklama")
                .geldigiIlIlceKodu("0600")
                .durumu("AKTIF")
                .acilmi("H")
                .build();
    }



    @Test
    void findByEvrakTipi_ShouldReturnListOfEvrakKayit_WhenEvrakTipiExists() {
        // Given
        List<EvrakKayit> evrakKayitList = Arrays.asList(evrakKayit);
        when(evrakKayitRepo.findByEvrakTipi("ILETISIMIN_DENETLENMESI")).thenReturn(evrakKayitList);

        // When
        List<EvrakKayit> result = dbEvrakKayitService.findByEvrakTipi("ILETISIMIN_DENETLENMESI");

        // Then
        assertThat(result).isNotEmpty();
        assertThat(result).hasSize(1);
        assertThat(result.get(0).getEvrakTipi()).isEqualTo("ILETISIMIN_DENETLENMESI");
        verify(evrakKayitRepo, times(1)).findByEvrakTipi("ILETISIMIN_DENETLENMESI");
    }

    @Test
    void findByDurumu_ShouldReturnListOfEvrakKayit_WhenDurumuExists() {
        // Given
        List<EvrakKayit> evrakKayitList = Arrays.asList(evrakKayit);
        when(evrakKayitRepo.findByDurumu("AKTIF")).thenReturn(evrakKayitList);

        // When
        List<EvrakKayit> result = dbEvrakKayitService.findByDurumu("AKTIF");

        // Then
        assertThat(result).isNotEmpty();
        assertThat(result).hasSize(1);
        assertThat(result.get(0).getDurumu()).isEqualTo("AKTIF");
        verify(evrakKayitRepo, times(1)).findByDurumu("AKTIF");
    }
}
