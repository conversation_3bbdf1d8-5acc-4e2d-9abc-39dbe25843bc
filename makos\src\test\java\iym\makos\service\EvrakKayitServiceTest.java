package iym.makos.service;

import iym.common.model.entity.iym.EvrakKayit;
import iym.common.service.db.DbEvrakKayitService;
import iym.makos.mapper.EvrakKayitMapper;
import iym.makos.service.makos.EvrakKayitService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.web.server.ResponseStatusException;
import iym.makos.dto.EvrakKayitDTO;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.doNothing;

@ExtendWith(MockitoExtension.class)
class EvrakKayitServiceTest {

    @Mock
    private DbEvrakKayitService dbEvrakKayitService;

    @Mock
    private EvrakKayitMapper evrakKayitMapper;

    @InjectMocks
    private EvrakKayitService evrakKayitService;

    private EvrakKayit evrakKayit;
    private EvrakKayitDTO evrakKayitDTO;

    @BeforeEach
    void setUp() {
        Date testDate = new Date();

        evrakKayit = EvrakKayit.builder()
                .id(1L)
                .evrakSiraNo("TEST-001")
                .evrakNo("TEST-EVRAK-001")
                .girisTarih(testDate)
                .evrakTarihi(testDate)
                .build();

        evrakKayitDTO = EvrakKayitDTO.builder()
                .id(1L)
                .evrakSiraNo("TEST-001")
                .evrakNo("TEST-EVRAK-001")
                .girisTarih(testDate)
                .evrakTarihi(testDate)
                .build();
    }

    @Test
    void findAll() {
        // Given
        List<EvrakKayit> evrakKayitList = new ArrayList<>();
        evrakKayitList.add(evrakKayit);

        List<EvrakKayitDTO> evrakKayitDTOList = new ArrayList<>();
        evrakKayitDTOList.add(evrakKayitDTO);

        when(dbEvrakKayitService.findAll()).thenReturn(evrakKayitList);
        when(evrakKayitMapper.toDtoList(evrakKayitList)).thenReturn(evrakKayitDTOList);

        // When
        List<EvrakKayitDTO> result = evrakKayitService.findAll();

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(1);
        assertThat(result.get(0).getId()).isEqualTo(1L);

        verify(dbEvrakKayitService).findAll();
        verify(evrakKayitMapper).toDtoList(evrakKayitList);
    }

    @Test
    void findAllPaginated() {
        // Given
        List<EvrakKayit> evrakKayitList = new ArrayList<>();
        evrakKayitList.add(evrakKayit);

        Page<EvrakKayit> evrakKayitPage = new PageImpl<>(evrakKayitList);

        List<EvrakKayitDTO> evrakKayitDTOList = new ArrayList<>();
        evrakKayitDTOList.add(evrakKayitDTO);

        Pageable pageable = PageRequest.of(0, 10);

        // DbEvrakKayitService inherits findAll(Pageable) from GenericDbService
        when(dbEvrakKayitService.findAll(pageable)).thenReturn(evrakKayitPage);
        when(evrakKayitMapper.toDtoList(evrakKayitList)).thenReturn(evrakKayitDTOList);

        // When
        Page<EvrakKayitDTO> result = evrakKayitService.findAll(pageable);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getContent()).hasSize(1);
        assertThat(result.getContent().get(0).getId()).isEqualTo(1L);

        verify(dbEvrakKayitService).findAll(pageable);
        verify(evrakKayitMapper).toDtoList(evrakKayitList);
    }

    @Test
    void findById() {
        // Given
        when(dbEvrakKayitService.findById(1L)).thenReturn(Optional.of(evrakKayit));
        when(evrakKayitMapper.toDto(evrakKayit)).thenReturn(evrakKayitDTO);

        // When
        EvrakKayitDTO result = evrakKayitService.findById(1L);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(1L);

        verify(dbEvrakKayitService).findById(1L);
        verify(evrakKayitMapper).toDto(evrakKayit);
    }

    @Test
    void findByIdNotFound() {
        // Given
        when(dbEvrakKayitService.findById(1L)).thenReturn(Optional.empty());

        // When/Then
        assertThatThrownBy(() -> evrakKayitService.findById(1L))
                .isInstanceOf(ResponseStatusException.class)
                .hasMessageContaining("Evrak kayıt bulunamadı");

        verify(dbEvrakKayitService).findById(1L);
        verify(evrakKayitMapper, never()).toDto(any());
    }
/*
    @Test
    void create() {
        // Given
        when(dbEvrakKayitService.findByEvrakSiraNo("TEST-001")).thenReturn(Optional.empty());
        when(evrakKayitMapper.toEntity(evrakKayitDTO)).thenReturn(evrakKayit);
        // The save method returns void, so we use doNothing() instead of when()
        doNothing().when(dbEvrakKayitService).save(any(EvrakKayit.class));
        when(evrakKayitMapper.toDto(evrakKayit)).thenReturn(evrakKayitDTO);

        // When
        EvrakKayitDTO result = evrakKayitService.create(evrakKayitDTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(1L);

        verify(dbEvrakKayitService).findByEvrakSiraNo("TEST-001");
        verify(evrakKayitMapper).toEntity(evrakKayitDTO);
        verify(dbEvrakKayitService).save(evrakKayit);
        verify(evrakKayitMapper).toDto(evrakKayit);
    }

    @Test
    void createWithExistingEvrakSiraNo() {
        // Given
        when(dbEvrakKayitService.existsByEvrakSiraNo("TEST-001")).thenReturn(Optional.of(evrakKayit));

        // When/Then
        assertThatThrownBy(() -> evrakKayitService.create(evrakKayitDTO))
                .isInstanceOf(ResponseStatusException.class)
                .hasMessageContaining("Evrak sıra no zaten mevcut");

        verify(dbEvrakKayitService).findByEvrakSiraNo("TEST-001");
        verify(evrakKayitMapper, never()).toEntity(any());
        verify(dbEvrakKayitService, never()).save(any());
    }
*/
    @Test
    void update() {
        // Given
        when(dbEvrakKayitService.findById(1L)).thenReturn(Optional.of(evrakKayit));
        when(evrakKayitMapper.updateEntityFromDto(evrakKayit, evrakKayitDTO)).thenReturn(evrakKayit);
        // The update method returns void, so we use doNothing() instead of when()
        doNothing().when(dbEvrakKayitService).update(any(EvrakKayit.class));
        when(evrakKayitMapper.toDto(evrakKayit)).thenReturn(evrakKayitDTO);

        // When
        EvrakKayitDTO result = evrakKayitService.update(1L, evrakKayitDTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(1L);

        verify(dbEvrakKayitService).findById(1L);
        verify(evrakKayitMapper).updateEntityFromDto(evrakKayit, evrakKayitDTO);
        verify(dbEvrakKayitService).update(evrakKayit);
        verify(evrakKayitMapper).toDto(evrakKayit);
    }

    @Test
    void updateNotFound() {
        // Given
        when(dbEvrakKayitService.findById(1L)).thenReturn(Optional.empty());

        // When/Then
        assertThatThrownBy(() -> evrakKayitService.update(1L, evrakKayitDTO))
                .isInstanceOf(ResponseStatusException.class)
                .hasMessageContaining("Evrak kayıt bulunamadı");

        verify(dbEvrakKayitService).findById(1L);
        verify(evrakKayitMapper, never()).updateEntityFromDto(any(), any());
        verify(dbEvrakKayitService, never()).update(any());
    }

    @Test
    void delete() {
        // Given
        when(dbEvrakKayitService.findById(1L)).thenReturn(Optional.of(evrakKayit));

        // When
        evrakKayitService.delete(1L);

        // Then
        verify(dbEvrakKayitService).findById(1L);
        verify(dbEvrakKayitService).delete(evrakKayit);
    }

    @Test
    void deleteNotFound() {
        // Given
        when(dbEvrakKayitService.findById(1L)).thenReturn(Optional.empty());

        // When/Then
        assertThatThrownBy(() -> evrakKayitService.delete(1L))
                .isInstanceOf(ResponseStatusException.class)
                .hasMessageContaining("Evrak kayıt bulunamadı");

        verify(dbEvrakKayitService).findById(1L);
        verify(dbEvrakKayitService, never()).delete(any());
    }
}
