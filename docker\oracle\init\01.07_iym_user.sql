-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;

-- Create sequence for IYM_USER if it doesn't exist
DECLARE
  seq_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO seq_exists FROM user_sequences WHERE sequence_name = 'IYM_USER_SEQ';
  IF seq_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE SEQUENCE iym.IYM_USER_SEQ
      START WITH 1
      INCREMENT BY 1
      NOCACHE
      NOCYCLE';
  END IF;
END;
/

-- Create IYM_USER table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'IYM_USER';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.IYM_USER (
      ID RAW(16) DEFAULT SYS_GUID() PRIMARY KEY,
      USERNAME VARCHAR2(100 BYTE) NOT NULL,
      PASSWORD VARCHAR2(100 BYTE) NOT NULL,
      STATUS VARCHAR2(20 BYTE) NOT NULL,
      ROLE VARCHAR2(20 BYTE) NOT NULL,
      KURUM NUMBER,
      CONSTRAINT iym_user_un UNIQUE (USERNAME)
    )';
  END IF;
END;
/



-- Create indexes for IYM_USER if they don't exist
BEGIN
  -- Try to create index on STATUS for filtering active/inactive users
  BEGIN
    EXECUTE IMMEDIATE 'CREATE INDEX iym.IYM_USER_STATUS_IDX ON iym.IYM_USER (STATUS)';
  EXCEPTION
    WHEN OTHERS THEN
      IF SQLCODE = -1408 THEN -- ORA-01408: such column list already indexed
        NULL; -- Index already exists, ignore the error
      ELSE
        RAISE; -- Re-raise any other error
      END IF;
  END;

  -- Try to create index on KURUM for filtering by institution
  BEGIN
    EXECUTE IMMEDIATE 'CREATE INDEX iym.IYM_USER_KURUM_IDX ON iym.IYM_USER (KURUM)';
  EXCEPTION
    WHEN OTHERS THEN
      IF SQLCODE = -1408 THEN -- ORA-01408: such column list already indexed
        NULL; -- Index already exists, ignore the error
      ELSE
        RAISE; -- Re-raise any other error
      END IF;
  END;

  -- Try to create composite index on ROLE and STATUS for common queries
  BEGIN
    EXECUTE IMMEDIATE 'CREATE INDEX iym.IYM_USER_ROLE_STATUS_IDX ON iym.IYM_USER (ROLE, STATUS)';
  EXCEPTION
    WHEN OTHERS THEN
      IF SQLCODE = -1408 THEN -- ORA-01408: such column list already indexed
        NULL; -- Index already exists, ignore the error
      ELSE
        RAISE; -- Re-raise any other error
      END IF;
  END;
END;
/


COMMIT;
