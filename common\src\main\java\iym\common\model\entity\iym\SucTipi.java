package iym.common.model.entity.iym;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * Entity class for EVRAK_KAYIT table
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode

@Entity(name = "SucTipleri")
@Table(name = "MAHKEME_SUC_TIPLERI")
public class SucTipi implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "SUC_TIPI")
    private String sucTipiKodu;

    @Column(name = "SUC_ACIKLAMA")
    private String aciklama;

    @Column(name = "SUC_MAH_TIP")
    private String mahkemeKaraTipiKodu;

    @Column(name = "DURUM")
    private String durum;


}
