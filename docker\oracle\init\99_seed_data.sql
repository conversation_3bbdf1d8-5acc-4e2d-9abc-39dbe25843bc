-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;

-- Seed data for ILLER if it doesn't exist
DECLARE
  il_count NUMBER;
BEGIN
  SELECT COUNT(*) INTO il_count FROM iym.ILLER;
  IF il_count = 0 THEN
    INSERT INTO iym.ILLER (IL_KOD, IL_ADI, ILCE_ADI) VALUES ('0100', 'ADANA', 'MERKEZ');
    INSERT INTO iym.ILLER (IL_KOD, IL_ADI, ILCE_ADI) VALUES ('0101', 'ADANA', 'CEYHAN');
    INSERT INTO iym.ILLER (IL_KOD, IL_ADI, ILCE_ADI) VALUES ('0102', 'ADANA', 'FEKE');
    INSERT INTO iym.ILLER (IL_KOD, IL_ADI, ILCE_ADI) VALUES ('0600', 'ANKARA', 'MERKEZ');
    INSERT INTO iym.ILLER (IL_KOD, IL_ADI, ILCE_ADI) VALUES ('0601', 'ANKARA', 'AKYURT');
    INSERT INTO iym.ILLER (IL_KOD, IL_ADI, ILCE_ADI) VALUES ('0602', 'ANKARA', 'AYAŞ');
    INSERT INTO iym.ILLER (IL_KOD, IL_ADI, ILCE_ADI) VALUES ('3400', 'İSTANBUL', 'MERKEZ');
    INSERT INTO iym.ILLER (IL_KOD, IL_ADI, ILCE_ADI) VALUES ('3401', 'İSTANBUL', 'ADALAR');
    INSERT INTO iym.ILLER (IL_KOD, IL_ADI, ILCE_ADI) VALUES ('3402', 'İSTANBUL', 'BAKIRKÖY');
  END IF;
END;
/

-- Seed data for EVRAK_GELEN_KURUMLAR if it doesn't exist
DECLARE
  kurum_count NUMBER;
BEGIN
  SELECT COUNT(*) INTO kurum_count FROM iym.EVRAK_GELEN_KURUMLAR;
  IF kurum_count = 0 THEN
    INSERT INTO iym.EVRAK_GELEN_KURUMLAR (ID, KURUM_KOD, KURUM_ADI, KURUM, IDX) VALUES (1, '01', 'ADALET BAKANLIĞI', 'ADALET BAKANLIĞI', 1);
    INSERT INTO iym.EVRAK_GELEN_KURUMLAR (ID, KURUM_KOD, KURUM_ADI, KURUM, IDX) VALUES (2, '02', 'İÇİŞLERİ BAKANLIĞI', 'İÇİŞLERİ BAKANLIĞI', 2);
    INSERT INTO iym.EVRAK_GELEN_KURUMLAR (ID, KURUM_KOD, KURUM_ADI, KURUM, IDX) VALUES (3, '03', 'DIŞİŞLERİ BAKANLIĞI', 'DIŞİŞLERİ BAKANLIĞI', 3);
    INSERT INTO iym.EVRAK_GELEN_KURUMLAR (ID, KURUM_KOD, KURUM_ADI, KURUM, IDX) VALUES (4, '04', 'MALİYE BAKANLIĞI', 'MALİYE BAKANLIĞI', 4);
    INSERT INTO iym.EVRAK_GELEN_KURUMLAR (ID, KURUM_KOD, KURUM_ADI, KURUM, IDX) VALUES (5, '05', 'MİLLİ EĞİTİM BAKANLIĞI', 'MİLLİ EĞİTİM BAKANLIĞI', 5);
  END IF;
END;
/

-- Seed data for KULLANICILAR if it doesn't exist
DECLARE
  user_count NUMBER;
BEGIN
  SELECT COUNT(*) INTO user_count FROM iym.KULLANICILAR WHERE KULLANICI_ADI = 'admin';
  IF user_count = 0 THEN
    INSERT INTO iym.KULLANICILAR (
      ID, ADI, SOYADI, KULLANICI_ADI, SIFRE, DURUMU,
      GOREVI, BIRIMI, GOREV_TANIMI, TEL, POSTA
    )
    VALUES (
      iym.KULLANICILAR_SEQ.NEXTVAL, 'Admin', 'User', 'admin', 'e10adc3949ba59abbe56e057f20f883e', 'ACTIVE',
      'ADMIN', '0001', 'Sistem Yöneticisi', '5551234567', '<EMAIL>'
    );
  END IF;
END;
/

-- Seed data for MAKOS_USER if it doesn't exist
DECLARE
  user_count NUMBER;
BEGIN
  SELECT COUNT(*) INTO user_count FROM iym.MAKOS_USER WHERE USERNAME = 'makos_admin';
  IF user_count = 0 THEN
    INSERT INTO iym.MAKOS_USER (ID, USERNAME, PASSWORD, STATUS, ROLE, KURUM)
    VALUES (iym.MAKOS_USER_SEQ.NEXTVAL, 'makos_admin', '$2a$10$hmjzWHKnKcaC8PJ2Xy/bWuxQpo09JWSdUjSJIGn3I5LRI1hmM6hWO', 'ACTIVE', 'ROLE_ADMIN', 5);
  END IF;
END;
/

-- Seed data for IYM_USER if it doesn't exist
DECLARE
  user_count NUMBER;
BEGIN
  SELECT COUNT(*) INTO user_count FROM iym.IYM_USER WHERE USERNAME = 'iym_admin';
  IF user_count = 0 THEN
    INSERT INTO iym.IYM_USER (ID, USERNAME, PASSWORD, STATUS, ROLE, KURUM)
    VALUES (iym.MAKOS_USER_SEQ.NEXTVAL, 'iym_admin', '$2a$10$hmjzWHKnKcaC8PJ2Xy/bWuxQpo09JWSdUjSJIGn3I5LRI1hmM6hWO', 'ACTIVE', 'ROLE_ADMIN', 5);
  END IF;
END;
/

-- Seed data for EVRAK_KAYIT if it doesn't exist
DECLARE
  evrak_count NUMBER;
BEGIN
  SELECT COUNT(*) INTO evrak_count FROM iym.EVRAK_KAYIT;
  IF evrak_count = 0 THEN
    -- First record

           INSERT INTO iym.EVRAK_KAYIT (
             ID, EVRAK_SIRA_NO, EVRAK_NO, GIRIS_TARIH, EVRAK_TARIHI,
             EVRAK_GELDIGI_KURUM, KAY_KULLANICI, EVRAK_TIPI, HAVALE_BIRIM,
             ACIKLAMA, GEL_IL, EVRAK_KONUSU, DURUMU, EVRAK_YONU, ACILMI,
             SORUSTURMA_NO, MAHKEME_KARAR_NO, ASIL_EVRAK, EVRAK_GRUP, ONAMA
           ) VALUES (
             iym.EVRAK_KAYIT_SEQ.NEXTVAL, 'TEST-2025-001', 'IDB-2025-12345',
             TO_DATE('2023-05-15 10:30:00', 'YYYY-MM-DD HH24:MI:SS'),
             TO_DATE('2023-05-10 14:00:00', 'YYYY-MM-DD HH24:MI:SS'),
             '02', 1, '401.01.00', 'HUKUK',
             'İletişimin denetlenmesi talebi örnek açıklama', '0600', 'İletişimin Denetlenmesi Talebi',
             'AKTIF', 'ILETISIMIN_DENETLENMESI', 'H',
             '2023/123', 'MK-2023-001', 'H', 'İletişim Denetleme', 'E'
           );

           -- Sample data 2 - Mahkeme Kararı
           INSERT INTO iym.EVRAK_KAYIT (
             ID, EVRAK_SIRA_NO, EVRAK_NO, GIRIS_TARIH, EVRAK_TARIHI,
             EVRAK_GELDIGI_KURUM, KAY_KULLANICI, EVRAK_TIPI, HAVALE_BIRIM,
             ACIKLAMA, GEL_IL, EVRAK_KONUSU, DURUMU, EVRAK_YONU, ACILMI,
             SORUSTURMA_NO, MAHKEME_KARAR_NO, ASIL_EVRAK, EVRAK_GRUP, ONAMA
           ) VALUES (
             iym.EVRAK_KAYIT_SEQ.NEXTVAL, 'TEST-2025-002', 'IDB-2025-12346',
             TO_DATE('2023-05-16 11:30:00', 'YYYY-MM-DD HH24:MI:SS'),
             TO_DATE('2023-05-11 15:00:00', 'YYYY-MM-DD HH24:MI:SS'),
             '02', 1, '401.01.00', 'HUKUK',
             'İletişimin denetlenmesi talebi örnek açıklama', '0600', 'Mahkeme Kararı Talebi',
             'AKTIF', 'ILETISIMIN_DENETLENMESI', 'H',
             '2023/124', 'MK-2023-002', 'H', 'Mahkeme Kararları', 'E'
           );

           -- Sample data 3 - İletişimin Tespiti
           INSERT INTO iym.EVRAK_KAYIT (
             ID, EVRAK_SIRA_NO, EVRAK_NO, GIRIS_TARIH, EVRAK_TARIHI,
             EVRAK_GELDIGI_KURUM, KAY_KULLANICI, EVRAK_TIPI, HAVALE_BIRIM,
             ACIKLAMA, GEL_IL, EVRAK_KONUSU, DURUMU, EVRAK_YONU, ACILMI,
             SORUSTURMA_NO, MAHKEME_KARAR_NO, ASIL_EVRAK, EVRAK_GRUP, ONAMA
           ) VALUES (
             iym.EVRAK_KAYIT_SEQ.NEXTVAL, 'TEST-2025-003', 'E-2025-12347',
             TO_DATE('2023-05-17 12:30:00', 'YYYY-MM-DD HH24:MI:SS'),
             TO_DATE('2023-05-12 16:00:00', 'YYYY-MM-DD HH24:MI:SS'),
             '02', 1, '401.01.00', 'HUKUK',
             'İletişimin denetlenmesi talebi örnek açıklama', '0100', 'İletişimin Denetlenmesş Talebi',
             'AKTIF', 'ILETISIMIN_DENETLENMESI', 'H',
             '2023/125', 'MK-2023-003', 'H', 'İletişim Tespiti', 'E'
           );

  END IF;
END;
/


DECLARE
  mahkeke_karar_count NUMBER;
BEGIN
  SELECT COUNT(*) INTO mahkeke_karar_count FROM iym.EVRAK_KAYIT;
  IF mahkeke_karar_count = 0 THEN
    -- First record

                  SELECT ID INTO admin_id FROM iym.KULLANICILAR WHERE KULLANICI_ADI = 'admin';

                  -- Get the ID of the first evrak
                  SELECT ID INTO evrak_id1 FROM iym.EVRAK_KAYIT WHERE EVRAK_SIRA_NO = 'TEST-2025-001';

                  -- Sample data 1 - İletişimin Denetlenmesi Kararı
                  INSERT INTO iym.MAHKEME_KARAR (
                    ID, EVRAK_ID, KULLANICI_ID, KAYIT_TARIHI, DURUM,
                    HUKUK_BIRIM, KARAR_TIP, MAH_KARAR_BAS_TAR, MAH_KARAR_BITIS_TAR,
                    MAHKEME_ADI, MAHKEME_KARAR_NO, MAHKEME_ILI, ACIKLAMA,
                    HAKIM_SICIL_NO, SORUSTURMA_NO, MAHKEME_KODU
                  ) VALUES (
                    iym.MAHKEME_KARAR_SEQ.NEXTVAL, evrak_id1, admin_id, SYSDATE, 'AKTIF',
                    'AĞIR CEZA', 'ILETISIMIN_DENETLENMESI', SYSDATE, ADD_MONTHS(SYSDATE, 3),
                    'ANKARA 1. AĞIR CEZA MAHKEMESİ', 'MK-2023-001', '0600', 'İletişimin denetlenmesi kararı',
                    'HSN12345', '2025/123', '06000101'
                  );

                  -- Get the ID of the second evrak
                  SELECT ID INTO evrak_id2 FROM iym.EVRAK_KAYIT WHERE EVRAK_SIRA_NO = 'TEST-2025-002';

                  -- Sample data 2 - Mahkeme Kararı
                  INSERT INTO iym.MAHKEME_KARAR (
                    ID, EVRAK_ID, KULLANICI_ID, KAYIT_TARIHI, DURUM,
                    HUKUK_BIRIM, KARAR_TIP, MAH_KARAR_BAS_TAR, MAH_KARAR_BITIS_TAR,
                    MAHKEME_ADI, MAHKEME_KARAR_NO, MAHKEME_ILI, ACIKLAMA,
                    HAKIM_SICIL_NO, SORUSTURMA_NO, MAHKEME_KODU
                  ) VALUES (
                    iym.MAHKEME_KARAR_SEQ.NEXTVAL, evrak_id2, admin_id, SYSDATE, 'AKTIF',
                    'SULH CEZA', 'ILETISIMIN_DENETLENMESI', SYSDATE, ADD_MONTHS(SYSDATE, 2),
                    'ANKARA 1. SULH CEZA MAHKEMESİ', 'MK-2023-002', '0601', 'Genel mahkeme kararı',
                    'HSN54321', '2025/124', '06000102'
                  );

  END IF;
END;
/


COMMIT;
