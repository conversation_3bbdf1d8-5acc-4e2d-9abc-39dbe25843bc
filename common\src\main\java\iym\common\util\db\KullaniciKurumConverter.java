package iym.common.util.db;

import iym.common.model.api.KullaniciKurum;
import jakarta.persistence.AttributeConverter;
import org.springframework.stereotype.Component;

public class KullaniciKurumConverter implements AttributeConverter<KullaniciKurum, Integer> {

    @Override
    public Integer convertToDatabaseColumn(KullaniciKurum kurum) {
        if (kurum == null) {
            return null;
        } else {
            return kurum.getValue();
        }
    }

    @Override
    public KullaniciKurum convertToEntityAttribute(Integer kurum) {
        if (kurum == null) {
            return null;
        } else {
            return KullaniciKurum.convert(kurum).orElse(null);
        }
    }
}
