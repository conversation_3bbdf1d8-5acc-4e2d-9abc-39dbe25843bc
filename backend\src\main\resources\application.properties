# Server configuration
server.port=4000
server.servlet.context-path=/

## to avoid exception on startup: NoSuchFileException: mchange-commons-java-0.2.19.jar
server.tomcat.additional-tld-skip-patterns=*mchange-commons-java*.jar

# Default profile (development)
spring.profiles.active=dev

# PostgreSQL Database configuration
spring.datasource.url=************************************
spring.datasource.driverClassName=org.postgresql.Driver
spring.datasource.username=iym
spring.datasource.password=iym
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.default_schema=public

# JPA configuration
spring.jpa.hibernate.ddl-auto=none
spring.jpa.properties.hibernate.hbm2ddl.auto=none
spring.jpa.properties.hibernate.validator.apply_to_ddl=false

spring.jpa.properties.hibernate.temp.use_jdbc_metadata_defaults=false
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# Connection pool configuration
spring.datasource.hikari.connectionTimeout=20000
spring.datasource.hikari.maximumPoolSize=5

# Logging configuration
logging.level.root=INFO
logging.level.org.springframework.web=INFO
logging.level.org.hibernate=ERROR
logging.level.iym=DEBUG
logging.level.com.zaxxer.hikari.HikariConfig=ERROR
logging.level.com.zaxxer.hikari=ERROR

# Swagger configuration
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.operationsSorter=method
springdoc.swagger-ui.tagsSorter=alpha
springdoc.swagger-ui.disable-swagger-default-url=true
springdoc.swagger-ui.enabled=true
springdoc.api-docs.enabled=true

# Application specific properties
app.init-db=true

# JWT Configuration
app.jwtSecret=${JWT_SECRET:mysecretkeyasdRET567-asdHd-52sdauer-dfgZSDfas5sCd34df-123dFTH56HG-asd45FgbsdDd334-aasd456fdvb}
app.jwtExpirationInSec=${JWT_EXPIRATION_SEC:86400}

# CORS Configuration
cors.allowed.origins=${CORS_ALLOWED_ORIGINS:http://localhost:3000,http://localhost:4000,http://127.0.0.1:3000,http://127.0.0.1:4000}

makos.api.base-url=${MAKOS_API_BASE_URL:http://localhost:5000/makosapi}
makos.api.username=${MAKOS_API_USERNAME:makos_admin}
makos.api.password=${MAKOS_API_PASSWORD:123456}
makos.api.connect-timeout=${MAKOS_API_CONNECT_TIMEOUT:5000}
makos.api.read-timeout=${MAKOS_API_READ_TIMEOUT:30000}
