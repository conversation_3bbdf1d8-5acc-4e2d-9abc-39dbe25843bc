package iym.common.model.entity.postgresql;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;

/**
 * PostgreSQL version of Iller entity
 * This entity is specifically configured for PostgreSQL database
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode

@Entity(name = "IllerPostgreSQL")
@Table(name = "iller")
public class Iller implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "il_kod", nullable = false, length = 4)
    @NotNull
    @Size(max = 4)
    private String ilKod;

    @Column(name = "il_adi", length = 50)
    @Size(max = 50)
    private String ilAdi;

    @Column(name = "ilce_adi", length = 50)
    @Size(max = 50)
    private String ilceAdi;

    @Column(name = "created_at")
    @Temporal(TemporalType.TIMESTAMP)
    private java.util.Date createdAt;

    @Column(name = "updated_at")
    @Temporal(TemporalType.TIMESTAMP)
    private java.util.Date updatedAt;

    @PrePersist
    protected void onCreate() {
        createdAt = new java.util.Date();
        updatedAt = new java.util.Date();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = new java.util.Date();
    }
}
