package iym.makos.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * DTO for HedeflerTalep entity
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Hedefler Talep bilgilerini içerir")
public class HedeflerTalepDTO {

    @Schema(description = "Hedefler talep ID")
    private Long id;

    @Schema(description = "Birim kodu")
    private Long birimKod;

    @Schema(description = "Kullanıcı ID")
    private Long kullaniciId;

    @Schema(description = "Tek masa kullanıcı ID")
    private Long tekMasaKulId;

    @Schema(description = "Hedef numarası", example = "HEDEF-001")
    @Size(max = 100, message = "Hedef numarası 100 karakterden fazla olamaz")
    private String hedefNo;

    @Schema(description = "Hedef adı", example = "Test Hedef")
    @Size(max = 100, message = "Hedef adı 100 karakterden fazla olamaz")
    private String hedefAdi;

    @Schema(description = "Hedef soyadı", example = "Test Soyad")
    @Size(max = 100, message = "Hedef soyadı 100 karakterden fazla olamaz")
    private String hedefSoyadi;

    @Schema(description = "Başlama tarihi", example = "2023-05-15T10:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "Europe/Istanbul")
    private Date baslamaTarihi;

    @Schema(description = "Süresi")
    private Long suresi;

    @Schema(description = "Süre tipi")
    private Long sureTipi;

    @Schema(description = "Uzatma sayısı")
    private Long uzatmaSayisi;

    @Schema(description = "Durumu", example = "AKTIF")
    @Size(max = 100, message = "Durumu 100 karakterden fazla olamaz")
    private String durumu;

    @Schema(description = "Açıklama")
    @Size(max = 250, message = "Açıklama 250 karakterden fazla olamaz")
    private String aciklama;

    @Schema(description = "Mahkeme karar ID")
    private Long mahkemeKararId;

    @Schema(description = "Hedef aidiyet ID")
    private Long hedefAidiyatId;

    @Schema(description = "Grup kodu")
    private Long grupKod;

    @Schema(description = "Aidiyet kodu", example = "AIDIYAT-01")
    @Size(max = 35, message = "Aidiyet kodu 35 karakterden fazla olamaz")
    private String aidiyatKod;

    @Schema(description = "Unique kod")
    private Long uniqKod;

    @Schema(description = "Kayıt tarihi", example = "2023-05-15T10:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "Europe/Istanbul")
    private Date kayitTarihi;

    @Schema(description = "Tanımlama tarihi", example = "2023-05-15T10:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "Europe/Istanbul")
    private Date tanimlamaTarihi;

    @Schema(description = "Kapatma karar ID")
    private Long kapatmaKararId;

    @Schema(description = "Kapatma tarihi", example = "2023-05-15T10:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "Europe/Istanbul")
    private Date kapatmaTarihi;

    @Schema(description = "İmha", example = "EVET")
    @Size(max = 20, message = "İmha 20 karakterden fazla olamaz")
    private String imha;

    @Schema(description = "İmha tarihi", example = "2023-05-15T10:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "Europe/Istanbul")
    private Date imhaTarihi;

    @Schema(description = "Uzatma ID")
    private Long uzatmaId;

    @Schema(description = "Açılmı", example = "E")
    @Size(max = 1, message = "Açılmı 1 karakterden fazla olamaz")
    private String acilmi;

    @Schema(description = "Hedef 118 adı", example = "Test 118 Adı")
    @Size(max = 50, message = "Hedef 118 adı 50 karakterden fazla olamaz")
    private String hedef118Adi;

    @Schema(description = "Hedef 118 soyadı", example = "Test 118 Soyadı")
    @Size(max = 50, message = "Hedef 118 soyadı 50 karakterden fazla olamaz")
    private String hedef118Soyadi;

    @Schema(description = "Hedef 118 adres", example = "Test 118 Adres")
    @Size(max = 250, message = "Hedef 118 adres 250 karakterden fazla olamaz")
    private String hedef118Adres;

    @Schema(description = "Hedef tipi")
    private Long hedefTipi;

    @Schema(description = "Çanak numarası", example = "CANAK-001")
    @Size(max = 100, message = "Çanak numarası 100 karakterden fazla olamaz")
    private String canakNo;
}
