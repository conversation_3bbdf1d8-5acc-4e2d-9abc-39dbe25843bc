package iym.common.model.entity.iym;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;

/**
 * Entity class for MAHKEME_KODU_DETAY_TALEP table
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode

@Entity(name = "MahkemeKoduDetayTalep")
@Table(name = "MAHKEME_KODU_DETAY_TALEP")
public class MahkemeKoduDetayTalep implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "MAHKEME_KODU_DETAY_TALEP_SEQ")
    @SequenceGenerator(name = "MAHKEME_KODU_DETAY_TALEP_SEQ", sequenceName = "MAHKEME_KODU_DETAY_TALEP_SEQ", allocationSize = 1)
    private Long id;

    @Column(name = "MAHKEME_KARAR_DETAY_ID", nullable = false)
    @NotNull
    private Long mahkemeKararDetayId;

    @Column(name = "MAHKEME_KODU", nullable = false, length = 25)
    @NotNull
    @Size(max = 25)
    private String mahkemeKodu;

    @Column(name = "MAHKEME_ADI", length = 250)
    @NotNull
    @Size(max = 250)
    private String mahkemeAdi;

    @Column(name = "DURUMU", length = 10)
    @Size(max = 10)
    private String durumu;

}
