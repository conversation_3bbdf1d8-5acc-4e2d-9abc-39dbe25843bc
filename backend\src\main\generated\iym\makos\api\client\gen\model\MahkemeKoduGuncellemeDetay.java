/*
 * MAKOS OpenAPI definition
 * MAKOS Application
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package iym.makos.api.client.gen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import iym.makos.api.client.gen.model.MahkemeKararDetay;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * Güncelleme yapılacak mahkeme karar bilgisi ve karara ait güncellenecek yeni kod/il bilgileri
 */
@JsonPropertyOrder({
  MahkemeKoduGuncellemeDetay.JSON_PROPERTY_MAHKEME_KARAR_DETAY,
  MahkemeKoduGuncellemeDetay.JSON_PROPERTY_YENI_MAHKEME_KODU
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class MahkemeKoduGuncellemeDetay {
  public static final String JSON_PROPERTY_MAHKEME_KARAR_DETAY = "mahkemeKararDetay";
  private MahkemeKararDetay mahkemeKararDetay;

  public static final String JSON_PROPERTY_YENI_MAHKEME_KODU = "yeniMahkemeKodu";
  private String yeniMahkemeKodu;

  public MahkemeKoduGuncellemeDetay() {
  }

  public MahkemeKoduGuncellemeDetay mahkemeKararDetay(MahkemeKararDetay mahkemeKararDetay) {
    
    this.mahkemeKararDetay = mahkemeKararDetay;
    return this;
  }

   /**
   * Get mahkemeKararDetay
   * @return mahkemeKararDetay
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_DETAY)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public MahkemeKararDetay getMahkemeKararDetay() {
    return mahkemeKararDetay;
  }


  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_DETAY)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setMahkemeKararDetay(MahkemeKararDetay mahkemeKararDetay) {
    this.mahkemeKararDetay = mahkemeKararDetay;
  }


  public MahkemeKoduGuncellemeDetay yeniMahkemeKodu(String yeniMahkemeKodu) {
    
    this.yeniMahkemeKodu = yeniMahkemeKodu;
    return this;
  }

   /**
   * Get yeniMahkemeKodu
   * @return yeniMahkemeKodu
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_YENI_MAHKEME_KODU)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getYeniMahkemeKodu() {
    return yeniMahkemeKodu;
  }


  @JsonProperty(JSON_PROPERTY_YENI_MAHKEME_KODU)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setYeniMahkemeKodu(String yeniMahkemeKodu) {
    this.yeniMahkemeKodu = yeniMahkemeKodu;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    MahkemeKoduGuncellemeDetay mahkemeKoduGuncellemeDetay = (MahkemeKoduGuncellemeDetay) o;
    return Objects.equals(this.mahkemeKararDetay, mahkemeKoduGuncellemeDetay.mahkemeKararDetay) &&
        Objects.equals(this.yeniMahkemeKodu, mahkemeKoduGuncellemeDetay.yeniMahkemeKodu);
  }

  @Override
  public int hashCode() {
    return Objects.hash(mahkemeKararDetay, yeniMahkemeKodu);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MahkemeKoduGuncellemeDetay {\n");
    sb.append("    mahkemeKararDetay: ").append(toIndentedString(mahkemeKararDetay)).append("\n");
    sb.append("    yeniMahkemeKodu: ").append(toIndentedString(yeniMahkemeKodu)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

