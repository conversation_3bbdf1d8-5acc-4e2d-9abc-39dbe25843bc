{"info": {"_postman_id": "quick-test-12345", "name": "IYM <PERSON> - Quick Test", "description": "Quick test collection for IYM Backend JWT Authentication\n\nThis is a simplified version for quick testing:\n1. Login with iym_admin\n2. Test protected endpoint\n\nCredentials:\n- Username: iym_admin\n- Password: 123456", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "1. <PERSON><PERSON> - <PERSON>Y<PERSON> Admin", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Login successful\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"JWT token received\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.token).to.exist;", "    pm.environment.set(\"jwt_token\", jsonData.token);", "    console.log(\"JWT Token stored:\", jsonData.token.substring(0, 50) + \"...\");", "});", "", "pm.test(\"User info received\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.username).to.eql(\"iym_admin\");", "    pm.expect(jsonData.roles).to.include(\"ROLE_ADMIN\");", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"iym_admin\",\n    \"password\": \"123456\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}, "description": "Login with pre-seeded iym_admin user"}, "response": []}, {"name": "2. Test Protected Endpoint", "event": [{"listen": "prerequest", "script": {"exec": ["if (!pm.environment.get(\"jwt_token\")) {", "    throw new Error(\"No JWT token found. Please run login first.\");", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"Protected endpoint accessible\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Authentication working\", function () {", "    // If we get here without 401, authentication is working", "    pm.expect(pm.response.code).to.not.eql(401);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"iym_admin\",\n    \"password\": \"newpassword123\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/changePassword", "host": ["{{baseUrl}}"], "path": ["auth", "changePassword"]}, "description": "Test protected endpoint with JWT token"}, "response": []}, {"name": "3. Test Anonymous Endpoint", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Anonymous access works\", function () {", "    // Should work without authentication", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Swagger accessible\", function () {", "    pm.expect(pm.response.text()).to.include(\"swagger\");", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api-docs", "host": ["{{baseUrl}}"], "path": ["api-docs"]}, "description": "Test anonymous access to API docs"}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["if (!pm.environment.get(\"baseUrl\")) {", "    pm.environment.set(\"baseUrl\", \"http://localhost:4000\");", "}"]}}], "variable": [{"key": "baseUrl", "value": "http://localhost:4000", "type": "string"}]}