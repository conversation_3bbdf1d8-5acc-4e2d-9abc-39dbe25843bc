# IYM Backend JWT Authentication - Postman Collection

Bu Postman collection, IYM Backend projesinin JWT authentication sistemini test etmek için hazırlanmıştır.

## 📁 Dosyalar

1. **`IYM_Backend_JWT_Authentication.postman_collection.json`** - Kapsamlı Postman collection
2. **`IYM_Quick_Test.postman_collection.json`** - Hızlı test için basit collection
3. **`IYM_Backend_Environment.postman_environment.json`** - Environment değişkenleri
4. **`POSTMAN_COLLECTION_README.md`** - Bu dosya

## 🚀 Kurulum

### 1. Postman'e Import Etme

1. Postman'i açın
2. **Import** butonuna tıklayın
3. **File** sekmesinden aşağıdaki dosyaları sırayla import edin:
   - `IYM_Backend_Environment.postman_environment.json` **(ÖNCELİKLE BU!)**
   - `IYM_Backend_JWT_Authentication.postman_collection.json`

### 2. Environment Seçimi

1. Postman'de sağ üst köşeden **"IYM Backend Environment"** environment'ını seçin
2. Environment değişkenlerinin doğru ayarlandığını kontrol edin

**⚠️ ÖNEMLİ**: JWT token'ların otomatik olarak kaydedilmesi için environment'ın mutlaka seçili olması gerekir!

### 3. Test Kullanıcıları

Backend uygulamanızı başlattıktan sonra:

**Mevcut Kullanıcı:**
- `iym_admin` kullanıcısı zaten veritabanında mevcut (şifre: `123456`)

**Yeni Test Kullanıcısı Oluşturma:**
- Collection'daki "Register New User" endpoint'ini kullanın

## 👥 Örnek Kullanıcılar

| Username | Password | Role | Açıklama |
|----------|----------|------|----------|
| `iym_admin` | `123456` | ROLE_ADMIN | **Veritabanında mevcut admin kullanıcısı** |

**Not**: `iym_admin` kullanıcısı zaten veritabanında BCrypt ile şifrelenmiş olarak mevcut (clear text şifre: `123456`).

## 📋 Collection İçeriği

### 🔐 Authentication
- **Register New User** - Yeni kullanıcı kaydı
- **Login - Get JWT Token** - JWT token alma (yeni kayıtlı kullanıcı)
- **Login - IYM Admin User** - Mevcut IYM admin kullanıcısı ile giriş

### 🛡️ Protected Endpoints
- **Change Password** - Şifre değiştirme (JWT token gerekli)

### 📚 API Documentation
- **Swagger UI** - Swagger arayüzü
- **API Docs JSON** - API dokümantasyonu JSON formatında

### ❤️ Health Check
- **Health Check - Public** - Sistem sağlık kontrolü

## 🔄 Kullanım Sırası

### 1. İlk Kurulum
```
1. Backend uygulamasını başlatın (http://localhost:4000)
2. iym_admin kullanıcısı zaten mevcut (username: iym_admin, password: 123456)
```

### Hızlı Test (Önerilen)
```
1. "IYM_Quick_Test.postman_collection.json" dosyasını import edin
2. Collection'ı çalıştırın (3 basit test)
3. Tüm testler yeşil olmalı
```

### 2. Authentication Test
```
1. "Login - IYM Admin User" endpoint'ini çalıştırın (mevcut kullanıcı)
   VEYA
   "Login - Get JWT Token" endpoint'ini çalıştırın (register sonrası)
2. Response'da dönen JWT token otomatik olarak environment'a kaydedilir
3. Artık protected endpoint'leri kullanabilirsiniz
```

### 3. Protected Endpoint Test
```
1. "Change Password" endpoint'ini çalıştırın
2. JWT token otomatik olarak Authorization header'ına eklenir
```

## 🔧 Environment Değişkenleri

| Değişken | Açıklama | Örnek Değer |
|----------|----------|-------------|
| `baseUrl` | Backend API base URL | `http://localhost:4000` |
| `jwt_token` | Login sonrası alınan JWT token | (otomatik doldurulur) |
| `admin_jwt_token` | Admin kullanıcısının JWT token'ı | (otomatik doldurulur) |
| `user_id` | Login olan kullanıcının ID'si | (otomatik doldurulur) |
| `username` | Login olan kullanıcının adı | (otomatik doldurulur) |
| `admin_username` | Admin kullanıcısı adı | `iym_admin` |
| `iym_admin_password` | IYM admin kullanıcısı şifresi | `123456` |
| `admin_user_id` | Admin kullanıcısının ID'si | (otomatik doldurulur) |

## 🧪 Test Senaryoları

### Senaryo 1: Başarılı Authentication (Mevcut Kullanıcı)
```
1. "Login - IYM Admin User" → 200 OK
2. JWT token environment'a kaydedilir
3. "Change Password" → 200 OK (JWT token ile)
```

### Senaryo 1b: Başarılı Authentication (Yeni Kullanıcı)
```
1. "Register New User" → 200 OK
2. "Login - Get JWT Token" → 200 OK
3. JWT token environment'a kaydedilir
4. "Change Password" → 200 OK (JWT token ile)
```

### Senaryo 2: Unauthorized Access
```
1. JWT token'ı environment'tan silin
2. "Change Password" → 401 Unauthorized
```

### Senaryo 3: Token Expiration Test
```
1. Login yapın ve token alın
2. Token'ın expire olmasını bekleyin (test env: 1 saat)
3. Protected endpoint'e istek atın → 401 Unauthorized
```

## 🔍 Troubleshooting

### Problem: 401 Unauthorized
**Çözüm:**
- Environment'ın seçili olduğunu kontrol edin (sağ üst köşe)
- JWT token'ın doğru şekilde environment'a kaydedildiğini kontrol edin
- Token'ın expire olmadığını kontrol edin
- Login endpoint'ini tekrar çalıştırın

### Problem: Connection Refused
**Çözüm:**
- Backend uygulamasının çalıştığını kontrol edin (http://localhost:4000)
- `baseUrl` environment değişkenini kontrol edin

### Problem: User Not Found
**Çözüm:**
- `iym_admin` kullanıcısının veritabanında olduğunu kontrol edin (otomatik seed edilir)
- Yeni kullanıcı için Register endpoint'ini kullanın

## 📝 Notlar

- **JWT token'lar otomatik olarak environment değişkenlerine kaydedilir** (environment seçili olmalı!)
- Her request için response time kontrolü yapılır (< 5000ms)
- Bearer token authentication otomatik olarak header'a eklenir
- Test script'leri response validation yapar
- **Environment import edilmeden collection çalıştırılırsa token'lar kaydedilmez**

## 🔗 Faydalı Linkler

- **Swagger UI**: http://localhost:4000/swagger-ui.html
- **API Docs**: http://localhost:4000/api-docs
- **Health Check**: http://localhost:4000/health/check

## 🆘 Destek

Herhangi bir sorun yaşarsanız:
1. Backend loglarını kontrol edin
2. Postman Console'u açın (View → Show Postman Console)
3. Network tab'ında request/response detaylarını inceleyin
