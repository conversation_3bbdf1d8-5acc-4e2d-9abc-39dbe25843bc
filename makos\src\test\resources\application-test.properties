# Test profile configuration for unit tests
# This profile is used for @SpringBootTest and other unit tests that don't need database

# Exclude database auto-configuration
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration,org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration,org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration,org.springframework.boot.autoconfigure.data.jpa.JpaRepositoriesAutoConfiguration

# Disable JPA completely
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=false

# Mock database properties (will be ignored due to exclusions above)
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=

# Logging configuration for tests
logging.level.root=WARN
logging.level.org.springframework.web=INFO
logging.level.iym=INFO
logging.level.com.zaxxer.hikari=ERROR

# Disable security for tests
spring.security.user.name=test
spring.security.user.password=test
