# Test profile configuration for unit tests
# This profile is used for @WebMvcTest and other unit tests that don't need database

# Exclude database auto-configuration
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration,org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration,org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration,org.springframework.boot.autoconfigure.data.jpa.JpaRepositoriesAutoConfiguration

# Disable JPA
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=false

# Logging configuration for tests
logging.level.root=WARN
logging.level.org.springframework.web=INFO
logging.level.iym=INFO
logging.level.com.zaxxer.hikari=ERROR

# Disable security for tests
spring.security.user.name=test
spring.security.user.password=test
