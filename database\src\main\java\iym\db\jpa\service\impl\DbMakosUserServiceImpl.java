package iym.db.jpa.service.impl;


import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.makos.MakosUser;
import iym.common.model.enums.MakosUserRoleType;
import iym.common.model.enums.UserStatusType;
import iym.common.service.db.DbMakosUserService;
import iym.common.util.ExceptionUtils;
import iym.db.jpa.dao.MakosUserRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;


@Service
public class DbMakosUserServiceImpl extends GenericDbServiceImpl<MakosUser, Long> implements DbMakosUserService {

    @Autowired
    public DbMakosUserServiceImpl(MakosUserRepo repository) {
        super(repository);
    }
    @Autowired
    @Lazy
    private PasswordEncoder passwordEncoder;

    @Override
    @Transactional(readOnly = true)
    public Optional<MakosUser> findByUsername(String name) {
        return getRepository().findByUsername(name);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MakosUser> findByStatus(UserStatusType status) {
        return getRepository().findByStatus(status);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MakosUser> findByRoleOrderByUsernameAsc(MakosUserRoleType role) {
        return getRepository().findByRoleOrderByUsernameAsc(role);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MakosUser> findAllByOrderByUsernameAsc() {
        return getRepository().findAllByOrderByUsernameAsc();
    }

    @Override
    @Transactional
    public void activateUser(String username) {
        MakosUser user = checkUser(username);
        user.setStatus(UserStatusType.ACTIVE);
        repository.save(user);
    }

    @Override
    @Transactional
    public void deactivateUser(String username) {
        MakosUser user = checkUser(username);
        user.setStatus(UserStatusType.PASSIVE);
        repository.save(user);
    }

    @Override
    @Transactional
    public void updateUser(MakosUser user) {
        MakosUser userDb = checkUser(user.getUsername());
        userDb.setRole(user.getRole());
        userDb.setKurum(user.getKurum());

        // Changing password is optional
        if(user.getNewPassword() != null ){
            // check if new password is different then old password
            if(passwordEncoder.matches(user.getNewPassword(), userDb.getPassword())) {
                throw ExceptionUtils.newBadRequest("Yeni şifre eskisiyle aynı olamaz.");
            }

            // update user password
            userDb.setPassword(passwordEncoder.encode(user.getNewPassword()));
        }

        repository.save(userDb);
    }

    private MakosUser checkUser(String username) {
        Optional<MakosUser> userOptional = getRepository().findByUsername(username);
        MakosUser user;

        if (userOptional.isEmpty()) {
            throw ExceptionUtils.USER_NOT_FOUND;
        } else {
            user = userOptional.get();
        }

        return user;
    }

    public MakosUserRepo getRepository() {
        return (MakosUserRepo) repository;
    }

}
