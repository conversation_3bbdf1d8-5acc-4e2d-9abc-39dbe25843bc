package iym.common.model.api;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum EvrakTuru {
	ILETISIMIN_TESPITI(0),
	ILETISIMIN_DENETLENMESI(1),
	GENEL_EVRAK(2);
	
	private final int evrakTuru;
	
	EvrakTuru(int evrakTuru){
		this.evrakTuru = evrakTuru;
	}

	@JsonValue
	public int getEvrakTuru(){
		return this.evrakTuru;
	}

	@JsonCreator
	public static EvrakTuru fromName(String name) {
		for (EvrakTuru evrakTuru : EvrakTuru.values()) {
			if (evrakTuru.name().equals(name)) {
				return evrakTuru;
			}
		}
		throw new IllegalArgumentException("Gecersiz evrakTuru: '" + name + "'");
	}

	//@JsonCreator
	public static EvrakTuru fromValue(int value) {
		for (EvrakTuru evrakTuru : EvrakTuru.values()) {
			if (evrakTuru.evrakTuru == value) {
				return evrakTuru;
			}
		}
		throw new IllegalArgumentException("Gecersiz evrakTuru: '" + value + "'");
	}
}
