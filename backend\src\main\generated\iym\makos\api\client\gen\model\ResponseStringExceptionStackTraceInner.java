/*
 * MAKOS OpenAPI definition
 * MAKOS Application
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package iym.makos.api.client.gen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * ResponseStringExceptionStackTraceInner
 */
@JsonPropertyOrder({
  ResponseStringExceptionStackTraceInner.JSON_PROPERTY_CLASS_LOADER_NAME,
  ResponseStringExceptionStackTraceInner.JSON_PROPERTY_MODULE_NAME,
  ResponseStringExceptionStackTraceInner.JSON_PROPERTY_MODULE_VERSION,
  ResponseStringExceptionStackTraceInner.JSON_PROPERTY_METHOD_NAME,
  ResponseStringExceptionStackTraceInner.JSON_PROPERTY_FILE_NAME,
  ResponseStringExceptionStackTraceInner.JSON_PROPERTY_LINE_NUMBER,
  ResponseStringExceptionStackTraceInner.JSON_PROPERTY_NATIVE_METHOD,
  ResponseStringExceptionStackTraceInner.JSON_PROPERTY_CLASS_NAME
})
@JsonTypeName("ResponseString_exception_stackTrace_inner")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class ResponseStringExceptionStackTraceInner {
  public static final String JSON_PROPERTY_CLASS_LOADER_NAME = "classLoaderName";
  private String classLoaderName;

  public static final String JSON_PROPERTY_MODULE_NAME = "moduleName";
  private String moduleName;

  public static final String JSON_PROPERTY_MODULE_VERSION = "moduleVersion";
  private String moduleVersion;

  public static final String JSON_PROPERTY_METHOD_NAME = "methodName";
  private String methodName;

  public static final String JSON_PROPERTY_FILE_NAME = "fileName";
  private String fileName;

  public static final String JSON_PROPERTY_LINE_NUMBER = "lineNumber";
  private Integer lineNumber;

  public static final String JSON_PROPERTY_NATIVE_METHOD = "nativeMethod";
  private Boolean nativeMethod;

  public static final String JSON_PROPERTY_CLASS_NAME = "className";
  private String className;

  public ResponseStringExceptionStackTraceInner() {
  }

  public ResponseStringExceptionStackTraceInner classLoaderName(String classLoaderName) {
    
    this.classLoaderName = classLoaderName;
    return this;
  }

   /**
   * Get classLoaderName
   * @return classLoaderName
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CLASS_LOADER_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getClassLoaderName() {
    return classLoaderName;
  }


  @JsonProperty(JSON_PROPERTY_CLASS_LOADER_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setClassLoaderName(String classLoaderName) {
    this.classLoaderName = classLoaderName;
  }


  public ResponseStringExceptionStackTraceInner moduleName(String moduleName) {
    
    this.moduleName = moduleName;
    return this;
  }

   /**
   * Get moduleName
   * @return moduleName
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MODULE_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getModuleName() {
    return moduleName;
  }


  @JsonProperty(JSON_PROPERTY_MODULE_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setModuleName(String moduleName) {
    this.moduleName = moduleName;
  }


  public ResponseStringExceptionStackTraceInner moduleVersion(String moduleVersion) {
    
    this.moduleVersion = moduleVersion;
    return this;
  }

   /**
   * Get moduleVersion
   * @return moduleVersion
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MODULE_VERSION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getModuleVersion() {
    return moduleVersion;
  }


  @JsonProperty(JSON_PROPERTY_MODULE_VERSION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setModuleVersion(String moduleVersion) {
    this.moduleVersion = moduleVersion;
  }


  public ResponseStringExceptionStackTraceInner methodName(String methodName) {
    
    this.methodName = methodName;
    return this;
  }

   /**
   * Get methodName
   * @return methodName
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_METHOD_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getMethodName() {
    return methodName;
  }


  @JsonProperty(JSON_PROPERTY_METHOD_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMethodName(String methodName) {
    this.methodName = methodName;
  }


  public ResponseStringExceptionStackTraceInner fileName(String fileName) {
    
    this.fileName = fileName;
    return this;
  }

   /**
   * Get fileName
   * @return fileName
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FILE_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getFileName() {
    return fileName;
  }


  @JsonProperty(JSON_PROPERTY_FILE_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFileName(String fileName) {
    this.fileName = fileName;
  }


  public ResponseStringExceptionStackTraceInner lineNumber(Integer lineNumber) {
    
    this.lineNumber = lineNumber;
    return this;
  }

   /**
   * Get lineNumber
   * @return lineNumber
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_LINE_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getLineNumber() {
    return lineNumber;
  }


  @JsonProperty(JSON_PROPERTY_LINE_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setLineNumber(Integer lineNumber) {
    this.lineNumber = lineNumber;
  }


  public ResponseStringExceptionStackTraceInner nativeMethod(Boolean nativeMethod) {
    
    this.nativeMethod = nativeMethod;
    return this;
  }

   /**
   * Get nativeMethod
   * @return nativeMethod
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NATIVE_METHOD)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getNativeMethod() {
    return nativeMethod;
  }


  @JsonProperty(JSON_PROPERTY_NATIVE_METHOD)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNativeMethod(Boolean nativeMethod) {
    this.nativeMethod = nativeMethod;
  }


  public ResponseStringExceptionStackTraceInner className(String className) {
    
    this.className = className;
    return this;
  }

   /**
   * Get className
   * @return className
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CLASS_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getClassName() {
    return className;
  }


  @JsonProperty(JSON_PROPERTY_CLASS_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setClassName(String className) {
    this.className = className;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ResponseStringExceptionStackTraceInner responseStringExceptionStackTraceInner = (ResponseStringExceptionStackTraceInner) o;
    return Objects.equals(this.classLoaderName, responseStringExceptionStackTraceInner.classLoaderName) &&
        Objects.equals(this.moduleName, responseStringExceptionStackTraceInner.moduleName) &&
        Objects.equals(this.moduleVersion, responseStringExceptionStackTraceInner.moduleVersion) &&
        Objects.equals(this.methodName, responseStringExceptionStackTraceInner.methodName) &&
        Objects.equals(this.fileName, responseStringExceptionStackTraceInner.fileName) &&
        Objects.equals(this.lineNumber, responseStringExceptionStackTraceInner.lineNumber) &&
        Objects.equals(this.nativeMethod, responseStringExceptionStackTraceInner.nativeMethod) &&
        Objects.equals(this.className, responseStringExceptionStackTraceInner.className);
  }

  @Override
  public int hashCode() {
    return Objects.hash(classLoaderName, moduleName, moduleVersion, methodName, fileName, lineNumber, nativeMethod, className);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ResponseStringExceptionStackTraceInner {\n");
    sb.append("    classLoaderName: ").append(toIndentedString(classLoaderName)).append("\n");
    sb.append("    moduleName: ").append(toIndentedString(moduleName)).append("\n");
    sb.append("    moduleVersion: ").append(toIndentedString(moduleVersion)).append("\n");
    sb.append("    methodName: ").append(toIndentedString(methodName)).append("\n");
    sb.append("    fileName: ").append(toIndentedString(fileName)).append("\n");
    sb.append("    lineNumber: ").append(toIndentedString(lineNumber)).append("\n");
    sb.append("    nativeMethod: ").append(toIndentedString(nativeMethod)).append("\n");
    sb.append("    className: ").append(toIndentedString(className)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

