package iym.makos.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * DTO for Gorevler2 entity
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Görevler bilgilerini içerir")
public class Gorevler2DTO {

    @Schema(description = "Görev adı", example = "Yönetici")
    @NotNull(message = "Görev adı boş olamaz")
    @Size(max = 75, message = "Görev adı 75 karakterden fazla olamaz")
    private String gorev;

    @Schema(description = "Görev kodu", example = "1")
    @NotNull(message = "Görev kodu boş olamaz")
    private Long gorevKodu;

    @Schema(description = "Görev imza adı", example = "Yönetici İmza")
    @Size(max = 100, message = "Görev imza adı 100 karakterden fazla olamaz")
    private String gorevImzaAdi;

    @Schema(description = "İmza yetkisi", example = "E")
    @Size(max = 1, message = "İmza yetkisi 1 karakterden fazla olamaz")
    private String imzaYetki;

    @Schema(description = "Silindi", example = "0")
    private Long silindi;

    @Schema(description = "Görev kodu 2", example = "YON")
    @Size(max = 100, message = "Görev kodu 2 100 karakterden fazla olamaz")
    private String gorevKodu2;

    @Schema(description = "Görev tipi", example = "Yönetim")
    @Size(max = 100, message = "Görev tipi 100 karakterden fazla olamaz")
    private String gorevTipi;

    @Schema(description = "Öncelik", example = "1")
    private Long oncelik;

    @Schema(description = "Başlama tarihi", example = "2023-01-01T00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "Europe/Istanbul")
    private Date baslamaTarihi;

    @Schema(description = "Bitiş tarihi", example = "2025-12-31T23:59:59")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "Europe/Istanbul")
    private Date bitisTarihi;
}
