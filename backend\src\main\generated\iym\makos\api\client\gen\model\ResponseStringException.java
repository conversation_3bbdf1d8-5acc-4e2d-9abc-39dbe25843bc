/*
 * MAKOS OpenAPI definition
 * MAKOS Application
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package iym.makos.api.client.gen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import iym.makos.api.client.gen.model.ResponseStringExceptionStackTraceInner;
import iym.makos.api.client.gen.model.ResponseStringExceptionSuppressedInner;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * ResponseStringException
 */
@JsonPropertyOrder({
  ResponseStringException.JSON_PROPERTY_STACK_TRACE,
  ResponseStringException.JSON_PROPERTY_MESSAGE,
  ResponseStringException.JSON_PROPERTY_SUPPRESSED,
  ResponseStringException.JSON_PROPERTY_LOCALIZED_MESSAGE
})
@JsonTypeName("ResponseString_exception")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class ResponseStringException {
  public static final String JSON_PROPERTY_STACK_TRACE = "stackTrace";
  private List<ResponseStringExceptionStackTraceInner> stackTrace;

  public static final String JSON_PROPERTY_MESSAGE = "message";
  private String message;

  public static final String JSON_PROPERTY_SUPPRESSED = "suppressed";
  private List<ResponseStringExceptionSuppressedInner> suppressed;

  public static final String JSON_PROPERTY_LOCALIZED_MESSAGE = "localizedMessage";
  private String localizedMessage;

  public ResponseStringException() {
  }

  public ResponseStringException stackTrace(List<ResponseStringExceptionStackTraceInner> stackTrace) {
    
    this.stackTrace = stackTrace;
    return this;
  }

  public ResponseStringException addStackTraceItem(ResponseStringExceptionStackTraceInner stackTraceItem) {
    if (this.stackTrace == null) {
      this.stackTrace = new ArrayList<>();
    }
    this.stackTrace.add(stackTraceItem);
    return this;
  }

   /**
   * Get stackTrace
   * @return stackTrace
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_STACK_TRACE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<ResponseStringExceptionStackTraceInner> getStackTrace() {
    return stackTrace;
  }


  @JsonProperty(JSON_PROPERTY_STACK_TRACE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setStackTrace(List<ResponseStringExceptionStackTraceInner> stackTrace) {
    this.stackTrace = stackTrace;
  }


  public ResponseStringException message(String message) {
    
    this.message = message;
    return this;
  }

   /**
   * Get message
   * @return message
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MESSAGE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getMessage() {
    return message;
  }


  @JsonProperty(JSON_PROPERTY_MESSAGE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMessage(String message) {
    this.message = message;
  }


  public ResponseStringException suppressed(List<ResponseStringExceptionSuppressedInner> suppressed) {
    
    this.suppressed = suppressed;
    return this;
  }

  public ResponseStringException addSuppressedItem(ResponseStringExceptionSuppressedInner suppressedItem) {
    if (this.suppressed == null) {
      this.suppressed = new ArrayList<>();
    }
    this.suppressed.add(suppressedItem);
    return this;
  }

   /**
   * Get suppressed
   * @return suppressed
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SUPPRESSED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<ResponseStringExceptionSuppressedInner> getSuppressed() {
    return suppressed;
  }


  @JsonProperty(JSON_PROPERTY_SUPPRESSED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSuppressed(List<ResponseStringExceptionSuppressedInner> suppressed) {
    this.suppressed = suppressed;
  }


  public ResponseStringException localizedMessage(String localizedMessage) {
    
    this.localizedMessage = localizedMessage;
    return this;
  }

   /**
   * Get localizedMessage
   * @return localizedMessage
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_LOCALIZED_MESSAGE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getLocalizedMessage() {
    return localizedMessage;
  }


  @JsonProperty(JSON_PROPERTY_LOCALIZED_MESSAGE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setLocalizedMessage(String localizedMessage) {
    this.localizedMessage = localizedMessage;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ResponseStringException responseStringException = (ResponseStringException) o;
    return Objects.equals(this.stackTrace, responseStringException.stackTrace) &&
        Objects.equals(this.message, responseStringException.message) &&
        Objects.equals(this.suppressed, responseStringException.suppressed) &&
        Objects.equals(this.localizedMessage, responseStringException.localizedMessage);
  }

  @Override
  public int hashCode() {
    return Objects.hash(stackTrace, message, suppressed, localizedMessage);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ResponseStringException {\n");
    sb.append("    stackTrace: ").append(toIndentedString(stackTrace)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("    suppressed: ").append(toIndentedString(suppressed)).append("\n");
    sb.append("    localizedMessage: ").append(toIndentedString(localizedMessage)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

