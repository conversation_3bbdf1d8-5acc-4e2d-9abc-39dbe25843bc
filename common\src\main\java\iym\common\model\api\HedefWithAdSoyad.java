package iym.common.model.api;

import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
@ToString
@EqualsAndHashCode
public class HedefWithAdSoyad {

    @NotNull
    private Hedef hedef;

    @NotNull
    private String hedefAd;

    @NotNull
    private String hedefSoyad;

}
