package iym.makos.config.security;


import iym.common.model.entity.makos.MakosUser;
import iym.common.service.db.DbMakosUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;


@Service
@Slf4j
public class UserDetailsServiceImpl implements UserDetailsService {

    private final DbMakosUserService userService;

    public UserDetailsServiceImpl(@Autowired DbMakosUserService userService) {
        this.userService = userService;
    }

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        try {

            log.info("Retrieving user details for {}", username);

            if (username != null && !username.isEmpty()) {
                MakosUser makosUser = userService.findByUsername(username)
                        .orElseThrow(() -> {
                            log.error("User not found: {}", username);
                            return new UsernameNotFoundException(username);
                        });

                return new UserDetailsImpl(makosUser);
            }
        } catch (Exception e) {
            log.error("Loading user failed:{}", username, e);
        }
        throw new UsernameNotFoundException("User not found: " + username);
    }

}
