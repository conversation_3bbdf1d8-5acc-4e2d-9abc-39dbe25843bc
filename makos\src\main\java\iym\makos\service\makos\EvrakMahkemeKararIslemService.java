package iym.makos.service.makos;

import iym.common.model.entity.iym.EvrakKayit;
import iym.common.model.entity.iym.EvrakMahkemeKararIslem;
import iym.common.service.db.DbEvrakKayitService;
import iym.common.service.db.DbEvrakMahkemeKararIslemService;
import iym.makos.dto.EvrakMahkemeKararIslemDTO;
import iym.makos.mapper.EvrakMahkemeKararIslemMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;
import java.util.Optional;

/**
 * Service for EvrakMahkemeKararIslem operations
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class EvrakMahkemeKararIslemService {

    private final DbEvrakMahkemeKararIslemService dbEvrakMahkemeKararIslemService;
    private final DbEvrakKayitService dbEvrakKayitService;
    private final EvrakMahkemeKararIslemMapper evrakMahkemeKararIslemMapper;

    /**
     * Get all evrak mahkeme karar islem records
     * @return List of EvrakMahkemeKararIslemDTO
     */
    @Transactional(readOnly = true)
    public List<EvrakMahkemeKararIslemDTO> findAll() {
        List<EvrakMahkemeKararIslem> evrakMahkemeKararIslemList = dbEvrakMahkemeKararIslemService.findAll();
        return evrakMahkemeKararIslemMapper.toDtoList(evrakMahkemeKararIslemList);
    }

    /**
     * Get all evrak mahkeme karar islem records with pagination
     * @param pageable Pagination information
     * @return Page of EvrakMahkemeKararIslemDTO
     */
    @Transactional(readOnly = true)
    public Page<EvrakMahkemeKararIslemDTO> findAll(Pageable pageable) {
        Page<EvrakMahkemeKararIslem> evrakMahkemeKararIslemPage = dbEvrakMahkemeKararIslemService.findAll(pageable);
        List<EvrakMahkemeKararIslemDTO> dtoList = evrakMahkemeKararIslemMapper.toDtoList(evrakMahkemeKararIslemPage.getContent());
        return new PageImpl<>(dtoList, pageable, evrakMahkemeKararIslemPage.getTotalElements());
    }

    /**
     * Get evrak mahkeme karar islem by evrak id
     * @param evrakId Evrak id
     * @return EvrakMahkemeKararIslemDTO
     */
    @Transactional(readOnly = true)
    public EvrakMahkemeKararIslemDTO findById(Long evrakId) {
        EvrakMahkemeKararIslem evrakMahkemeKararIslem = dbEvrakMahkemeKararIslemService.findById(evrakId)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Evrak mahkeme karar işlem bulunamadı: " + evrakId));
        return evrakMahkemeKararIslemMapper.toDto(evrakMahkemeKararIslem);
    }

    /**
     * Get evrak mahkeme karar islem by kurum
     * @param kurum Kurum
     * @return List of EvrakMahkemeKararIslemDTO
     */
    @Transactional(readOnly = true)
    public List<EvrakMahkemeKararIslemDTO> findByKurum(String kurum) {
        List<EvrakMahkemeKararIslem> evrakMahkemeKararIslemList = dbEvrakMahkemeKararIslemService.findByKurum(kurum);
        return evrakMahkemeKararIslemMapper.toDtoList(evrakMahkemeKararIslemList);
    }


    /**
     * Get evrak mahkeme karar islem by kurum and seviye
     * @param kurum Kurum
     * @param seviye Seviye
     * @return List of EvrakMahkemeKararIslemDTO
     */
    @Transactional(readOnly = true)
    public List<EvrakMahkemeKararIslemDTO> findByKurumAndSeviye(String kurum, String seviye) {
        List<EvrakMahkemeKararIslem> evrakMahkemeKararIslemList = dbEvrakMahkemeKararIslemService.findByKurumAndSeviye(kurum, seviye);
        return evrakMahkemeKararIslemMapper.toDtoList(evrakMahkemeKararIslemList);
    }

    /**
     * Create new evrak mahkeme karar islem
     * @param evrakMahkemeKararIslemDTO EvrakMahkemeKararIslemDTO
     * @return Created EvrakMahkemeKararIslemDTO
     */
    @Transactional
    public EvrakMahkemeKararIslemDTO create(EvrakMahkemeKararIslemDTO evrakMahkemeKararIslemDTO) {
        // Check if evrak exists
        Optional<EvrakKayit> evrakKayit = dbEvrakKayitService.findById(evrakMahkemeKararIslemDTO.getEvrakId());
        if (evrakKayit.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Evrak bulunamadı: " + evrakMahkemeKararIslemDTO.getEvrakId());
        }

        // Check if evrak mahkeme karar islem already exists
        Optional<EvrakMahkemeKararIslem> existingEvrakMahkemeKararIslem = dbEvrakMahkemeKararIslemService.findById(evrakMahkemeKararIslemDTO.getEvrakId());
        if (existingEvrakMahkemeKararIslem.isPresent()) {
            throw new ResponseStatusException(HttpStatus.CONFLICT, "Bu evrak için mahkeme karar işlem zaten mevcut: " + evrakMahkemeKararIslemDTO.getEvrakId());
        }

        // Set default seviye if not provided
        if (evrakMahkemeKararIslemDTO.getSeviye() == null) {
            evrakMahkemeKararIslemDTO.setSeviye("0");
        }

        // Set kurum from evrak if not provided
        if (evrakMahkemeKararIslemDTO.getKurum() == null) {
            evrakMahkemeKararIslemDTO.setKurum(evrakKayit.get().getEvrakGeldigiKurumKodu());
        }

        EvrakMahkemeKararIslem evrakMahkemeKararIslem = evrakMahkemeKararIslemMapper.toEntity(evrakMahkemeKararIslemDTO);
        dbEvrakMahkemeKararIslemService.save(evrakMahkemeKararIslem);
        log.info("Evrak mahkeme karar işlem oluşturuldu: {}", evrakMahkemeKararIslem.getEvrakId());
        return evrakMahkemeKararIslemMapper.toDto(evrakMahkemeKararIslem);
    }

    /**
     * Update existing evrak mahkeme karar islem
     * @param evrakId Evrak id
     * @param evrakMahkemeKararIslemDTO EvrakMahkemeKararIslemDTO
     * @return Updated EvrakMahkemeKararIslemDTO
     */
    @Transactional
    public EvrakMahkemeKararIslemDTO update(Long evrakId, EvrakMahkemeKararIslemDTO evrakMahkemeKararIslemDTO) {
        // Check if evrak id in path matches evrak id in dto
        if (!evrakId.equals(evrakMahkemeKararIslemDTO.getEvrakId())) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Evrak ID'leri eşleşmiyor");
        }

        EvrakMahkemeKararIslem existingEvrakMahkemeKararIslem = dbEvrakMahkemeKararIslemService.findById(evrakId)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Evrak mahkeme karar işlem bulunamadı: " + evrakId));

        EvrakMahkemeKararIslem updatedEvrakMahkemeKararIslem = evrakMahkemeKararIslemMapper.updateEntityFromDto(existingEvrakMahkemeKararIslem, evrakMahkemeKararIslemDTO);
        dbEvrakMahkemeKararIslemService.update(updatedEvrakMahkemeKararIslem);
        log.info("Evrak mahkeme karar işlem güncellendi: {}", updatedEvrakMahkemeKararIslem.getEvrakId());
        return evrakMahkemeKararIslemMapper.toDto(updatedEvrakMahkemeKararIslem);
    }

    /**
     * Delete evrak mahkeme karar islem
     * @param evrakId Evrak id
     */
    @Transactional
    public void delete(Long evrakId) {
        EvrakMahkemeKararIslem evrakMahkemeKararIslem = dbEvrakMahkemeKararIslemService.findById(evrakId)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Evrak mahkeme karar işlem bulunamadı: " + evrakId));
        dbEvrakMahkemeKararIslemService.delete(evrakMahkemeKararIslem);
        log.info("Evrak mahkeme karar işlem silindi: {}", evrakId);
    }
}
