package iym.makos.mapper;

import iym.common.model.entity.iym.HedeflerAidiyatTalep;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import iym.makos.dto.HedeflerAidiyatTalepDTO;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

class HedeflerAidiyatTalepMapperTest {

    private HedeflerAidiyatTalepMapper hedeflerAidiyatTalepMapper;
    private HedeflerAidiyatTalep hedeflerAidiyatTalep;
    private HedeflerAidiyatTalepDTO hedeflerAidiyatTalepDTO;
    private Date testDate;

    @BeforeEach
    void setUp() {
        hedeflerAidiyatTalepMapper = new HedeflerAidiyatTalepMapper();
        testDate = new Date();

        hedeflerAidiyatTalep = HedeflerAidiyatTalep.builder()
                .id(1L)
                .hedefTalepId(100L)
                .aidiyatKod("AIDIYAT1")
                .tarih(testDate)
                .kullaniciId(200L)
                .durumu("AKTIF")
                .build();

        hedeflerAidiyatTalepDTO = HedeflerAidiyatTalepDTO.builder()
                .id(1L)
                .hedefTalepId(100L)
                .aidiyatKod("AIDIYAT1")
                .tarih(testDate)
                .kullaniciId(200L)
                .durumu("AKTIF")
                .build();
    }

    @Test
    void toDto_shouldMapEntityToDto() {
        // When
        HedeflerAidiyatTalepDTO result = hedeflerAidiyatTalepMapper.toDto(hedeflerAidiyatTalep);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(hedeflerAidiyatTalep.getId());
        assertThat(result.getHedefTalepId()).isEqualTo(hedeflerAidiyatTalep.getHedefTalepId());
        assertThat(result.getAidiyatKod()).isEqualTo(hedeflerAidiyatTalep.getAidiyatKod());
        assertThat(result.getTarih()).isEqualTo(hedeflerAidiyatTalep.getTarih());
        assertThat(result.getKullaniciId()).isEqualTo(hedeflerAidiyatTalep.getKullaniciId());
        assertThat(result.getDurumu()).isEqualTo(hedeflerAidiyatTalep.getDurumu());
    }

    @Test
    void toDto_shouldReturnNullWhenEntityIsNull() {
        // When
        HedeflerAidiyatTalepDTO result = hedeflerAidiyatTalepMapper.toDto(null);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void toEntity_shouldMapDtoToEntity() {
        // When
        HedeflerAidiyatTalep result = hedeflerAidiyatTalepMapper.toEntity(hedeflerAidiyatTalepDTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(hedeflerAidiyatTalepDTO.getId());
        assertThat(result.getHedefTalepId()).isEqualTo(hedeflerAidiyatTalepDTO.getHedefTalepId());
        assertThat(result.getAidiyatKod()).isEqualTo(hedeflerAidiyatTalepDTO.getAidiyatKod());
        assertThat(result.getTarih()).isEqualTo(hedeflerAidiyatTalepDTO.getTarih());
        assertThat(result.getKullaniciId()).isEqualTo(hedeflerAidiyatTalepDTO.getKullaniciId());
        assertThat(result.getDurumu()).isEqualTo(hedeflerAidiyatTalepDTO.getDurumu());
    }

    @Test
    void toEntity_shouldReturnNullWhenDtoIsNull() {
        // When
        HedeflerAidiyatTalep result = hedeflerAidiyatTalepMapper.toEntity(null);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void updateEntityFromDto_shouldUpdateEntityWithDtoValues() {
        // Given
        HedeflerAidiyatTalep existingEntity = HedeflerAidiyatTalep.builder()
                .id(1L)
                .hedefTalepId(100L)
                .aidiyatKod("AIDIYAT1")
                .tarih(testDate)
                .kullaniciId(200L)
                .durumu("AKTIF")
                .build();

        HedeflerAidiyatTalepDTO updatedDto = HedeflerAidiyatTalepDTO.builder()
                .id(1L)
                .hedefTalepId(101L)
                .aidiyatKod("AIDIYAT2")
                .tarih(testDate)
                .kullaniciId(201L)
                .durumu("PASIF")
                .build();

        // When
        HedeflerAidiyatTalep result = hedeflerAidiyatTalepMapper.updateEntityFromDto(existingEntity, updatedDto);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(1L);
        assertThat(result.getHedefTalepId()).isEqualTo(101L);
        assertThat(result.getAidiyatKod()).isEqualTo("AIDIYAT2");
        assertThat(result.getTarih()).isEqualTo(testDate);
        assertThat(result.getKullaniciId()).isEqualTo(201L);
        assertThat(result.getDurumu()).isEqualTo("PASIF");
    }

    @Test
    void updateEntityFromDto_shouldReturnEntityWhenDtoIsNull() {
        // Given
        HedeflerAidiyatTalep existingEntity = HedeflerAidiyatTalep.builder()
                .id(1L)
                .hedefTalepId(100L)
                .aidiyatKod("AIDIYAT1")
                .tarih(testDate)
                .kullaniciId(200L)
                .durumu("AKTIF")
                .build();

        // When
        HedeflerAidiyatTalep result = hedeflerAidiyatTalepMapper.updateEntityFromDto(existingEntity, null);

        // Then
        assertThat(result).isEqualTo(existingEntity);
    }

    @Test
    void updateEntityFromDto_shouldReturnNullWhenEntityIsNull() {
        // When
        HedeflerAidiyatTalep result = hedeflerAidiyatTalepMapper.updateEntityFromDto(null, hedeflerAidiyatTalepDTO);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void toDtoList_shouldMapEntityListToDtoList() {
        // Given
        List<HedeflerAidiyatTalep> entityList = Arrays.asList(hedeflerAidiyatTalep, hedeflerAidiyatTalep);

        // When
        List<HedeflerAidiyatTalepDTO> result = hedeflerAidiyatTalepMapper.toDtoList(entityList);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(2);
        assertThat(result.get(0).getId()).isEqualTo(hedeflerAidiyatTalep.getId());
        assertThat(result.get(1).getId()).isEqualTo(hedeflerAidiyatTalep.getId());
    }

    @Test
    void toDtoList_shouldReturnNullWhenEntityListIsNull() {
        // When
        List<HedeflerAidiyatTalepDTO> result = hedeflerAidiyatTalepMapper.toDtoList(null);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void toEntityList_shouldMapDtoListToEntityList() {
        // Given
        List<HedeflerAidiyatTalepDTO> dtoList = Arrays.asList(hedeflerAidiyatTalepDTO, hedeflerAidiyatTalepDTO);

        // When
        List<HedeflerAidiyatTalep> result = hedeflerAidiyatTalepMapper.toEntityList(dtoList);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(2);
        assertThat(result.get(0).getId()).isEqualTo(hedeflerAidiyatTalepDTO.getId());
        assertThat(result.get(1).getId()).isEqualTo(hedeflerAidiyatTalepDTO.getId());
    }

    @Test
    void toEntityList_shouldReturnNullWhenDtoListIsNull() {
        // When
        List<HedeflerAidiyatTalep> result = hedeflerAidiyatTalepMapper.toEntityList(null);

        // Then
        assertThat(result).isNull();
    }
}
