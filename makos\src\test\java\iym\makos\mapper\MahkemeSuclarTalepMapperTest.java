package iym.makos.mapper;

import iym.common.model.entity.iym.MahkemeSuclarTalep;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import iym.makos.dto.MahkemeSuclarTalepDTO;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class MahkemeSuclarTalepMapperTest {

    private MahkemeSuclarTalepMapper mahkemeSuclarTalepMapper;
    private MahkemeSuclarTalep mahkemeSuclarTalep;
    private MahkemeSuclarTalepDTO mahkemeSuclarTalepDTO;

    @BeforeEach
    void setUp() {
        mahkemeSuclarTalepMapper = new MahkemeSuclarTalepMapper();

        mahkemeSuclarTalep = MahkemeSuclarTalep.builder()
                .id(1L)
                .mahkemeKararTalepId(100L)
                .sucTipKodu("01")
                .durumu("AKTIF")
                .build();

        mahkemeSuclarTalepDTO = MahkemeSuclarTalepDTO.builder()
                .id(1L)
                .mahkemeKararTalepId(100L)
                .sucTipKodu("01")
                .durumu("AKTIF")
                .build();
    }

    @Test
    void toDto_shouldMapEntityToDto() {
        // When
        MahkemeSuclarTalepDTO result = mahkemeSuclarTalepMapper.toDto(mahkemeSuclarTalep);

        // Then
        assertNotNull(result);
        assertEquals(mahkemeSuclarTalep.getId(), result.getId());
        assertEquals(mahkemeSuclarTalep.getMahkemeKararTalepId(), result.getMahkemeKararTalepId());
        assertEquals(mahkemeSuclarTalep.getSucTipKodu(), result.getSucTipKodu());
        assertEquals(mahkemeSuclarTalep.getDurumu(), result.getDurumu());
    }

    @Test
    void toDto_shouldReturnNullWhenEntityIsNull() {
        // When
        MahkemeSuclarTalepDTO result = mahkemeSuclarTalepMapper.toDto(null);

        // Then
        assertNull(result);
    }

    @Test
    void toEntity_shouldMapDtoToEntity() {
        // When
        MahkemeSuclarTalep result = mahkemeSuclarTalepMapper.toEntity(mahkemeSuclarTalepDTO);

        // Then
        assertNotNull(result);
        assertEquals(mahkemeSuclarTalepDTO.getId(), result.getId());
        assertEquals(mahkemeSuclarTalepDTO.getMahkemeKararTalepId(), result.getMahkemeKararTalepId());
        assertEquals(mahkemeSuclarTalepDTO.getSucTipKodu(), result.getSucTipKodu());
        assertEquals(mahkemeSuclarTalepDTO.getDurumu(), result.getDurumu());
    }

    @Test
    void toEntity_shouldReturnNullWhenDtoIsNull() {
        // When
        MahkemeSuclarTalep result = mahkemeSuclarTalepMapper.toEntity(null);

        // Then
        assertNull(result);
    }

    @Test
    void updateEntityFromDto_shouldUpdateEntityWithDtoValues() {
        // Given
        MahkemeSuclarTalep entity = MahkemeSuclarTalep.builder()
                .id(1L)
                .mahkemeKararTalepId(100L)
                .sucTipKodu("01")
                .durumu("AKTIF")
                .build();

        MahkemeSuclarTalepDTO dto = MahkemeSuclarTalepDTO.builder()
                .id(1L)
                .mahkemeKararTalepId(200L)
                .sucTipKodu("02")
                .durumu("PASIF")
                .build();

        // When
        MahkemeSuclarTalep result = mahkemeSuclarTalepMapper.updateEntityFromDto(entity, dto);

        // Then
        assertNotNull(result);
        assertEquals(entity.getId(), result.getId());
        assertEquals(dto.getMahkemeKararTalepId(), result.getMahkemeKararTalepId());
        assertEquals(dto.getSucTipKodu(), result.getSucTipKodu());
        assertEquals(dto.getDurumu(), result.getDurumu());
    }

    @Test
    void updateEntityFromDto_shouldReturnEntityWhenDtoIsNull() {
        // Given
        MahkemeSuclarTalep entity = MahkemeSuclarTalep.builder()
                .id(1L)
                .mahkemeKararTalepId(100L)
                .sucTipKodu("01")
                .durumu("AKTIF")
                .build();

        // When
        MahkemeSuclarTalep result = mahkemeSuclarTalepMapper.updateEntityFromDto(entity, null);

        // Then
        assertNotNull(result);
        assertEquals(entity, result);
    }

    @Test
    void updateEntityFromDto_shouldReturnNullWhenEntityIsNull() {
        // When
        MahkemeSuclarTalep result = mahkemeSuclarTalepMapper.updateEntityFromDto(null, mahkemeSuclarTalepDTO);

        // Then
        assertNull(result);
    }

    @Test
    void toDtoList_shouldMapEntityListToDtoList() {
        // Given
        List<MahkemeSuclarTalep> entities = Arrays.asList(
                MahkemeSuclarTalep.builder()
                        .id(1L)
                        .mahkemeKararTalepId(100L)
                        .sucTipKodu("01")
                        .durumu("A")
                        .build(),
                MahkemeSuclarTalep.builder()
                        .id(2L)
                        .mahkemeKararTalepId(200L)
                        .sucTipKodu("02")
                        .durumu("P")
                        .build()
        );

        // When
        List<MahkemeSuclarTalepDTO> result = mahkemeSuclarTalepMapper.toDtoList(entities);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(entities.get(0).getId(), result.get(0).getId());
        assertEquals(entities.get(0).getMahkemeKararTalepId(), result.get(0).getMahkemeKararTalepId());
        assertEquals(entities.get(0).getSucTipKodu(), result.get(0).getSucTipKodu());
        assertEquals(entities.get(0).getDurumu(), result.get(0).getDurumu());
        assertEquals(entities.get(1).getId(), result.get(1).getId());
        assertEquals(entities.get(1).getMahkemeKararTalepId(), result.get(1).getMahkemeKararTalepId());
        assertEquals(entities.get(1).getSucTipKodu(), result.get(1).getSucTipKodu());
        assertEquals(entities.get(1).getDurumu(), result.get(1).getDurumu());
    }

    @Test
    void toDtoList_shouldReturnEmptyListWhenEntityListIsNull() {
        // When
        List<MahkemeSuclarTalepDTO> result = mahkemeSuclarTalepMapper.toDtoList(null);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
}
