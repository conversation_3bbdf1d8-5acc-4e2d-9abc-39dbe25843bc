package iym.makos.model.api;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
@ToString
@EqualsAndHashCode
public class MahkemeKoduGuncellemeDetay {

  @NotNull
  @Schema(description = "Mahkeme kodu değişikliği yapılacak mahkeme karar bilgileri")
  private MahkemeKararDetay mahkemeKararDetay;

  @NotNull
  private String yeniMahkemeKodu;
}

