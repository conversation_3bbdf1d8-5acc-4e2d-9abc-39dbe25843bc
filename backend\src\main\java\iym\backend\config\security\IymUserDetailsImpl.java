package iym.backend.config.security;

import com.fasterxml.jackson.annotation.JsonIgnore;
import iym.common.model.api.KullaniciKurum;
import iym.common.model.entity.iym.IymUser;
import iym.common.model.enums.IymUserRoleType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.*;

/**
 * UserDetails implementation for IymUser entity
 * Used by Spring Security for authentication and authorization
 */
@ToString
public class IymUserDetailsImpl implements UserDetails {

    public static final String DEFAULT_ROLE_PREFIX = "ROLE_";

    @Getter
    private final UUID userId;
    private final String username;

    @JsonIgnore
    @ToString.Exclude
    private final String password;

    @Getter
    @Setter
    private String actingUserName;

    @Getter
    private final KullaniciKurum kurum;

    @Getter
    private IymUserRoleType userRole;

    private final Set<GrantedAuthority> grantedAuthorities = new HashSet<>();

    /**
     * Constructor from IymUser entity
     */
    public IymUserDetailsImpl(IymUser iymUser) {
        this.userId = iymUser.getId();
        this.username = iymUser.getUsername();
        this.password = iymUser.getPassword();
        this.kurum = iymUser.getKurum();
        this.userRole = iymUser.getRole();
        this.grantedAuthorities.add(new SimpleGrantedAuthority(iymUser.getRole().name()));
    }

    /**
     * Constructor with individual parameters
     */
    public IymUserDetailsImpl(UUID userId, String username, String password, KullaniciKurum kurum, IymUserRoleType userRole) {
        this.userId = userId;
        this.username = username;
        this.password = password;
        this.kurum = kurum;
        this.userRole = userRole;
        this.grantedAuthorities.add(new SimpleGrantedAuthority(userRole.name()));
    }

    /**
     * Update user role and authorities
     */
    public void updateUserRole(IymUserRoleType userRole) {
        // remove existing role
        this.grantedAuthorities.clear();

        // add new role
        this.grantedAuthorities.add(new SimpleGrantedAuthority(userRole.name()));
        this.userRole = userRole;
    }

    /**
     * Get user ID
     */
    public UUID getId() {
        return userId;
    }

    /**
     * Get authorities (roles)
     * Use hasAuthority() to check roles
     */
    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return grantedAuthorities;
    }

    @Override
    public String getPassword() {
        return password;
    }

    @Override
    public String getUsername() {
        return username;
    }

    /**
     * Get full username including acting user name if present
     */
    public String getFullUsername() {
        if (actingUserName != null && !actingUserName.isEmpty())
            return username + "-" + actingUserName;

        return getUsername();
    }

    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    public boolean isEnabled() {
        return true;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        IymUserDetailsImpl that = (IymUserDetailsImpl) o;
        return Objects.equals(userId, that.userId) && 
               Objects.equals(username, that.username) && 
               Objects.equals(actingUserName, that.actingUserName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(userId, getUsername(), getActingUserName());
    }

    /**
     * Check if user has specific role
     */
    public boolean hasRole(IymUserRoleType userRole) {
        if (userRole == null) {
            return false;
        }

        for (GrantedAuthority grantedAuthority : grantedAuthorities) {
            if (grantedAuthority.getAuthority().equals(userRole.name())) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if user has specific authority
     */
    public boolean hasAuthority(String authority) {
        if (authority == null || authority.trim().isEmpty()) {
            return false;
        }

        for (GrantedAuthority grantedAuthority : grantedAuthorities) {
            if (grantedAuthority.getAuthority().equals(authority)) {
                return true;
            }
        }

        return false;
    }
}
