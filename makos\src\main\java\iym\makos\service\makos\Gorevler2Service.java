package iym.makos.service.makos;

import iym.common.model.entity.iym.Gorevler2;
import iym.common.model.entity.iym.Gorevler2PK;
import iym.common.service.db.DbGorevler2Service;
import iym.makos.dto.Gorevler2DTO;
import iym.makos.mapper.Gorevler2Mapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.util.Date;
import java.util.List;

/**
 * Service for Gorevler2 operations
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class Gorevler2Service {

    private final DbGorevler2Service dbGorevler2Service;
    private final Gorevler2Mapper gorevler2Mapper;

    /**
     * Get all Gorevler2 records
     * @return List of Gorevler2DTO
     */
    public List<Gorevler2DTO> findAll() {
        List<Gorevler2> gorevler2List = dbGorevler2Service.findAll();
        return gorevler2Mapper.toDtoList(gorevler2List);
    }

    /**
     * Get all Gorevler2 records with pagination
     * @param pageable Pagination information
     * @return Page of Gorevler2DTO
     */
    public Page<Gorevler2DTO> findAll(Pageable pageable) {
        Page<Gorevler2> gorevler2Page = dbGorevler2Service.findAll(pageable);
        List<Gorevler2DTO> dtoList = gorevler2Mapper.toDtoList(gorevler2Page.getContent());
        return new PageImpl<>(dtoList, pageable, gorevler2Page.getTotalElements());
    }

    /**
     * Get Gorevler2 by gorev and gorevKodu
     * @param gorev Gorev
     * @param gorevKodu Gorev kodu
     * @return Gorevler2DTO
     */
    public Gorevler2DTO findByGorevAndGorevKodu(String gorev, Long gorevKodu) {
        Gorevler2 gorevler2 = dbGorevler2Service.findByGorevAndGorevKodu(gorev, gorevKodu)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Görev bulunamadı: " + gorev + ", " + gorevKodu));
        return gorevler2Mapper.toDto(gorevler2);
    }

    /**
     * Get Gorevler2 by id
     * @param id Gorevler2PK
     * @return Gorevler2DTO
     */
    public Gorevler2DTO findById(Gorevler2PK id) {
        Gorevler2 gorevler2 = dbGorevler2Service.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Görev bulunamadı: " + id.getGorev() + ", " + id.getGorevKodu()));
        return gorevler2Mapper.toDto(gorevler2);
    }

    /**
     * Get Gorevler2 by gorev
     * @param gorev Gorev
     * @return List of Gorevler2DTO
     */
    public List<Gorevler2DTO> findByGorev(String gorev) {
        List<Gorevler2> gorevler2List = dbGorevler2Service.findByGorev(gorev);
        return gorevler2Mapper.toDtoList(gorevler2List);
    }

    /**
     * Get Gorevler2 by gorevKodu
     * @param gorevKodu Gorev kodu
     * @return List of Gorevler2DTO
     */
    public List<Gorevler2DTO> findByGorevKodu(Long gorevKodu) {
        List<Gorevler2> gorevler2List = dbGorevler2Service.findByGorevKodu(gorevKodu);
        return gorevler2Mapper.toDtoList(gorevler2List);
    }

    /**
     * Get Gorevler2 by gorevImzaAdi
     * @param gorevImzaAdi Gorev imza adı
     * @return List of Gorevler2DTO
     */
    public List<Gorevler2DTO> findByGorevImzaAdi(String gorevImzaAdi) {
        List<Gorevler2> gorevler2List = dbGorevler2Service.findByGorevImzaAdi(gorevImzaAdi);
        return gorevler2Mapper.toDtoList(gorevler2List);
    }

    /**
     * Get Gorevler2 by imzaYetki
     * @param imzaYetki İmza yetkisi
     * @return List of Gorevler2DTO
     */
    public List<Gorevler2DTO> findByImzaYetki(String imzaYetki) {
        List<Gorevler2> gorevler2List = dbGorevler2Service.findByImzaYetki(imzaYetki);
        return gorevler2Mapper.toDtoList(gorevler2List);
    }

    /**
     * Get Gorevler2 by silindi
     * @param silindi Silindi
     * @return List of Gorevler2DTO
     */
    public List<Gorevler2DTO> findBySilindi(Long silindi) {
        List<Gorevler2> gorevler2List = dbGorevler2Service.findBySilindi(silindi);
        return gorevler2Mapper.toDtoList(gorevler2List);
    }

    /**
     * Get Gorevler2 by gorevKodu2
     * @param gorevKodu2 Gorev kodu 2
     * @return List of Gorevler2DTO
     */
    public List<Gorevler2DTO> findByGorevKodu2(String gorevKodu2) {
        List<Gorevler2> gorevler2List = dbGorevler2Service.findByGorevKodu2(gorevKodu2);
        return gorevler2Mapper.toDtoList(gorevler2List);
    }

    /**
     * Get Gorevler2 by gorevTipi
     * @param gorevTipi Gorev tipi
     * @return List of Gorevler2DTO
     */
    public List<Gorevler2DTO> findByGorevTipi(String gorevTipi) {
        List<Gorevler2> gorevler2List = dbGorevler2Service.findByGorevTipi(gorevTipi);
        return gorevler2Mapper.toDtoList(gorevler2List);
    }

    /**
     * Get Gorevler2 by oncelik
     * @param oncelik Öncelik
     * @return List of Gorevler2DTO
     */
    public List<Gorevler2DTO> findByOncelik(Long oncelik) {
        List<Gorevler2> gorevler2List = dbGorevler2Service.findByOncelik(oncelik);
        return gorevler2Mapper.toDtoList(gorevler2List);
    }

    /**
     * Get Gorevler2 by baslamaTarihi between
     * @param startDate Start date
     * @param endDate End date
     * @return List of Gorevler2DTO
     */
    public List<Gorevler2DTO> findByBaslamaTarihiBetween(Date startDate, Date endDate) {
        List<Gorevler2> gorevler2List = dbGorevler2Service.findByBaslamaTarihiBetween(startDate, endDate);
        return gorevler2Mapper.toDtoList(gorevler2List);
    }

    /**
     * Get Gorevler2 by bitisTarihi between
     * @param startDate Start date
     * @param endDate End date
     * @return List of Gorevler2DTO
     */
    public List<Gorevler2DTO> findByBitisTarihiBetween(Date startDate, Date endDate) {
        List<Gorevler2> gorevler2List = dbGorevler2Service.findByBitisTarihiBetween(startDate, endDate);
        return gorevler2Mapper.toDtoList(gorevler2List);
    }

    /**
     * Get Gorevler2 by date range
     * @param date Date to check
     * @return List of Gorevler2DTO
     */
    public List<Gorevler2DTO> findByDateRange(Date date) {
        List<Gorevler2> gorevler2List = dbGorevler2Service.findByBaslamaTarihiLessThanEqualAndBitisTarihiGreaterThanEqual(date, date);
        return gorevler2Mapper.toDtoList(gorevler2List);
    }

    /**
     * Search Gorevler2 by gorev
     * @param gorev Gorev
     * @return List of Gorevler2DTO
     */
    public List<Gorevler2DTO> findByGorevContainingIgnoreCase(String gorev) {
        List<Gorevler2> gorevler2List = dbGorevler2Service.findByGorevContainingIgnoreCase(gorev);
        return gorevler2Mapper.toDtoList(gorevler2List);
    }

    /**
     * Search Gorevler2 by gorevImzaAdi
     * @param gorevImzaAdi Gorev imza adı
     * @return List of Gorevler2DTO
     */
    public List<Gorevler2DTO> findByGorevImzaAdiContainingIgnoreCase(String gorevImzaAdi) {
        List<Gorevler2> gorevler2List = dbGorevler2Service.findByGorevImzaAdiContainingIgnoreCase(gorevImzaAdi);
        return gorevler2Mapper.toDtoList(gorevler2List);
    }

    /**
     * Get Gorevler2 by gorevTipi and imzaYetki
     * @param gorevTipi Gorev tipi
     * @param imzaYetki İmza yetkisi
     * @return List of Gorevler2DTO
     */
    public List<Gorevler2DTO> findByGorevTipiAndImzaYetki(String gorevTipi, String imzaYetki) {
        List<Gorevler2> gorevler2List = dbGorevler2Service.findByGorevTipiAndImzaYetki(gorevTipi, imzaYetki);
        return gorevler2Mapper.toDtoList(gorevler2List);
    }

    /**
     * Get Gorevler2 by gorevTipi and silindi
     * @param gorevTipi Gorev tipi
     * @param silindi Silindi
     * @return List of Gorevler2DTO
     */
    public List<Gorevler2DTO> findByGorevTipiAndSilindi(String gorevTipi, Long silindi) {
        List<Gorevler2> gorevler2List = dbGorevler2Service.findByGorevTipiAndSilindi(gorevTipi, silindi);
        return gorevler2Mapper.toDtoList(gorevler2List);
    }

    /**
     * Check if Gorevler2 exists by gorev and gorevKodu
     * @param gorev Gorev
     * @param gorevKodu Gorev kodu
     * @return true if exists, false otherwise
     */
    public boolean existsByGorevAndGorevKodu(String gorev, Long gorevKodu) {
        return dbGorevler2Service.existsByGorevAndGorevKodu(gorev, gorevKodu);
    }

    /**
     * Create new Gorevler2
     * @param gorevler2DTO Gorevler2DTO
     * @return Created Gorevler2DTO
     */
    public Gorevler2DTO create(Gorevler2DTO gorevler2DTO) {
        // Check if already exists
        if (dbGorevler2Service.existsByGorevAndGorevKodu(gorevler2DTO.getGorev(), gorevler2DTO.getGorevKodu())) {
            throw new ResponseStatusException(HttpStatus.CONFLICT, "Görev zaten mevcut: " + gorevler2DTO.getGorev() + ", " + gorevler2DTO.getGorevKodu());
        }

        // Set default silindi if not provided
        if (gorevler2DTO.getSilindi() == null) {
            gorevler2DTO.setSilindi(0L);
        }

        Gorevler2 gorevler2 = gorevler2Mapper.toEntity(gorevler2DTO);
        dbGorevler2Service.save(gorevler2);
        log.info("Görev oluşturuldu: {}, {}", gorevler2.getGorev(), gorevler2.getGorevKodu());
        return gorevler2Mapper.toDto(gorevler2);
    }

    /**
     * Update existing Gorevler2
     * @param gorev Gorev
     * @param gorevKodu Gorev kodu
     * @param gorevler2DTO Gorevler2DTO
     * @return Updated Gorevler2DTO
     */
    public Gorevler2DTO update(String gorev, Long gorevKodu, Gorevler2DTO gorevler2DTO) {
        Gorevler2 existingGorevler2 = dbGorevler2Service.findByGorevAndGorevKodu(gorev, gorevKodu)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Görev bulunamadı: " + gorev + ", " + gorevKodu));

        // Ensure primary key fields are not changed
        gorevler2DTO.setGorev(gorev);
        gorevler2DTO.setGorevKodu(gorevKodu);

        Gorevler2 updatedGorevler2 = gorevler2Mapper.updateEntityFromDto(existingGorevler2, gorevler2DTO);
        dbGorevler2Service.update(updatedGorevler2);
        log.info("Görev güncellendi: {}, {}", updatedGorevler2.getGorev(), updatedGorevler2.getGorevKodu());
        return gorevler2Mapper.toDto(updatedGorevler2);
    }

    /**
     * Delete Gorevler2
     * @param gorev Gorev
     * @param gorevKodu Gorev kodu
     */
    public void delete(String gorev, Long gorevKodu) {
        Gorevler2PK id = new Gorevler2PK(gorev, gorevKodu);
        Gorevler2 gorevler2 = dbGorevler2Service.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Görev bulunamadı: " + gorev + ", " + gorevKodu));
        dbGorevler2Service.delete(gorevler2);
        log.info("Görev silindi: {}, {}", gorev, gorevKodu);
    }
}
