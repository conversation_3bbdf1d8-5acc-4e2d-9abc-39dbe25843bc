/*
 * MAKOS OpenAPI definition
 * MAKOS Application
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package iym.makos.api.client.gen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import iym.makos.api.client.gen.model.Hedef;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * CanakHedefDetay
 */
@JsonPropertyOrder({
  CanakHedefDetay.JSON_PROPERTY_HEDEF,
  CanakHedefDetay.JSON_PROPERTY_CANAK_HEDEF_NO
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class CanakHedefDetay {
  public static final String JSON_PROPERTY_HEDEF = "hedef";
  private Hedef hedef;

  public static final String JSON_PROPERTY_CANAK_HEDEF_NO = "canakHedefNo";
  private String canakHedefNo;

  public CanakHedefDetay() {
  }

  public CanakHedefDetay hedef(Hedef hedef) {
    
    this.hedef = hedef;
    return this;
  }

   /**
   * Get hedef
   * @return hedef
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_HEDEF)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public Hedef getHedef() {
    return hedef;
  }


  @JsonProperty(JSON_PROPERTY_HEDEF)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setHedef(Hedef hedef) {
    this.hedef = hedef;
  }


  public CanakHedefDetay canakHedefNo(String canakHedefNo) {
    
    this.canakHedefNo = canakHedefNo;
    return this;
  }

   /**
   * Get canakHedefNo
   * @return canakHedefNo
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_CANAK_HEDEF_NO)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getCanakHedefNo() {
    return canakHedefNo;
  }


  @JsonProperty(JSON_PROPERTY_CANAK_HEDEF_NO)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setCanakHedefNo(String canakHedefNo) {
    this.canakHedefNo = canakHedefNo;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CanakHedefDetay canakHedefDetay = (CanakHedefDetay) o;
    return Objects.equals(this.hedef, canakHedefDetay.hedef) &&
        Objects.equals(this.canakHedefNo, canakHedefDetay.canakHedefNo);
  }

  @Override
  public int hashCode() {
    return Objects.hash(hedef, canakHedefNo);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CanakHedefDetay {\n");
    sb.append("    hedef: ").append(toIndentedString(hedef)).append("\n");
    sb.append("    canakHedefNo: ").append(toIndentedString(canakHedefNo)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

