-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;

-- Sequence for EVRAK_KAYIT is already created in 01_create_schema.sql

-- Create EVRAK_KAYIT table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'EVRAK_KAYIT';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.EVRAK_KAYIT (
      ID NUMBER NOT NULL,
      EVRAK_SIRA_NO VARCHAR2(30 BYTE),
      EVRAK_NO VARCHAR2(50 BYTE),
      GIRIS_TARIH DATE NOT NULL,
      EVRAK_TARIHI DATE NOT NULL,
      EVRAK_GELDIGI_KURUM VARCHAR2(10 BYTE),
      KAY_KULLANICI NUMBER,
      EVRAK_TIPI VARCHAR2(10 BYTE),
      HAV<PERSON>E_BIRIM VARCHAR2(10 BYTE),
      ACIKLAMA VARCHAR2(4000 BYTE),
      GEL_IL VARCHAR2(4 BYTE),
      EVRAK_KONUSU VARCHAR2(200 BYTE),
      ARSIV_DOSYA_NO VARCHAR2(20 BYTE),
      DURUMU VARCHAR2(20 BYTE),
      EVRAK_YONU VARCHAR2(100 BYTE),
      ONAY_TARIHI DATE,
      ACILMI CHAR(1 BYTE),
      SORUSTURMA_NO VARCHAR2(20 BYTE),
      MAHKEME_KARAR_NO VARCHAR2(20 BYTE),
      UNIQ_COL NUMBER,
      ELDEN_TESLIM VARCHAR2(1 BYTE),
      TEKITMI CHAR(1 BYTE),
      ASIL_EVRAK CHAR(1 BYTE) DEFAULT ''H'',
      ONCELIK VARCHAR2(50 BYTE) DEFAULT NULL,
      EVRAK_GELDIGI_KURUM_ESKI VARCHAR2(10 BYTE),
      EVRAK_GRUP VARCHAR2(300 BYTE),
      ONAMA VARCHAR2(1 BYTE),
      CONSTRAINT EVRAK_KAYIT_PK PRIMARY KEY (ID)
    )';
  END IF;
END;
/

-- Create indexes for EVRAK_KAYIT if they don't exist
BEGIN
  -- Try to create EVRAK_NO_TARIH_KURUM_ILD_IDX
  BEGIN
    EXECUTE IMMEDIATE 'CREATE UNIQUE INDEX iym.EVRAK_NO_TARIH_KURUM_ILD_IDX ON iym.EVRAK_KAYIT (EVRAK_NO, EVRAK_TARIHI, GEL_IL, EVRAK_GELDIGI_KURUM, DURUMU)';
  EXCEPTION
    WHEN OTHERS THEN
      IF SQLCODE = -1408 THEN -- ORA-01408: such column list already indexed
        NULL; -- Index already exists, ignore the error
      ELSE
        RAISE; -- Re-raise any other error
      END IF;
  END;

  -- Try to create EVRAK_SNO_IDX
  BEGIN
    EXECUTE IMMEDIATE 'CREATE UNIQUE INDEX iym.EVRAK_SNO_IDX ON iym.EVRAK_KAYIT (EVRAK_SIRA_NO)';
  EXCEPTION
    WHEN OTHERS THEN
      IF SQLCODE = -1408 THEN -- ORA-01408: such column list already indexed
        NULL; -- Index already exists, ignore the error
      ELSE
        RAISE; -- Re-raise any other error
      END IF;
  END;

  -- Try to create IDX_ID_GIRIS_TAR
  BEGIN
    EXECUTE IMMEDIATE 'CREATE INDEX iym.IDX_ID_GIRIS_TAR ON iym.EVRAK_KAYIT (ID, GIRIS_TARIH)';
  EXCEPTION
    WHEN OTHERS THEN
      IF SQLCODE = -1408 THEN -- ORA-01408: such column list already indexed
        NULL; -- Index already exists, ignore the error
      ELSE
        RAISE; -- Re-raise any other error
      END IF;
  END;
END;
/

-- Sequence for EVRAK_GELEN_KURUMLAR is already created in 01_create_schema.sql

-- Create EVRAK_GELEN_KURUMLAR table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'EVRAK_GELEN_KURUMLAR';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.EVRAK_GELEN_KURUMLAR (
      ID NUMBER NOT NULL,
      KURUM_KOD VARCHAR2(10 BYTE) NOT NULL,
      KURUM_ADI VARCHAR2(50 BYTE) NOT NULL,
      KURUM VARCHAR2(64 BYTE),
      IDX NUMBER,
      CONSTRAINT EVRAK_GELEN_KURUMLAR_PRM PRIMARY KEY (KURUM_KOD)
    )';
  END IF;
END;
/

-- Create ILLER table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'ILLER';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.ILLER (
      IL_KOD VARCHAR2(4 BYTE) NOT NULL,
      IL_ADI VARCHAR2(50 BYTE),
      ILCE_ADI VARCHAR2(50 BYTE),
      CONSTRAINT IL_KOD_IDX PRIMARY KEY (IL_KOD)
    )';
  END IF;
END;
/

-- Sequence for KULLANICILAR is already created in 01_create_schema.sql

-- Create KULLANICILAR table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'KULLANICILAR';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.KULLANICILAR (
      ID NUMBER NOT NULL,
      ADI VARCHAR2(20 BYTE) NOT NULL,
      SOYADI VARCHAR2(40 BYTE) NOT NULL,
      KULLANICI_ADI VARCHAR2(20 BYTE) NOT NULL,
      SIFRE CHAR(32 BYTE) NOT NULL,
      GOREVI VARCHAR2(6 BYTE),
      YETKI NUMBER,
      BIRIMI VARCHAR2(4 BYTE),
      GOREV_TANIMI VARCHAR2(300 BYTE),
      TEL VARCHAR2(16 BYTE),
      POSTA VARCHAR2(55 BYTE),
      FAX VARCHAR2(55 BYTE),
      RESMI_KOD VARCHAR2(12 BYTE),
      KIM_KUL_ID NUMBER,
      DURUMU VARCHAR2(20 BYTE),
      IMZA_YETKISI CHAR(1 BYTE),
      IMZA_DOSYASI VARCHAR2(50 BYTE),
      EKSTRAGUVENLIK VARCHAR2(1 BYTE),
      TEMSIL_EDILEN_KURUM VARCHAR2(2 BYTE),
      CID VARCHAR2(32 BYTE),
      TCK VARCHAR2(11 BYTE),
      PAROLA_DEGISIM_TARIHI DATE,
      AKADEMIK_UNVAN VARCHAR2(10 BYTE),
      GRUP_KODU VARCHAR2(10 BYTE),
      CONSTRAINT KULLANICI_ID_IDX PRIMARY KEY (ID)
    )';

    -- Create unique index on KULLANICI_ADI
    EXECUTE IMMEDIATE 'CREATE UNIQUE INDEX iym.KULLANICI_ADI ON iym.KULLANICILAR (KULLANICI_ADI ASC)';
  END IF;
END;
/

-- Create sequence for HEDEFLER_TALEP if it doesn't exist
DECLARE
  seq_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO seq_exists FROM user_sequences WHERE sequence_name = 'MAKOS_USER_SEQ';
  IF seq_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE SEQUENCE iym.MAKOS_USER_SEQ
      START WITH 1
      INCREMENT BY 1
      NOCACHE
      NOCYCLE';
  END IF;
END;
/


-- Create MAKOS_USER table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'MAKOS_USER';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.MAKOS_USER (
      ID NUMBER NOT NULL,
      USERNAME VARCHAR2(100 BYTE) NOT NULL,
      PASSWORD VARCHAR2(100 BYTE) NOT NULL,
      STATUS VARCHAR2(20 BYTE) NOT NULL,
      ROLE VARCHAR2(20 BYTE) NOT NULL,
      KURUM NUMBER,
      CONSTRAINT MAKOS_USER_ID_IDX PRIMARY KEY (ID),
      CONSTRAINT makos_user_un UNIQUE (USERNAME)
    )';
  END IF;
END;
/

-- Create indexes for MAKOS_USER if they don't exist
BEGIN
  -- Try to create index on STATUS for filtering active/inactive users
  BEGIN
    EXECUTE IMMEDIATE 'CREATE INDEX iym.MAKOS_USER_STATUS_IDX ON iym.MAKOS_USER (STATUS)';
  EXCEPTION
    WHEN OTHERS THEN
      IF SQLCODE = -1408 THEN -- ORA-01408: such column list already indexed
        NULL; -- Index already exists, ignore the error
      ELSE
        RAISE; -- Re-raise any other error
      END IF;
  END;

  -- Try to create index on KURUM for filtering by institution
  BEGIN
    EXECUTE IMMEDIATE 'CREATE INDEX iym.MAKOS_USER_KURUM_IDX ON iym.MAKOS_USER (KURUM)';
  EXCEPTION
    WHEN OTHERS THEN
      IF SQLCODE = -1408 THEN -- ORA-01408: such column list already indexed
        NULL; -- Index already exists, ignore the error
      ELSE
        RAISE; -- Re-raise any other error
      END IF;
  END;

  -- Try to create composite index on ROLE and STATUS for common queries
  BEGIN
    EXECUTE IMMEDIATE 'CREATE INDEX iym.MAKOS_USER_ROLE_STATUS_IDX ON iym.MAKOS_USER (ROLE, STATUS)';
  EXCEPTION
    WHEN OTHERS THEN
      IF SQLCODE = -1408 THEN -- ORA-01408: such column list already indexed
        NULL; -- Index already exists, ignore the error
      ELSE
        RAISE; -- Re-raise any other error
      END IF;
  END;
END;
/



-- Create KULLANICI_KURUMLAR table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'KULLANICI_KURUMLAR';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.KULLANICI_KURUMLAR (
      KURUM_KOD NUMBER,
      KURUM_AD VARCHAR2(100 BYTE),
      KURUM VARCHAR2(10 BYTE)
    )';
  END IF;
END;
/

-- Sequence for KULLANICI_KURUM is already created in 01_create_schema.sql

-- Create KULLANICI_KURUM table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'KULLANICI_KURUM';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.KULLANICI_KURUM (
      KULLANICI_ID NUMBER NOT NULL,
      KURUM_KOD VARCHAR2(20 BYTE) NOT NULL,
      ID NUMBER,
      CONSTRAINT KURUM_KULLANICI_IDX PRIMARY KEY (KULLANICI_ID, KURUM_KOD)
    )';
  END IF;
END;
/

-- Sequence for KULLANICI_GOREV2 is already created in 01_create_schema.sql

-- Create KULLANICI_GOREV2 table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'KULLANICI_GOREV2';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.KULLANICI_GOREV2 (
      ID NUMBER NOT NULL,
      KULLANICI_ID NUMBER NOT NULL,
      GOREV_ID NUMBER NOT NULL,
      VEKIL CHAR(1 BYTE) NOT NULL,
      TARIH DATE,
      DURUM CHAR(1 BYTE),
      TARIH2 DATE,
      ONCELIK NUMBER,
      CONSTRAINT K_GOREV21 PRIMARY KEY (ID)
    )';

    -- Create indexes for KULLANICI_GOREV2
    EXECUTE IMMEDIATE 'CREATE INDEX iym.IND_KULL_GOR_ID ON iym.KULLANICI_GOREV2 (KULLANICI_ID ASC)';
    EXECUTE IMMEDIATE 'CREATE INDEX iym.KULLANICI_GOREV_ID ON iym.KULLANICI_GOREV2 (GOREV_ID ASC)';
  END IF;
END;
/

-- Create MAKOS_USER_AUDIT_LOG table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'MAKOS_USER_AUDIT_LOG';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.MAKOS_USER_AUDIT_LOG (
      ID RAW(16) DEFAULT SYS_GUID() PRIMARY KEY,
      USER_AUDIT_TYPE VARCHAR2(100 BYTE) NOT NULL,
      USERNAME VARCHAR2(100 BYTE),
      ACTING_USERNAME VARCHAR2(100 BYTE),
      USER_IP VARCHAR2(100 BYTE),
      ADMIN_OPERATED_USERNAME VARCHAR2(100 BYTE),
      REQUEST_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
      RESPONSE_TIME TIMESTAMP,
      RESPONSE_CODE NUMBER
    )';
  END IF;
END;
/

-- Create indexes for MAKOS_USER_AUDIT_LOG if they don't exist
BEGIN
  -- Try to create index on USER_AUDIT_TYPE for filtering by audit type
  BEGIN
    EXECUTE IMMEDIATE 'CREATE INDEX iym.MAKOS_USER_AUDIT_LOG_TYPE_IDX ON iym.MAKOS_USER_AUDIT_LOG (USER_AUDIT_TYPE)';
  EXCEPTION
    WHEN OTHERS THEN
      IF SQLCODE = -1408 THEN -- ORA-01408: such column list already indexed
        NULL; -- Index already exists, ignore the error
      ELSE
        RAISE; -- Re-raise any other error
      END IF;
  END;

  -- Try to create index on USERNAME for filtering by user
  BEGIN
    EXECUTE IMMEDIATE 'CREATE INDEX iym.MAKOS_USER_AUDIT_LOG_USER_IDX ON iym.MAKOS_USER_AUDIT_LOG (USERNAME)';
  EXCEPTION
    WHEN OTHERS THEN
      IF SQLCODE = -1408 THEN -- ORA-01408: such column list already indexed
        NULL; -- Index already exists, ignore the error
      ELSE
        RAISE; -- Re-raise any other error
      END IF;
  END;

  -- Try to create index on REQUEST_TIME for time-based queries
  BEGIN
    EXECUTE IMMEDIATE 'CREATE INDEX iym.MAKOS_USER_AUDIT_LOG_TIME_IDX ON iym.MAKOS_USER_AUDIT_LOG (REQUEST_TIME)';
  EXCEPTION
    WHEN OTHERS THEN
      IF SQLCODE = -1408 THEN -- ORA-01408: such column list already indexed
        NULL; -- Index already exists, ignore the error
      ELSE
        RAISE; -- Re-raise any other error
      END IF;
  END;

  -- Try to create composite index on USERNAME and REQUEST_TIME for common queries
  BEGIN
    EXECUTE IMMEDIATE 'CREATE INDEX iym.MAKOS_USER_AUDIT_LOG_USER_TIME_IDX ON iym.MAKOS_USER_AUDIT_LOG (USERNAME, REQUEST_TIME)';
  EXCEPTION
    WHEN OTHERS THEN
      IF SQLCODE = -1408 THEN -- ORA-01408: such column list already indexed
        NULL; -- Index already exists, ignore the error
      ELSE
        RAISE; -- Re-raise any other error
      END IF;
  END;
END;
/


-- Create MAKOS_KARAR_REQUEST_LOG table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'MAKOS_KARAR_REQUEST_LOG';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.MAKOS_KARAR_REQUEST_LOG (
      ID RAW(16) DEFAULT SYS_GUID() PRIMARY KEY,
      REQUEST_ID RAW(16) NOT NULL,
      REQUEST_URL VARCHAR2(500 BYTE),
      REQUEST_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
      USERNAME VARCHAR2(100 BYTE),
      ACTING_USERNAME VARCHAR2(100 BYTE),
      USER_IP VARCHAR2(100 BYTE),
      TARGET_ID VARCHAR2(100 BYTE),
      EVRAK_NO VARCHAR2(50 BYTE),
      MAHKEME_KARAR_NO VARCHAR2(20 BYTE),
      SORUSTURMA_NO VARCHAR2(20 BYTE),
      REQUEST_BODY CLOB,
      RESPONSE_CODE NUMBER,
      RESPONSE_TIME TIMESTAMP,
      RESPONSE_BODY CLOB
    )';
  END IF;
END;
/

-- Create indexes for MAKOS_KARAR_REQUEST_LOG if they don't exist
BEGIN
  -- Try to create index on REQUEST_TIME for time-based queries
  BEGIN
    EXECUTE IMMEDIATE 'CREATE INDEX iym.MAKOS_KARAR_REQUEST_LOG_TIME_IDX ON iym.MAKOS_KARAR_REQUEST_LOG (REQUEST_TIME)';
  EXCEPTION
    WHEN OTHERS THEN
      IF SQLCODE = -1408 THEN -- ORA-01408: such column list already indexed
        NULL; -- Index already exists, ignore the error
      ELSE
        RAISE; -- Re-raise any other error
      END IF;
  END;

  -- Try to create index on USERNAME for filtering by user
  BEGIN
    EXECUTE IMMEDIATE 'CREATE INDEX iym.MAKOS_KARAR_REQUEST_LOG_USER_IDX ON iym.MAKOS_KARAR_REQUEST_LOG (USERNAME)';
  EXCEPTION
    WHEN OTHERS THEN
      IF SQLCODE = -1408 THEN -- ORA-01408: such column list already indexed
        NULL; -- Index already exists, ignore the error
      ELSE
        RAISE; -- Re-raise any other error
      END IF;
  END;

  -- Try to create index on MAHKEME_KARAR_NO for filtering by court decision number
  BEGIN
    EXECUTE IMMEDIATE 'CREATE INDEX iym.MAKOS_KARAR_REQUEST_LOG_KARAR_IDX ON iym.MAKOS_KARAR_REQUEST_LOG (MAHKEME_KARAR_NO)';
  EXCEPTION
    WHEN OTHERS THEN
      IF SQLCODE = -1408 THEN -- ORA-01408: such column list already indexed
        NULL; -- Index already exists, ignore the error
      ELSE
        RAISE; -- Re-raise any other error
      END IF;
  END;

  -- Try to create index on SORUSTURMA_NO for filtering by investigation number
  BEGIN
    EXECUTE IMMEDIATE 'CREATE INDEX iym.MAKOS_KARAR_REQUEST_LOG_SORUSTURMA_IDX ON iym.MAKOS_KARAR_REQUEST_LOG (SORUSTURMA_NO)';
  EXCEPTION
    WHEN OTHERS THEN
      IF SQLCODE = -1408 THEN -- ORA-01408: such column list already indexed
        NULL; -- Index already exists, ignore the error
      ELSE
        RAISE; -- Re-raise any other error
      END IF;
  END;

  -- Try to create composite index on USERNAME and REQUEST_TIME for common queries
  BEGIN
    EXECUTE IMMEDIATE 'CREATE INDEX iym.MAKOS_KARAR_REQUEST_LOG_USER_TIME_IDX ON iym.MAKOS_KARAR_REQUEST_LOG (USERNAME, REQUEST_TIME)';
  EXCEPTION
    WHEN OTHERS THEN
      IF SQLCODE = -1408 THEN -- ORA-01408: such column list already indexed
        NULL; -- Index already exists, ignore the error
      ELSE
        RAISE; -- Re-raise any other error
      END IF;
  END;
END;
/
