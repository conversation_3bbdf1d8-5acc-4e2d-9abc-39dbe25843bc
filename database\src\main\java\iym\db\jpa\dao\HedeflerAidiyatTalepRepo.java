package iym.db.jpa.dao;

import iym.common.model.entity.iym.HedeflerAidiyatTalep;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for HedeflerAidiyatTalep entity
 */
@Repository
public interface HedeflerAidiyatTalepRepo extends JpaRepository<HedeflerAidiyatTalep, Long> {

    Optional<HedeflerAidiyatTalep> findById(Long id);

    List<HedeflerAidiyatTalep> findByHedefTalepId(Long hedefTalepId);

    List<HedeflerAidiyatTalep> findByTarihBetween(Date startDate, Date endDate);
    
    Optional<HedeflerAidiyatTalep> findByHedefTalepIdAndAidiyatKod(Long hedefTalepId, String aidiyatKod);

}
