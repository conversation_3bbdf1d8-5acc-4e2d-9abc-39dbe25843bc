package iym.makos.processor;

import iym.common.validation.ValidationResult;
import iym.makos.config.security.UserDetailsImpl;
import iym.makos.dto.id.IDYeniKararRequest;
import iym.makos.dto.id.IDYeniKararResponse;
import iym.makos.model.MakosApiResponse;
import iym.makos.model.MakosResponseCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
@Slf4j
public class IDYeniKararRequestProcessor extends MakosRequestProcessorBase<IDYeniKararRequest, IDYeniKararResponse> {

    @Override
    public IDYeniKararResponse process(IDYeniKararRequest request, UserDetailsImpl islemYapanKullanici) {
        try {
            ValidationResult validationResult = requestValidator.validate(request);
            if (!validationResult.isValid()) {
                return IDYeniKararResponse.builder()
                        .response(MakosApiResponse.builder()
                                .responseCode(MakosResponseCode.INVALID_REQUEST)
                                .responseMessage(validationResult.getReasons().toString())
                                .build())
                        .build();
            }

            Date kayitTarihi = new Date();
            Long kaydedenKullaniciId = islemYapanKullanici.getId();
            Long mahkemeKararTalepId = requestSaver.handleDbSave(request, kayitTarihi, kaydedenKullaniciId);

            return IDYeniKararResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.SUCCESS)
                            .responseMessage(validationResult.getReasons().toString())
                            .build())
                    .btkEvrakId(mahkemeKararTalepId)
                    .build();

        } catch (Exception ex) {
            log.error("IDYeniKarar process failed. id:{}, evrakNo:{}", request.getId(), request.getEvrakDetay().getEvrakNo(), ex);
            return IDYeniKararResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("INTERNAL ERROR")
                            .build())
                    .build();
        }

    }
}
