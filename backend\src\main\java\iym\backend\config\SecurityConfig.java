package iym.backend.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;

/**
 * Security configuration for IYM Backend
 * Uses BCrypt password encoding for secure password storage
 */
@Configuration
public class SecurityConfig {

    /**
     * Password encoder bean using BCrypt
     * BCrypt is a secure password hashing function
     * @return BCryptPasswordEncoder
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
}
