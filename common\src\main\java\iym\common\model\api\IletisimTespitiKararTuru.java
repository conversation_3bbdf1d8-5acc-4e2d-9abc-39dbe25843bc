package iym.common.model.api;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum IletisimTespitiKararTuru {
	ILETISIMIN_TESPITI(0);

	private final int kararTuru;

	IletisimTespitiKararTuru(int kararTuru){
		this.kararTuru = kararTuru;
	}

	@JsonValue
	public int getKararTuru(){
		return this.kararTuru;
	}

	@JsonCreator
	public static IletisimTespitiKararTuru fromName(String name) {
		for (IletisimTespitiKararTuru kararTuru : IletisimTespitiKararTuru.values()) {
			if (kararTuru.name().equals(name)) {
				return kararTuru;
			}
		}
		throw new IllegalArgumentException("Gecersiz kararTuru: '" + name + "'");
	}

	//@JsonCreator
	public static IletisimTespitiKararTuru fromValue(int value) {
		for (IletisimTespitiKararTuru evrakTuru : IletisimTespitiKararTuru.values()) {
			if (evrakTuru.kararTuru == value) {
				return evrakTuru;
			}
		}
		throw new IllegalArgumentException("Gecersiz kararTuru: '" + value + "'");
	}
}
