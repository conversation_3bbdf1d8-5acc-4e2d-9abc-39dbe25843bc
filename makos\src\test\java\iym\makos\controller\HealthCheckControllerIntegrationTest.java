package iym.makos.controller;

import iym.common.service.db.DbIllerService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;

import static org.hamcrest.Matchers.containsString;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest
@AutoConfigureMockMvc
@ActiveProfiles("integration-test")
public class HealthCheckControllerIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    // Mock the DbIllerService to avoid dependency issues in integration tests
    @MockBean
    private DbIllerService dbIllerService;

    @BeforeEach
    void setUp() {
        System.out.println("Setting up integration test");
    }

    @Test
    void shouldReturnDefaultMessage() throws Exception {
        this.mockMvc.perform(get("/check/healthCheck"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().string(containsString(HealthCheckController.API_ALIVE_MESSAGE)));
    }
}
