package iym.common.model.entity.iym;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * Entity class for HEDEFLER_TALEP table
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode

@Entity(name = "HedeflerTalep")
@Table(name = "HEDEFLER_TALEP")
public class HedeflerTalep implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "HEDEFLER_TALEP_SEQ")
    @SequenceGenerator(name = "HEDEFLER_TALEP_SEQ", sequenceName = "HEDEFLER_TALEP_SEQ", allocationSize = 1)
    private Long id;

    @Column(name = "BIRIM_KOD")
    private Long birimKod;

    @Column(name = "KULLANICI_ID")
    private Long kullaniciId;

    @Column(name = "TEK_MASA_KUL_ID")
    private Long tekMasaKulId;

    @Column(name = "HEDEF_NO", length = 100)
    @Size(max = 100)
    private String hedefNo;

    @Column(name = "HEDEF_ADI", length = 100)
    @Size(max = 100)
    private String hedefAdi;

    @Column(name = "HEDEF_SOYADI", length = 100)
    @Size(max = 100)
    private String hedefSoyadi;

    @Column(name = "BASLAMA_TARIHI")
    @Temporal(TemporalType.TIMESTAMP)
    private Date baslamaTarihi;

    @Column(name = "SURESI")
    private Long suresi;

    @Column(name = "SURE_TIPI")
    private Long sureTipi;

    @Column(name = "UZATMA_SAYISI")
    private Long uzatmaSayisi;

    @Column(name = "DURUMU", length = 100)
    @Size(max = 100)
    private String durumu;

    @Column(name = "ACIKLAMA", length = 250)
    @Size(max = 250)
    private String aciklama;

    @Column(name = "MAHKEME_KARAR_ID")
    private Long mahkemeKararTalepId;

    @Column(name = "HEDEF_AIDIYAT_ID")
    private Long hedefAidiyatId;

    @Column(name = "GRUP_KOD")
    private Long grupKod;

    @Column(name = "AIDIYAT_KOD", length = 35)
    @Size(max = 35)
    private String aidiyatKod;

    @Column(name = "UNIQ_KOD")
    private Long uniqKod;

    @Column(name = "KAYIT_TARIHI")
    @Temporal(TemporalType.TIMESTAMP)
    private Date kayitTarihi;

    @Column(name = "TANIMLAMA_TARIHI")
    @Temporal(TemporalType.TIMESTAMP)
    private Date tanimlamaTarihi;

    @Column(name = "KAPATMA_KARAR_ID")
    private Long kapatmaKararId;

    @Column(name = "KAPATMA_TARIHI")
    @Temporal(TemporalType.TIMESTAMP)
    private Date kapatmaTarihi;

    @Column(name = "IMHA", length = 20)
    @Size(max = 20)
    private String imha;

    @Column(name = "IMHA_TARIHI")
    @Temporal(TemporalType.TIMESTAMP)
    private Date imhaTarihi;

    @Column(name = "UZATMA_ID")
    private Long uzatmaId;

    @Column(name = "ACILMI", length = 1, columnDefinition = "CHAR(1)")
    @Size(max = 1)
    private String acilmi;

    @Column(name = "HEDEF_118_ADI", length = 50)
    @Size(max = 50)
    private String hedef118Adi;

    @Column(name = "HEDEF_118_SOYADI", length = 50)
    @Size(max = 50)
    private String hedef118Soyadi;

    @Column(name = "HEDEF_118_ADRES", length = 250)
    @Size(max = 250)
    private String hedef118Adres;

    @Column(name = "HEDEF_TIPI")
    private Long hedefTipi;

    @Column(name = "CANAK_NO", length = 100)
    @Size(max = 100)
    private String canakNo;
}
