package iym.makos.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * DTO for EvrakKayit entity
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Evrak Kayıt bilgilerini içerir")
public class EvrakKayitDTO {

    @Schema(description = "Evrak kayıt ID")
    private Long id;

    @Schema(description = "Evrak sıra numarası", example = "2023-001")
    @Size(max = 30, message = "Evrak sıra no 30 karakterden fazla olamaz")
    private String evrakSiraNo;

    @Schema(description = "Evrak numarası", example = "E-2023-12345")
    @Size(max = 50, message = "Evrak No 50 karakterden fazla olamaz")
    private String evrakNo;

    @Schema(description = "Evrak giriş tarihi", example = "2023-05-15T10:30:00")
    @NotNull(message = "Giriş tarihi boş olamaz")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "Europe/Istanbul")
    private Date girisTarih;

    @Schema(description = "Evrak tarihi", example = "2023-05-10T14:00:00")
    @NotNull(message = "Evrak tarihi boş olamaz")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "Europe/Istanbul")
    private Date evrakTarihi;

    @Schema(description = "Evrak geldiği kurum kodu", example = "02")
    @Size(max = 10, message = "Evrak geldiği kurum 10 karakterden fazla olamaz")
    private String evrakGeldigiKurum;

    @Schema(description = "Kaydeden kullanıcı ID")
    private Long kayKullanici;

    @Schema(description = "Evrak tipi kodu", example = "ILETISIMIN_DENETLENMESI")
    @Size(max = 10, message = "Evrak tipi 10 karakterden fazla olamaz")
    private String evrakTipi;

    @Schema(description = "Havale birim kodu", example = "02")
    @Size(max = 10, message = "Havale birim 10 karakterden fazla olamaz")
    private String havaleBirim;

    @Schema(description = "Açıklama", example = "Örnek evrak açıklaması")
    @Size(max = 4000, message = "Açıklama 4000 karakterden fazla olamaz")
    private String aciklama;

    @Schema(description = "Geldiği il kodu", example = "0600")
    @Size(max = 4, message = "Geldiği il 4 karakterden fazla olamaz")
    private String gelIl;

    @Schema(description = "Evrak konusu", example = "İletişimin Denetlenmesi Talebi")
    @Size(max = 200, message = "Evrak konusu 200 karakterden fazla olamaz")
    private String evrakKonusu;

    @Schema(description = "Arşiv dosya numarası", example = "ARŞ-2023-001")
    @Size(max = 20, message = "Arşiv dosya no 20 karakterden fazla olamaz")
    private String arsivDosyaNo;

    @Schema(description = "Evrak durumu", example = "AKTIF")
    @Size(max = 20, message = "Durumu 20 karakterden fazla olamaz")
    private String durumu;

    @Schema(description = "Evrak yönü", example = "GELEN")
    @Size(max = 100, message = "Evrak yönü 100 karakterden fazla olamaz")
    private String evrakYonu;

    @Schema(description = "Onay tarihi", example = "2023-05-20T09:15:00")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "Europe/Istanbul")
    private Date onayTarihi;

    @Schema(description = "Açılmış mı? (E/H)", example = "H")
    @Size(max = 1, message = "Açılmı 1 karakterden fazla olamaz")
    private String acilmi;

    @Schema(description = "Soruşturma numarası", example = "SOR-2023-001")
    @Size(max = 20, message = "Soruşturma no 20 karakterden fazla olamaz")
    private String sorusturmaNo;

    @Schema(description = "Mahkeme karar numarası", example = "MK-2023-001")
    @Size(max = 20, message = "Mahkeme karar no 20 karakterden fazla olamaz")
    private String mahkemeKararNo;
}
