package iym.common.model.entity.postgresql;

import com.fasterxml.jackson.annotation.JsonIgnore;
import iym.common.model.api.KullaniciKurum;
import iym.common.model.enums.IymUserRoleType;
import iym.common.model.enums.UserStatusType;
import iym.common.util.db.KullaniciKurumConverter;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.util.UUID;

/**
 * PostgreSQL version of IymUser entity
 * This entity is specifically configured for PostgreSQL database
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode

@Entity(name = "IymUserPostgreSQL")
@Table(name = "iym_user", uniqueConstraints = { @UniqueConstraint(name = "iym_user_un", columnNames = { "username"}) })
public class IymUser implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    @Column(name = "username")
    @NotNull
    @NotBlank
    @Size(min = 4, max = 100)
    private String username;

    @Column(name = "password")
    @NotNull
    @NotBlank
    @Size(min = 5, max = 100)
    @JsonIgnore
    private String password;

    @Column(name = "status")
    @NotNull
    @Enumerated(EnumType.STRING)
    private UserStatusType status;

    @Column(name = "role")
    @NotNull
    @Enumerated(EnumType.STRING)
    private IymUserRoleType role;

    @Column(name = "kurum")
    @Convert(converter = KullaniciKurumConverter.class)
    private KullaniciKurum kurum;

    @Column(name = "created_at")
    @Temporal(TemporalType.TIMESTAMP)
    private java.util.Date createdAt;

    @Column(name = "updated_at")
    @Temporal(TemporalType.TIMESTAMP)
    private java.util.Date updatedAt;

    @PrePersist
    protected void onCreate() {
        createdAt = new java.util.Date();
        updatedAt = new java.util.Date();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = new java.util.Date();
    }
}
