package iym.db.jpa.service.impl;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.MahkemeSuclarTalep;
import iym.common.service.db.DbMahkemeSuclarTalepService;
import iym.db.jpa.dao.MahkemeSuclarTalepRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Service implementation for MahkemeSuclarTalep entity
 */
@Service
public class DbMahkemeSuclarTalepServiceImpl extends GenericDbServiceImpl<MahkemeSuclarTalep, Long> implements DbMahkemeSuclarTalepService {

    private final MahkemeSuclarTalepRepo mahkemeSuclarTalepRepo;

    @Autowired
    public DbMahkemeSuclarTalepServiceImpl(MahkemeSuclarTalepRepo repository) {
        super(repository);
        this.mahkemeSuclarTalepRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<MahkemeSuclarTalep> findById(Long id){
        return mahkemeSuclarTalepRepo.findById(id);
    }


    @Override
    @Transactional(readOnly = true)
    public List<MahkemeSuclarTalep> findByMahkemeKararTalepId(Long mahkemeKararTalepId) {
        return mahkemeSuclarTalepRepo.findByMahkemeKararTalepId(mahkemeKararTalepId);
    }



    @Override
    @Transactional(readOnly = true)
    public Optional<MahkemeSuclarTalep> findByMahkemeKararTalepIdAndSucTipKodu(Long mahkemeKararTalepId, String sucTipKod) {
        return mahkemeSuclarTalepRepo.findByMahkemeKararTalepIdAndSucTipKodu(mahkemeKararTalepId, sucTipKod);
    }
}
