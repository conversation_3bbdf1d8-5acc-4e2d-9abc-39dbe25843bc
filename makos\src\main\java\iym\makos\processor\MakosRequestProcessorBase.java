package iym.makos.processor;

import iym.common.validation.ValidationResult;
import iym.makos.handler.MahkemeKararDBSaveHandler;
import iym.makos.handler.MahkemeKararRequestDBSaveHandlerFactory;
import iym.makos.model.reqrep.MahkemeKararRequest;
import iym.makos.model.reqrep.MahkemeKararResponse;
import iym.makos.validation.IMahkemeKararRequestValidator;
import iym.makos.validation.MahkemeKararRequestValidatorFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.GenericTypeResolver;

import java.util.Objects;

public abstract class MakosRequestProcessorBase<T extends MahkemeKararRequest, R extends MahkemeKararResponse> implements IMakosRequestProcessor<T, R> {

    protected IMahkemeKararRequestValidator<T> requestValidator;

    protected MahkemeKararDBSaveHandler<T> requestSaver;


    @Autowired
    public final void setRequestValidator(MahkemeKararRequestValidatorFactory validatorFactory) {
        this.requestValidator = validatorFactory.getValidator(getRelatedRequestType());
    }

    @Autowired
    public final void setRequestSaver(MahkemeKararRequestDBSaveHandlerFactory saverFactory) {
        this.requestSaver = saverFactory.getHandler(getRelatedRequestType());
    }

    @SuppressWarnings("unchecked")
    public Class<T> getRelatedRequestType() {
        return (Class<T>) Objects.requireNonNull(GenericTypeResolver.resolveTypeArguments(this.getClass(), IMakosRequestProcessor.class))[0];
    }

    @SuppressWarnings("unchecked")
    public Class<R> getRelatedResponseType() {
        return (Class<R>) Objects.requireNonNull(GenericTypeResolver.resolveTypeArguments(this.getClass(), IMakosRequestProcessor.class))[1];
    }

}
