# Integration Test Environment Configuration for Makos
# H2 In-Memory Database for integration testing
spring.datasource.url=jdbc:h2:mem:makosintegrationtestdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect

# H2 Console for debugging tests (if needed)
spring.h2.console.enabled=true

# JPA configuration for integration tests
# Use schema.sql instead of Hibernate DDL to avoid Oracle-specific syntax issues
spring.jpa.hibernate.ddl-auto=none
spring.jpa.properties.hibernate.hbm2ddl.auto=none
spring.jpa.properties.hibernate.validator.apply_to_ddl=false
spring.jpa.properties.hibernate.temp.use_jdbc_metadata_defaults=false
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false

# Use schema.sql for H2-compatible table creation
spring.sql.init.mode=always
spring.sql.init.schema-locations=classpath:schema.sql

# H2 specific configuration to handle Oracle syntax
spring.jpa.properties.hibernate.globally_quoted_identifiers=false
spring.jpa.properties.hibernate.globally_quoted_identifiers_skip_column_definitions=true

# Connection pool configuration for integration tests
spring.datasource.hikari.connectionTimeout=20000
spring.datasource.hikari.maximumPoolSize=5

# Logging configuration for integration tests
logging.level.root=WARN
logging.level.org.springframework.web=INFO
logging.level.org.hibernate=WARN
logging.level.iym=INFO
logging.level.com.zaxxer.hikari.HikariConfig=ERROR
logging.level.com.zaxxer.hikari=ERROR

# Application specific properties for integration tests
app.init-db=false

# Swagger configuration
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.operationsSorter=method
springdoc.swagger-ui.tagsSorter=alpha
springdoc.swagger-ui.disable-swagger-default-url=true
