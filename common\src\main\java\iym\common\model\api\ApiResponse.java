package iym.common.model.api;

import iym.common.model.enums.ResponseCode;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
@ToString
@EqualsAndHashCode
public class ApiResponse {

    @NotNull
    @Valid
    private ResponseCode responseCode;

    private String responseMessage;
}
