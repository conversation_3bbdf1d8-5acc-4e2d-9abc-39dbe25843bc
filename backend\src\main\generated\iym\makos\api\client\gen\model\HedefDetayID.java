/*
 * MAKOS OpenAPI definition
 * MAKOS Application
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package iym.makos.api.client.gen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import iym.makos.api.client.gen.model.HedefWithAdSoyad;
import iym.makos.api.client.gen.model.MahkemeKararDetay;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * HedefDetayID
 */
@JsonPropertyOrder({
  HedefDetayID.JSON_PROPERTY_HEDEF_NO_AD_SOYAD,
  HedefDetayID.JSON_PROPERTY_BASLAMA_TARIHI,
  HedefDetayID.JSON_PROPERTY_SURE_TIP,
  HedefDetayID.JSON_PROPERTY_SURE,
  HedefDetayID.JSON_PROPERTY_ILGILI_MAHKEME_KARAR_DETAYI,
  HedefDetayID.JSON_PROPERTY_UZATMA_SAYISI,
  HedefDetayID.JSON_PROPERTY_HEDEF_AIDIYAT_KODLARI,
  HedefDetayID.JSON_PROPERTY_CANAK_NO
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class HedefDetayID {
  public static final String JSON_PROPERTY_HEDEF_NO_AD_SOYAD = "hedefNoAdSoyad";
  private HedefWithAdSoyad hedefNoAdSoyad;

  public static final String JSON_PROPERTY_BASLAMA_TARIHI = "baslamaTarihi";
  private LocalDateTime baslamaTarihi;

  /**
   * Gets or Sets sureTip
   */
  public enum SureTipEnum {
    _1("1"),
    
    _2("2"),
    
    _0("0");

    private String value;

    SureTipEnum(String value) {
      this.value = value;
    }

    @JsonValue
    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static SureTipEnum fromValue(String value) {
      for (SureTipEnum b : SureTipEnum.values()) {
        if (b.value.equals(value)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + value + "'");
    }
  }

  public static final String JSON_PROPERTY_SURE_TIP = "sureTip";
  private SureTipEnum sureTip;

  public static final String JSON_PROPERTY_SURE = "sure";
  private Integer sure;

  public static final String JSON_PROPERTY_ILGILI_MAHKEME_KARAR_DETAYI = "ilgiliMahkemeKararDetayi";
  private MahkemeKararDetay ilgiliMahkemeKararDetayi;

  public static final String JSON_PROPERTY_UZATMA_SAYISI = "uzatmaSayisi";
  private Integer uzatmaSayisi;

  public static final String JSON_PROPERTY_HEDEF_AIDIYAT_KODLARI = "hedefAidiyatKodlari";
  private List<String> hedefAidiyatKodlari;

  public static final String JSON_PROPERTY_CANAK_NO = "canakNo";
  private String canakNo;

  public HedefDetayID() {
  }

  public HedefDetayID hedefNoAdSoyad(HedefWithAdSoyad hedefNoAdSoyad) {
    
    this.hedefNoAdSoyad = hedefNoAdSoyad;
    return this;
  }

   /**
   * Get hedefNoAdSoyad
   * @return hedefNoAdSoyad
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_HEDEF_NO_AD_SOYAD)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public HedefWithAdSoyad getHedefNoAdSoyad() {
    return hedefNoAdSoyad;
  }


  @JsonProperty(JSON_PROPERTY_HEDEF_NO_AD_SOYAD)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setHedefNoAdSoyad(HedefWithAdSoyad hedefNoAdSoyad) {
    this.hedefNoAdSoyad = hedefNoAdSoyad;
  }


  public HedefDetayID baslamaTarihi(LocalDateTime baslamaTarihi) {
    
    this.baslamaTarihi = baslamaTarihi;
    return this;
  }

   /**
   * Get baslamaTarihi
   * @return baslamaTarihi
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_BASLAMA_TARIHI)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public LocalDateTime getBaslamaTarihi() {
    return baslamaTarihi;
  }


  @JsonProperty(JSON_PROPERTY_BASLAMA_TARIHI)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setBaslamaTarihi(LocalDateTime baslamaTarihi) {
    this.baslamaTarihi = baslamaTarihi;
  }


  public HedefDetayID sureTip(SureTipEnum sureTip) {
    
    this.sureTip = sureTip;
    return this;
  }

   /**
   * Get sureTip
   * @return sureTip
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_SURE_TIP)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public SureTipEnum getSureTip() {
    return sureTip;
  }


  @JsonProperty(JSON_PROPERTY_SURE_TIP)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setSureTip(SureTipEnum sureTip) {
    this.sureTip = sureTip;
  }


  public HedefDetayID sure(Integer sure) {
    
    this.sure = sure;
    return this;
  }

   /**
   * Get sure
   * @return sure
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_SURE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public Integer getSure() {
    return sure;
  }


  @JsonProperty(JSON_PROPERTY_SURE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setSure(Integer sure) {
    this.sure = sure;
  }


  public HedefDetayID ilgiliMahkemeKararDetayi(MahkemeKararDetay ilgiliMahkemeKararDetayi) {
    
    this.ilgiliMahkemeKararDetayi = ilgiliMahkemeKararDetayi;
    return this;
  }

   /**
   * Get ilgiliMahkemeKararDetayi
   * @return ilgiliMahkemeKararDetayi
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ILGILI_MAHKEME_KARAR_DETAYI)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public MahkemeKararDetay getIlgiliMahkemeKararDetayi() {
    return ilgiliMahkemeKararDetayi;
  }


  @JsonProperty(JSON_PROPERTY_ILGILI_MAHKEME_KARAR_DETAYI)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setIlgiliMahkemeKararDetayi(MahkemeKararDetay ilgiliMahkemeKararDetayi) {
    this.ilgiliMahkemeKararDetayi = ilgiliMahkemeKararDetayi;
  }


  public HedefDetayID uzatmaSayisi(Integer uzatmaSayisi) {
    
    this.uzatmaSayisi = uzatmaSayisi;
    return this;
  }

   /**
   * Uzatma Sayisi. Sadece uzatma kararlarinda gerekli
   * @return uzatmaSayisi
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_UZATMA_SAYISI)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getUzatmaSayisi() {
    return uzatmaSayisi;
  }


  @JsonProperty(JSON_PROPERTY_UZATMA_SAYISI)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setUzatmaSayisi(Integer uzatmaSayisi) {
    this.uzatmaSayisi = uzatmaSayisi;
  }


  public HedefDetayID hedefAidiyatKodlari(List<String> hedefAidiyatKodlari) {
    
    this.hedefAidiyatKodlari = hedefAidiyatKodlari;
    return this;
  }

  public HedefDetayID addHedefAidiyatKodlariItem(String hedefAidiyatKodlariItem) {
    if (this.hedefAidiyatKodlari == null) {
      this.hedefAidiyatKodlari = new ArrayList<>();
    }
    this.hedefAidiyatKodlari.add(hedefAidiyatKodlariItem);
    return this;
  }

   /**
   * Get hedefAidiyatKodlari
   * @return hedefAidiyatKodlari
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HEDEF_AIDIYAT_KODLARI)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<String> getHedefAidiyatKodlari() {
    return hedefAidiyatKodlari;
  }


  @JsonProperty(JSON_PROPERTY_HEDEF_AIDIYAT_KODLARI)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHedefAidiyatKodlari(List<String> hedefAidiyatKodlari) {
    this.hedefAidiyatKodlari = hedefAidiyatKodlari;
  }


  public HedefDetayID canakNo(String canakNo) {
    
    this.canakNo = canakNo;
    return this;
  }

   /**
   * Canak numarası. Sadece yeni kararda girilebilir. Zorunlu olmayan alan
   * @return canakNo
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CANAK_NO)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCanakNo() {
    return canakNo;
  }


  @JsonProperty(JSON_PROPERTY_CANAK_NO)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCanakNo(String canakNo) {
    this.canakNo = canakNo;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    HedefDetayID hedefDetayID = (HedefDetayID) o;
    return Objects.equals(this.hedefNoAdSoyad, hedefDetayID.hedefNoAdSoyad) &&
        Objects.equals(this.baslamaTarihi, hedefDetayID.baslamaTarihi) &&
        Objects.equals(this.sureTip, hedefDetayID.sureTip) &&
        Objects.equals(this.sure, hedefDetayID.sure) &&
        Objects.equals(this.ilgiliMahkemeKararDetayi, hedefDetayID.ilgiliMahkemeKararDetayi) &&
        Objects.equals(this.uzatmaSayisi, hedefDetayID.uzatmaSayisi) &&
        Objects.equals(this.hedefAidiyatKodlari, hedefDetayID.hedefAidiyatKodlari) &&
        Objects.equals(this.canakNo, hedefDetayID.canakNo);
  }

  @Override
  public int hashCode() {
    return Objects.hash(hedefNoAdSoyad, baslamaTarihi, sureTip, sure, ilgiliMahkemeKararDetayi, uzatmaSayisi, hedefAidiyatKodlari, canakNo);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class HedefDetayID {\n");
    sb.append("    hedefNoAdSoyad: ").append(toIndentedString(hedefNoAdSoyad)).append("\n");
    sb.append("    baslamaTarihi: ").append(toIndentedString(baslamaTarihi)).append("\n");
    sb.append("    sureTip: ").append(toIndentedString(sureTip)).append("\n");
    sb.append("    sure: ").append(toIndentedString(sure)).append("\n");
    sb.append("    ilgiliMahkemeKararDetayi: ").append(toIndentedString(ilgiliMahkemeKararDetayi)).append("\n");
    sb.append("    uzatmaSayisi: ").append(toIndentedString(uzatmaSayisi)).append("\n");
    sb.append("    hedefAidiyatKodlari: ").append(toIndentedString(hedefAidiyatKodlari)).append("\n");
    sb.append("    canakNo: ").append(toIndentedString(canakNo)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

