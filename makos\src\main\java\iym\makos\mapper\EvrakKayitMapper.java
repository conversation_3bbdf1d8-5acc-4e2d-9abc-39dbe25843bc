package iym.makos.mapper;

import iym.common.model.entity.iym.EvrakKayit;
import iym.common.util.CommonUtils;
import iym.makos.model.api.EvrakDetay;
import org.springframework.stereotype.Component;
import iym.makos.dto.EvrakKayitDTO;

import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper class for converting between EvrakKayit entity and DTO
 */
@Component
public class EvrakKayitMapper {

    /**
     * Convert entity to DTO
     * @param entity EvrakKayit entity
     * @return EvrakKayitDTO
     */
    public EvrakKayitDTO toDto(EvrakKayit entity) {
        if (entity == null) {
            return null;
        }

        return EvrakKayitDTO.builder()
                .id(entity.getId())
                .evrakSiraNo(entity.getEvrakSiraNo())
                .evrakNo(entity.getEvrakNo())
                .girisTarih(entity.getGirisTarih())
                .evrakTarihi(entity.getEvrakTarihi())
                .evrakGeldigiKurum(entity.getEvrakGeldigiKurumKodu())
                .kayKullanici(entity.getKayKullaniciId())
                .evrakTipi(entity.getEvrakTipi())
                .havaleBirim(entity.getHavaleBirim())
                .aciklama(entity.getAciklama())
                .gelIl(entity.getGeldigiIlIlceKodu())
                .evrakKonusu(entity.getEvrakKonusu())
                .arsivDosyaNo(entity.getArsivDosyaNo())
                .durumu(entity.getDurumu())
                .evrakYonu(entity.getEvrakYonu())
                .onayTarihi(entity.getOnayTarihi())
                .acilmi(entity.getAcilmi())
                .sorusturmaNo(entity.getSorusturmaNo())
                .mahkemeKararNo(entity.getMahkemeKararNo())
                .build();
    }

    public EvrakKayitDTO toDto2(EvrakDetay entity){
        if (entity == null) {
            return null;
        }

        return EvrakKayitDTO.builder()
                .id(0L)
                .evrakSiraNo("")
                .evrakNo(entity.getEvrakNo())
                .girisTarih(new Date())
                .evrakTarihi(CommonUtils.toDate(entity.getEvrakTarihi()))
                .evrakGeldigiKurum(entity.getEvrakKurumKodu())
                .kayKullanici(0L)
                .evrakTipi("")
                .havaleBirim(entity.getHavaleBirimi())
                .aciklama(entity.getAciklama())
                .gelIl(entity.getGeldigiIlIlceKodu())
                .evrakKonusu(entity.getEvrakKonusu())
                .arsivDosyaNo("")
                .durumu("")
                .evrakYonu("ILETISIMIN_DENETLENMESI")
                .onayTarihi(null)
                .acilmi(entity.isAcilmi() ? "E" : "H")
                .sorusturmaNo("")
                .mahkemeKararNo("")
                .build();

    }

    public EvrakKayit toEntity2(EvrakDetay dto) {
        if (dto == null) {
            return null;
        }

        return EvrakKayit.builder()
                .id(null)
                .evrakSiraNo(null)
                .evrakNo(dto.getEvrakNo())
                .girisTarih(new Date())
                .evrakTarihi(CommonUtils.toDate(dto.getEvrakTarihi()))
                .evrakGeldigiKurumKodu(dto.getEvrakKurumKodu())
                .kayKullaniciId(null)
                .evrakTipi(dto.getEvrakTuru().name())
                .havaleBirim(dto.getHavaleBirimi())
                .aciklama(dto.getAciklama())
                .geldigiIlIlceKodu(dto.getGeldigiIlIlceKodu())
                .evrakKonusu(dto.getEvrakKonusu())
                .durumu(null)
                .evrakYonu("ILETISIMIN DETENLENMESI")
                .acilmi(dto.isAcilmi() ? "E" : "H")
                .build();
    }

    /**
     * Convert DTO to entity
     * @param dto EvrakKayitDTO
     * @return EvrakKayit entity
     */
    public EvrakKayit toEntity(EvrakKayitDTO dto) {
        if (dto == null) {
            return null;
        }

        return EvrakKayit.builder()
                .id(dto.getId())
                .evrakSiraNo(dto.getEvrakSiraNo())
                .evrakNo(dto.getEvrakNo())
                .girisTarih(dto.getGirisTarih())
                .evrakTarihi(dto.getEvrakTarihi())
                .evrakGeldigiKurumKodu(dto.getEvrakGeldigiKurum())
                .kayKullaniciId(dto.getKayKullanici())
                .evrakTipi(dto.getEvrakTipi())
                .havaleBirim(dto.getHavaleBirim())
                .aciklama(dto.getAciklama())
                .geldigiIlIlceKodu(dto.getGelIl())
                .evrakKonusu(dto.getEvrakKonusu())
                .arsivDosyaNo(dto.getArsivDosyaNo())
                .durumu(dto.getDurumu())
                .evrakYonu(dto.getEvrakYonu())
                .onayTarihi(dto.getOnayTarihi())
                .acilmi(dto.getAcilmi())
                .sorusturmaNo(dto.getSorusturmaNo())
                .mahkemeKararNo(dto.getMahkemeKararNo())
                .build();
    }

    /**
     * Update entity from DTO
     * @param entity Existing EvrakKayit entity
     * @param dto EvrakKayitDTO with updated values
     * @return Updated EvrakKayit entity
     */
    public EvrakKayit updateEntityFromDto(EvrakKayit entity, EvrakKayitDTO dto) {
        if (entity == null || dto == null) {
            return entity;
        }

        entity.setEvrakSiraNo(dto.getEvrakSiraNo());
        entity.setEvrakNo(dto.getEvrakNo());
        entity.setGirisTarih(dto.getGirisTarih());
        entity.setEvrakTarihi(dto.getEvrakTarihi());
        entity.setEvrakGeldigiKurumKodu(dto.getEvrakGeldigiKurum());
        entity.setKayKullaniciId(dto.getKayKullanici());
        entity.setEvrakTipi(dto.getEvrakTipi());
        entity.setHavaleBirim(dto.getHavaleBirim());
        entity.setAciklama(dto.getAciklama());
        entity.setGeldigiIlIlceKodu(dto.getGelIl());
        entity.setEvrakKonusu(dto.getEvrakKonusu());
        entity.setArsivDosyaNo(dto.getArsivDosyaNo());
        entity.setDurumu(dto.getDurumu());
        entity.setEvrakYonu(dto.getEvrakYonu());
        entity.setOnayTarihi(dto.getOnayTarihi());
        entity.setAcilmi(dto.getAcilmi());
        entity.setSorusturmaNo(dto.getSorusturmaNo());
        entity.setMahkemeKararNo(dto.getMahkemeKararNo());

        return entity;
    }

    /**
     * Convert list of entities to list of DTOs
     * @param entities List of EvrakKayit entities
     * @return List of EvrakKayitDTOs
     */
    public List<EvrakKayitDTO> toDtoList(List<EvrakKayit> entities) {
        if (entities == null) {
            return null;
        }

        return entities.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }
}
