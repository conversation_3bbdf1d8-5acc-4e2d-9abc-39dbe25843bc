package iym.backend.config.security;

import iym.common.model.entity.iym.IymUser;
import iym.common.service.db.DbIymUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

/**
 * UserDetailsService implementation for IymUser
 * Used by Spring Security to load user details during authentication
 */
@Service
@Slf4j
public class IymUserDetailsServiceImpl implements UserDetailsService {

    private final DbIymUserService userService;

    public IymUserDetailsServiceImpl(@Autowired DbIymUserService userService) {
        this.userService = userService;
    }

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        try {
            log.info("Retrieving user details for {}", username);

            if (username != null && !username.isEmpty()) {
                IymUser iymUser = userService.findByUsername(username)
                        .orElseThrow(() -> {
                            log.error("User not found: {}", username);
                            return new UsernameNotFoundException(username);
                        });

                return new IymUserDetailsImpl(iymUser);
            }
        } catch (Exception e) {
            log.error("Loading user failed:{}", username, e);
        }
        throw new UsernameNotFoundException("User not found: " + username);
    }

    /**
     * Load user by username and return IymUserDetailsImpl directly
     * This method is useful when you need the specific implementation type
     */
    public IymUserDetailsImpl loadIymUserByUsername(String username) throws UsernameNotFoundException {
        return (IymUserDetailsImpl) loadUserByUsername(username);
    }
}
