/*
 * MAKOS OpenAPI definition
 * MAKOS Application
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package iym.makos.api.client.gen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import iym.makos.api.client.gen.model.MahkemeKararBilgisi;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * MahkemeKararID
 */
@JsonPropertyOrder({
  MahkemeKararID.JSON_PROPERTY_MAHKEME_KARAR_BILGISI,
  MahkemeKararID.JSON_PROPERTY_AIDIYAT_KODLARI,
  MahkemeKararID.JSON_PROPERTY_SUC_TIPI_KODLARI
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class MahkemeKararID {
  public static final String JSON_PROPERTY_MAHKEME_KARAR_BILGISI = "mahkemeKararBilgisi";
  private MahkemeKararBilgisi mahkemeKararBilgisi;

  public static final String JSON_PROPERTY_AIDIYAT_KODLARI = "aidiyatKodlari";
  private List<String> aidiyatKodlari;

  public static final String JSON_PROPERTY_SUC_TIPI_KODLARI = "sucTipiKodlari";
  private List<String> sucTipiKodlari;

  public MahkemeKararID() {
  }

  public MahkemeKararID mahkemeKararBilgisi(MahkemeKararBilgisi mahkemeKararBilgisi) {
    
    this.mahkemeKararBilgisi = mahkemeKararBilgisi;
    return this;
  }

   /**
   * Get mahkemeKararBilgisi
   * @return mahkemeKararBilgisi
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_BILGISI)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public MahkemeKararBilgisi getMahkemeKararBilgisi() {
    return mahkemeKararBilgisi;
  }


  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_BILGISI)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setMahkemeKararBilgisi(MahkemeKararBilgisi mahkemeKararBilgisi) {
    this.mahkemeKararBilgisi = mahkemeKararBilgisi;
  }


  public MahkemeKararID aidiyatKodlari(List<String> aidiyatKodlari) {
    
    this.aidiyatKodlari = aidiyatKodlari;
    return this;
  }

  public MahkemeKararID addAidiyatKodlariItem(String aidiyatKodlariItem) {
    if (this.aidiyatKodlari == null) {
      this.aidiyatKodlari = new ArrayList<>();
    }
    this.aidiyatKodlari.add(aidiyatKodlariItem);
    return this;
  }

   /**
   * Get aidiyatKodlari
   * @return aidiyatKodlari
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_AIDIYAT_KODLARI)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<String> getAidiyatKodlari() {
    return aidiyatKodlari;
  }


  @JsonProperty(JSON_PROPERTY_AIDIYAT_KODLARI)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAidiyatKodlari(List<String> aidiyatKodlari) {
    this.aidiyatKodlari = aidiyatKodlari;
  }


  public MahkemeKararID sucTipiKodlari(List<String> sucTipiKodlari) {
    
    this.sucTipiKodlari = sucTipiKodlari;
    return this;
  }

  public MahkemeKararID addSucTipiKodlariItem(String sucTipiKodlariItem) {
    if (this.sucTipiKodlari == null) {
      this.sucTipiKodlari = new ArrayList<>();
    }
    this.sucTipiKodlari.add(sucTipiKodlariItem);
    return this;
  }

   /**
   * Get sucTipiKodlari
   * @return sucTipiKodlari
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SUC_TIPI_KODLARI)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<String> getSucTipiKodlari() {
    return sucTipiKodlari;
  }


  @JsonProperty(JSON_PROPERTY_SUC_TIPI_KODLARI)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSucTipiKodlari(List<String> sucTipiKodlari) {
    this.sucTipiKodlari = sucTipiKodlari;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    MahkemeKararID mahkemeKararID = (MahkemeKararID) o;
    return Objects.equals(this.mahkemeKararBilgisi, mahkemeKararID.mahkemeKararBilgisi) &&
        Objects.equals(this.aidiyatKodlari, mahkemeKararID.aidiyatKodlari) &&
        Objects.equals(this.sucTipiKodlari, mahkemeKararID.sucTipiKodlari);
  }

  @Override
  public int hashCode() {
    return Objects.hash(mahkemeKararBilgisi, aidiyatKodlari, sucTipiKodlari);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MahkemeKararID {\n");
    sb.append("    mahkemeKararBilgisi: ").append(toIndentedString(mahkemeKararBilgisi)).append("\n");
    sb.append("    aidiyatKodlari: ").append(toIndentedString(aidiyatKodlari)).append("\n");
    sb.append("    sucTipiKodlari: ").append(toIndentedString(sucTipiKodlari)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

