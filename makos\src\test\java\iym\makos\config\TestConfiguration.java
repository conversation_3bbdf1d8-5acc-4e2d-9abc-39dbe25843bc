package iym.makos.config;

import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Import;
import spring.common.CommonLoader;

/**
 * Test configuration that excludes database components
 * Used for unit tests that don't need database connectivity
 */
@SpringBootApplication
@Import({CommonLoader.class})  // Only import CommonLoader, exclude DbLoader
public class TestConfiguration {
    // Test-specific configuration without database dependencies
}
