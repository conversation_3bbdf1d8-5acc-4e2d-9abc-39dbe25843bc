package iym.makos.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Id;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for Iller entity
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Suc tipi")
public class SucTipiDTO {

    private String sucTipiKodu;
    private String aciklama;
    private String mahkemeKaraTipiKodu;
    private String durum;

}
