package iym.backend.conf.openapi;


import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import jakarta.servlet.ServletContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
public class OpenApiConfig {

    @Bean
    public OpenAPI openAPI(ServletContext servletContext) {
        return new OpenAPI()
                .addServersItem(new Server().url(servletContext.getContextPath()).description("Default Server URL"))
                .components(new Components()
                        .addSecuritySchemes("BearerAuth", createBearerAuthScheme()))
                .info(new Info()
                        .title("IYM Backend OpenAPI definition")
                        .description("IYM Backend Application")
                        .version("v1.0"))
                .security(List.of(new SecurityRequirement().addList("BearerAuth")));
    }

    private SecurityScheme createBearerAuthScheme() {
        return new SecurityScheme().type(SecurityScheme.Type.HTTP)
                .in(SecurityScheme.In.HEADER)
                .scheme("bearer")
                .bearerFormat("JWT")
                .name("Bearer Authentication");
    }

    private SecurityScheme createBasicAuthScheme() {
        return new SecurityScheme().type(SecurityScheme.Type.HTTP)
                .in(SecurityScheme.In.HEADER)
                .scheme("basic")
                .name("Basic Authentication");
    }
}
