package iym.db.jpa.service.impl;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.HtsHedeflerTalep;
import iym.common.service.db.DbHtsHedeflerTalepService;
import iym.db.jpa.dao.HtsHedeflerTalepRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * Service implementation for HtsHedeflerTalep entity
 */
@Service
public class DbHtsHedeflerTalepServiceImpl extends GenericDbServiceImpl<HtsHedeflerTalep, Long> implements DbHtsHedeflerTalepService {

    private final HtsHedeflerTalepRepo htsHedeflerTalepRepo;

    @Autowired
    public DbHtsHedeflerTalepServiceImpl(HtsHedeflerTalepRepo repository) {
        super(repository);
        this.htsHedeflerTalepRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public List<HtsHedeflerTalep> findByMahkemeKararId(Long mahkemeKararId) {
        return htsHedeflerTalepRepo.findByMahkemeKararId(mahkemeKararId);
    }



}
