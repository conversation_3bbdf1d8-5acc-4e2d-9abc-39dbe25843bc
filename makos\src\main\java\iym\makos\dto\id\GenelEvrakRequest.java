package iym.makos.dto.id;

import iym.common.model.api.KararTuru;
import iym.common.validation.ValidationResult;
import iym.makos.model.reqrep.MahkemeKararRequest;
import iym.makos.validator.MakosRequestValid;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;
import lombok.extern.slf4j.Slf4j;

@Jacksonized
@Data
@NoArgsConstructor
@SuperBuilder
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@MakosRequestValid
@Slf4j
public class GenelEvrakRequest extends MahkemeKararRequest {

    @Override
    public ValidationResult isValid() {
        log.trace("Checking if GenelEvrakRequest is valid");
        ValidationResult validationResult = new ValidationResult(true);
        try {

            if (kararTuru != KararTuru.GENEL_EVRAK) {
                validationResult.addFailedReason("Karar türü: " + KararTuru.GENEL_EVRAK.name() + " olmalıdır");
                return validationResult;
            }

            // TODO
        } catch (Exception e) {
            log.error("Validation failed", e);
            return new ValidationResult("Validation failed. Internal error");
        }
        return validationResult;
    }

    @Override
    protected void assignKararTuru() {
        this.kararTuru = KararTuru.GENEL_EVRAK;
    }
}

