package iym.makos.service.makos;

import iym.common.model.api.HedefTip;
import iym.common.model.entity.iym.Hedefler;
import iym.common.service.db.DbHedeflerService;
import iym.makos.dto.HedeflerDTO;
import iym.makos.mapper.HedeflerMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;

/**
 * Service for HedeflerTalep operations
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class HedeflerService {

    private final DbHedeflerService dbHedeflerService;
    private final HedeflerMapper hedeflerMapper;

    @Transactional(readOnly = true)
    public HedeflerDTO findById(Long id){
        Hedefler hedef = dbHedeflerService.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Hedefler  bulunamadı: " + id));

        return hedeflerMapper.toDto(hedef);
    }

    @Transactional(readOnly = true)
    public HedeflerDTO findByMahkemeKararIdAndHedefNoAndHedefTipi(Long mahkemeKararId, String hedefNo, HedefTip hedefTipi){

        Hedefler hedef = dbHedeflerService.findByMahkemeKararIdAndHedefNoAndHedefTipi(mahkemeKararId, hedefNo, hedefTipi)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Hedefler  bulunamadı: " + hedefNo));

        return hedeflerMapper.toDto(hedef);
    }

    @Transactional(readOnly = true)
    public List<HedeflerDTO> findByMahkemeKararId(Long mahkemeKararId){
        List<Hedefler> hedeflerList = dbHedeflerService.findByMahkemeKararId(mahkemeKararId);
        return hedeflerMapper.toDtoList(hedeflerList);
    }



}
