package iym.makos.model.api;

import iym.common.model.api.MahkemeKararTip;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
@ToString
@EqualsAndHashCode
public class MahkemeKararBilgisi {

  @NotNull
  private MahkemeKararTip mahkemeKararTipi;

  @NotNull
  @Valid
  private MahkemeKararDetay mahkemeKararDetay;

}

