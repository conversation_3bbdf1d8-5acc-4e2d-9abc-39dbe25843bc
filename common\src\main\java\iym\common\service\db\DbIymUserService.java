package iym.common.service.db;


import iym.common.model.entity.iym.IymUser;
import iym.common.model.enums.IymUserRoleType;
import iym.common.model.enums.UserStatusType;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface DbIymUserService extends GenericDbService<IymUser, UUID> {
    Optional<IymUser> findByUsername(String name);

    List<IymUser> findByStatus(UserStatusType status);

    List<IymUser> findByRoleOrderByUsernameAsc(IymUserRoleType role);

    List<IymUser> findAllByOrderByUsernameAsc();

    void activateUser(String username);

    void deactivateUser(String username);

    void updateUser(IymUser iymUser);

}
