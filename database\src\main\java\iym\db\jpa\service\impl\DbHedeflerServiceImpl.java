package iym.db.jpa.service.impl;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.api.HedefTip;
import iym.common.model.entity.iym.Hedefler;
import iym.common.service.db.DbHedeflerService;
import iym.db.jpa.dao.HedeflerRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Service implementation for Hedefler entity
 */
@Service
public class DbHedeflerServiceImpl extends GenericDbServiceImpl<Hedefler, Long> implements DbHedeflerService {

    private final HedeflerRepo hedeflerRepo;

    @Autowired
    public DbHedeflerServiceImpl(HedeflerRepo repository) {
        super(repository);
        this.hedeflerRepo = repository;
    }


    @Override
    @Transactional(readOnly = true)
    public Optional<Hedefler> findById(Long id){
        return hedeflerRepo.findById(id);
    }


    @Override
    @Transactional(readOnly = true)
    public Optional<Hedefler> findByMahkemeKararIdAndHedefNoAndHedefTipi(Long mahkemeKararId, String hedefno, HedefTip hedefTipi){
        return hedeflerRepo.findByMahkemeKararIdAndHedefNoAndHedefTipi(mahkemeKararId, hedefno, String.valueOf(hedefTipi.getHedefKodu()));
    }

    @Override
    @Transactional(readOnly = true)
    public List<Hedefler> findByMahkemeKararId(Long mahkemeKararId) {
        return hedeflerRepo.findByMahkemeKararId(mahkemeKararId);
    }

}
