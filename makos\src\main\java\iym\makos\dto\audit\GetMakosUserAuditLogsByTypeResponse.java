package iym.makos.dto.audit;

import iym.common.model.api.ApiResponseBase;
import iym.common.model.entity.makos.MakosUserAuditLog;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.springframework.data.domain.Page;

@Data
@SuperBuilder
@ToString
@EqualsAndHashCode(callSuper = true)
public class GetMakosUserAuditLogsByTypeResponse extends ApiResponseBase {
    private Page<MakosUserAuditLog> auditLogs;
} 