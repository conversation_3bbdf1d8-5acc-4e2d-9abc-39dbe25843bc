package iym.common.model.api;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum MahkemeKararIslemTuru {

	BELIRSIZ(0),
	TANIMLAMA(1),
	SONLANDIRMA(2),
	UZATMA(3),
	AIDIYAT_DEGISTIRME(4),
	HEDEF_ADSOYAD_DEGISTIRME(5),
	MAHKEME_KODU_DEGISTIRME(6),
	GENEL_KARAR(7),
	HTS(8),
	HEDEF_CANAK_DEGISTIRME(9);


	private final int islemTuru;

	MahkemeKararIslemTuru(int kararKodu){
		this.islemTuru = kararKodu;
	}

	@JsonValue
	public int getIslemTuru(){
		return this.islemTuru;
	}

	@JsonCreator
	public static MahkemeKararIslemTuru fromName(String name) {
		for (MahkemeKararIslemTuru islemTuru : MahkemeKararIslemTuru.values()) {
			if (islemTuru.name().equals(name)) {
				return islemTuru;
			}
		}
		throw new IllegalArgumentException("Gecersiz islemTuru: " + name + "'");
	}

	//@JsonCreator
	public static MahkemeKararIslemTuru fromValue(int islemTuru) {
		for (MahkemeKararIslemTuru b : MahkemeKararIslemTuru.values()) {
			if (b.islemTuru == islemTuru) {
				return b;
			}
		}
		throw new IllegalArgumentException("Gecersiz islemTuru: '" + islemTuru + "'");
	}
}
