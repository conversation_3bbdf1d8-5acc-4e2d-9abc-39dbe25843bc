package iym.makos.mapper;

import iym.common.model.entity.iym.HtsHedeflerTalep;
import org.springframework.stereotype.Component;
import iym.makos.dto.HtsHedeflerTalepDTO;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper for HtsHedeflerTalep entity and DTO
 */
@Component
public class HtsHedeflerTalepMapper {

    /**
     * Convert entity to DTO
     * @param entity HtsHedeflerTalep entity
     * @return HtsHedeflerTalepDTO
     */
    public HtsHedeflerTalepDTO toDto(HtsHedeflerTalep entity) {
        if (entity == null) {
            return null;
        }

        return HtsHedeflerTalepDTO.builder()
                .id(entity.getId())
                .mahkemeKararId(entity.getMahkemeKararId())
                .hedefNo(entity.getHedefNo())
                .karsiHedefNo(entity.getKarsiHedefNo())
                .sorguTipi(entity.getSorguTipi())
                .baslangicT<PERSON>hi(entity.getBaslangicTarihi())
                .bitisTarihi(entity.getBitisTarihi())
                .tespitTuru(entity.getTespitTuru())
                .kullaniciId(entity.getKullaniciId())
                .durumu(entity.getDurumu())
                .build();
    }

    /**
     * Convert DTO to entity
     * @param dto HtsHedeflerTalepDTO
     * @return HtsHedeflerTalep entity
     */
    public HtsHedeflerTalep toEntity(HtsHedeflerTalepDTO dto) {
        if (dto == null) {
            return null;
        }

        return HtsHedeflerTalep.builder()
                .id(dto.getId())
                .mahkemeKararId(dto.getMahkemeKararId())
                .hedefNo(dto.getHedefNo())
                .karsiHedefNo(dto.getKarsiHedefNo())
                .sorguTipi(dto.getSorguTipi())
                .baslangicTarihi(dto.getBaslangicTarihi())
                .bitisTarihi(dto.getBitisTarihi())
                .tespitTuru(dto.getTespitTuru())
                .kullaniciId(dto.getKullaniciId())
                .durumu(dto.getDurumu())
                .build();
    }

    /**
     * Update entity from DTO
     * @param entity HtsHedeflerTalep entity to update
     * @param dto HtsHedeflerTalepDTO with new values
     * @return Updated HtsHedeflerTalep entity
     */
    public HtsHedeflerTalep updateEntityFromDto(HtsHedeflerTalep entity, HtsHedeflerTalepDTO dto) {
        if (entity == null || dto == null) {
            return entity;
        }

        // mahkemeKararId is a foreign key and should not be updated
        entity.setHedefNo(dto.getHedefNo());
        entity.setKarsiHedefNo(dto.getKarsiHedefNo());
        entity.setSorguTipi(dto.getSorguTipi());
        entity.setBaslangicTarihi(dto.getBaslangicTarihi());
        entity.setBitisTarihi(dto.getBitisTarihi());
        entity.setTespitTuru(dto.getTespitTuru());
        entity.setKullaniciId(dto.getKullaniciId());
        entity.setDurumu(dto.getDurumu());

        return entity;
    }

    /**
     * Convert list of entities to list of DTOs
     * @param entityList List of HtsHedeflerTalep entities
     * @return List of HtsHedeflerTalepDTO
     */
    public List<HtsHedeflerTalepDTO> toDtoList(List<HtsHedeflerTalep> entityList) {
        if (entityList == null) {
            return null;
        }

        return entityList.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }

    /**
     * Convert list of DTOs to list of entities
     * @param dtoList List of HtsHedeflerTalepDTO
     * @return List of HtsHedeflerTalep entities
     */
    public List<HtsHedeflerTalep> toEntityList(List<HtsHedeflerTalepDTO> dtoList) {
        if (dtoList == null) {
            return null;
        }

        return dtoList.stream()
                .map(this::toEntity)
                .collect(Collectors.toList());
    }
}
