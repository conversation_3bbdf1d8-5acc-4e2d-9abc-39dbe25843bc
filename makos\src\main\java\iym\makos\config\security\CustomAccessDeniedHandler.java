package iym.makos.config.security;

import com.fasterxml.jackson.databind.ObjectMapper;
import iym.common.model.dto.Response;
import iym.common.model.enums.ResultCode;
import iym.common.util.EnvironmentUtil;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
@Slf4j
public class CustomAccessDeniedHandler implements AccessDeniedHandler {

    private final EnvironmentUtil environmentUtil;

    public CustomAccessDeniedHandler(EnvironmentUtil environmentUtil) {
        this.environmentUtil = environmentUtil;
    }

    @Override
    public void handle(HttpServletRequest request,
                       HttpServletResponse response,
                       AccessDeniedException accessDeniedException)
            throws IOException, ServletException {

        response.setStatus(HttpServletResponse.SC_FORBIDDEN); // 403
        response.setContentType("application/json");

        String message = "Access Denied: You do not have the required role to access this resource";

        // Create response with environment-appropriate exception details
        Response<Object> responseBody;
        if (environmentUtil.isDevelopmentEnvironment()) {
            responseBody = new Response<>(ResultCode.REJECTED, message, accessDeniedException);
        } else {
            responseBody = new Response<>(ResultCode.REJECTED, message);
        }

        // Always log full details for debugging
        log.error("Access Denied at filter level. uri:{}, user:{}, error:{}",
                request.getServletPath(),
                getCurrentUser(request),
                accessDeniedException.getMessage(),
                accessDeniedException);

        final ObjectMapper mapper = new ObjectMapper();
        mapper.writeValue(response.getOutputStream(), responseBody);
        response.getOutputStream().flush();
    }

    /**
     * Get current authenticated user for logging purposes
     */
    private String getCurrentUser(HttpServletRequest request) {
        try {
            return org.springframework.security.core.context.SecurityContextHolder
                    .getContext()
                    .getAuthentication()
                    .getName();
        } catch (Exception e) {
            return "anonymous";
        }
    }
}
