package iym.makos.handler;

import iym.makos.model.reqrep.MahkemeKararRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.GenericTypeResolver;

@Slf4j
public abstract class MahkemeKararRequestDbSaveHandlerBase<T extends MahkemeKararRequest> implements MahkemeKararDBSaveHandler<T> {

    protected MahkemeKararRequestCommonDbSaver mahkemeKararRequestCommonDbSaver;

    @Autowired
    public final void setMahkemeKararRequestCommonDbSaver(MahkemeKararRequestCommonDbSaver mahkemeKararRequestCommonDbSaver) {
        this.mahkemeKararRequestCommonDbSaver = mahkemeKararRequestCommonDbSaver;
    }


    @SuppressWarnings("unchecked")
    public Class<T> getRelatedRequestType() {
        return (Class<T>) GenericTypeResolver.resolveTypeArgument(
                this.getClass(),
                MahkemeKararDBSaveHandler.class
        );
    }

}

