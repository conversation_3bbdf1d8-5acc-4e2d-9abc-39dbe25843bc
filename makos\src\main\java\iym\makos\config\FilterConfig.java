package iym.makos.config;

import iym.common.service.db.DbMakosUserAuditLogService;
import iym.common.service.db.DbMakosKararRequestLogService;
import iym.makos.controller.filters.MakosUserAuditLogFilter;
import iym.makos.controller.filters.MakosKararRequestLogFilter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Filter configuration for MAKOS module
 * Configures audit logging filters for user operations
 */
@Configuration
public class FilterConfig {

    private final DbMakosUserAuditLogService makosUserAuditLogService;
    private final DbMakosKararRequestLogService makosKararRequestLogService;

    public FilterConfig(DbMakosUserAuditLogService makosUserAuditLogService, DbMakosKararRequestLogService makosKararRequestLogService) {
        this.makosUserAuditLogService = makosUserAuditLogService;
        this.makosKararRequestLogService = makosKararRequestLogService;
    }

    /**
     * Configure MAKOS User Audit Log Filter
     * Logs all user-related operations for security and compliance
     */
    @Bean
    public FilterRegistrationBean<MakosUserAuditLogFilter> makosUserAuditLogFilter() {
        FilterRegistrationBean<MakosUserAuditLogFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(new MakosUserAuditLogFilter(makosUserAuditLogService));
        registrationBean.addUrlPatterns(
                "/auth/login",
                "/auth/register",
                "/auth/changePassword",
                "/user/getUsersForAdmin",
                "/user/activate",
                "/user/deactivate",
                "/user/add",
                "/user/update",
                "/user/delete");

        registrationBean.setOrder(1);

        return registrationBean;
    }

    /**
     * Configure MAKOS Karar Request Log Filter
     * Logs all court decision-related requests for audit and monitoring
     */
    @Bean
    public FilterRegistrationBean<MakosKararRequestLogFilter> makosKararRequestLogFilter() {
        FilterRegistrationBean<MakosKararRequestLogFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(new MakosKararRequestLogFilter(makosKararRequestLogService));
        registrationBean.addUrlPatterns("/mahkemeKarar/*");
        registrationBean.setOrder(2); // UserAuditLogFilter is 1, this is 2
        return registrationBean;
    }
}
