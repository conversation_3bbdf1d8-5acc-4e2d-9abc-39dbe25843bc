package iym.makos.processor;

import iym.common.validation.ValidationResult;
import iym.makos.config.security.UserDetailsImpl;
import iym.makos.dto.id.IDUzatmaKarariRequest;
import iym.makos.dto.id.IDUzatmaKarariResponse;
import iym.makos.model.MakosApiResponse;
import iym.makos.model.MakosResponseCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
@Slf4j
public class IDUzatmaKarariRequestProcessor extends MakosRequestProcessorBase<IDUzatmaKarariRequest, IDUzatmaKarariResponse> {

    @Override
    public IDUzatmaKarariResponse process(IDUzatmaKarariRequest request, UserDetailsImpl islemYapanKullanici) {
        try {

            ValidationResult validationResult = requestValidator.validate(request);
            if (!validationResult.isValid()) {
                return IDUzatmaKarariResponse.builder()
                        .response(MakosApiResponse.builder()
                                .responseCode(MakosResponseCode.INVALID_REQUEST)
                                .responseMessage(validationResult.getReasons().toString())
                                .build())
                        .build();
            }

            Date kayitTarihi = new Date();
            Long kaydedenKullaniciId = islemYapanKullanici.getId();
            Long mahkemeKararTalepId = requestSaver.handleDbSave(request, kayitTarihi, kaydedenKullaniciId);

            return IDUzatmaKarariResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.SUCCESS)
                            .responseMessage(validationResult.getReasons().toString())
                            .build())
                    .evrakId(mahkemeKararTalepId)
                    .build();

        } catch (Exception ex) {
            log.error("IDUzatmaKarari process failed. id:{}, evrakNo:{}", request.getId(), request.getEvrakDetay().getEvrakNo(), ex);
            return IDUzatmaKarariResponse.builder()
                    .response(MakosApiResponse.builder()
                            .responseCode(MakosResponseCode.FAILED)
                            .responseMessage("INTERNAL ERROR")
                            .build())
                    .build();
        }
    }
}
