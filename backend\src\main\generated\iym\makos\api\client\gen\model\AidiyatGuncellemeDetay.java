/*
 * MAKOS OpenAPI definition
 * MAKOS Application
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package iym.makos.api.client.gen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * AidiyatGuncellemeDetay
 */
@JsonPropertyOrder({
  AidiyatGuncellemeDetay.JSON_PROPERTY_GUNCELLEME_TIP,
  AidiyatGuncellemeDetay.JSON_PROPERTY_AIDIYAT_KODU
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class AidiyatGuncellemeDetay {
  /**
   * Gets or Sets guncellemeTip
   */
  public enum GuncellemeTipEnum {
    _0("0"),
    
    _1("1");

    private String value;

    GuncellemeTipEnum(String value) {
      this.value = value;
    }

    @JsonValue
    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static GuncellemeTipEnum fromValue(String value) {
      for (GuncellemeTipEnum b : GuncellemeTipEnum.values()) {
        if (b.value.equals(value)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + value + "'");
    }
  }

  public static final String JSON_PROPERTY_GUNCELLEME_TIP = "guncellemeTip";
  private GuncellemeTipEnum guncellemeTip;

  public static final String JSON_PROPERTY_AIDIYAT_KODU = "aidiyatKodu";
  private String aidiyatKodu;

  public AidiyatGuncellemeDetay() {
  }

  public AidiyatGuncellemeDetay guncellemeTip(GuncellemeTipEnum guncellemeTip) {
    
    this.guncellemeTip = guncellemeTip;
    return this;
  }

   /**
   * Get guncellemeTip
   * @return guncellemeTip
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_GUNCELLEME_TIP)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public GuncellemeTipEnum getGuncellemeTip() {
    return guncellemeTip;
  }


  @JsonProperty(JSON_PROPERTY_GUNCELLEME_TIP)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setGuncellemeTip(GuncellemeTipEnum guncellemeTip) {
    this.guncellemeTip = guncellemeTip;
  }


  public AidiyatGuncellemeDetay aidiyatKodu(String aidiyatKodu) {
    
    this.aidiyatKodu = aidiyatKodu;
    return this;
  }

   /**
   * Get aidiyatKodu
   * @return aidiyatKodu
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_AIDIYAT_KODU)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getAidiyatKodu() {
    return aidiyatKodu;
  }


  @JsonProperty(JSON_PROPERTY_AIDIYAT_KODU)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setAidiyatKodu(String aidiyatKodu) {
    this.aidiyatKodu = aidiyatKodu;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    AidiyatGuncellemeDetay aidiyatGuncellemeDetay = (AidiyatGuncellemeDetay) o;
    return Objects.equals(this.guncellemeTip, aidiyatGuncellemeDetay.guncellemeTip) &&
        Objects.equals(this.aidiyatKodu, aidiyatGuncellemeDetay.aidiyatKodu);
  }

  @Override
  public int hashCode() {
    return Objects.hash(guncellemeTip, aidiyatKodu);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class AidiyatGuncellemeDetay {\n");
    sb.append("    guncellemeTip: ").append(toIndentedString(guncellemeTip)).append("\n");
    sb.append("    aidiyatKodu: ").append(toIndentedString(aidiyatKodu)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

