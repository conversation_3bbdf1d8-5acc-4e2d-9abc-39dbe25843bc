package iym.makos.api.client.gen.api;

import iym.makos.api.client.gen.handler.ApiClient;

import java.io.File;
import iym.makos.api.client.gen.model.IDAidiyatBilgisiGuncellemeRequest;
import iym.makos.api.client.gen.model.IDCanakGuncellemeRequest;
import iym.makos.api.client.gen.model.IDHedefAdSoyadGuncellemeRequest;
import iym.makos.api.client.gen.model.IDKararRequest;
import iym.makos.api.client.gen.model.IDKararResponse;
import iym.makos.api.client.gen.model.IDMahkemeKoduGuncellemeRequest;
import iym.makos.api.client.gen.model.ITKararRequest;
import iym.makos.api.client.gen.model.ITKararResponse;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class MahkemeKararControllerApi {
    private ApiClient apiClient;

    public MahkemeKararControllerApi() {
        this(new ApiClient());
    }

    public MahkemeKararControllerApi(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return apiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param mahkemeKararDosyasi  (required)
     * @param mahkemeKararDetay  (required)
     * @return Object
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public Object aidiyatBilgisiGuncelle(File mahkemeKararDosyasi, IDAidiyatBilgisiGuncellemeRequest mahkemeKararDetay) throws RestClientException {
        return aidiyatBilgisiGuncelleWithHttpInfo(mahkemeKararDosyasi, mahkemeKararDetay).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param mahkemeKararDosyasi  (required)
     * @param mahkemeKararDetay  (required)
     * @return ResponseEntity&lt;Object&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<Object> aidiyatBilgisiGuncelleWithHttpInfo(File mahkemeKararDosyasi, IDAidiyatBilgisiGuncellemeRequest mahkemeKararDetay) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'mahkemeKararDosyasi' is set
        if (mahkemeKararDosyasi == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'mahkemeKararDosyasi' when calling aidiyatBilgisiGuncelle");
        }
        
        // verify the required parameter 'mahkemeKararDetay' is set
        if (mahkemeKararDetay == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'mahkemeKararDetay' when calling aidiyatBilgisiGuncelle");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        if (mahkemeKararDosyasi != null)
            localVarFormParams.add("mahkemeKararDosyasi", new FileSystemResource(mahkemeKararDosyasi));
        if (mahkemeKararDetay != null)
            localVarFormParams.add("mahkemeKararDetay", mahkemeKararDetay);

        final String[] localVarAccepts = { 
            "*/*"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "multipart/form-data"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] { "BasicAuth" };

        ParameterizedTypeReference<Object> localReturnType = new ParameterizedTypeReference<Object>() {};
        return apiClient.invokeAPI("/mahkemeKarar/aidiyatBilgisiGuncelle", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param mahkemeKararDosyasi  (required)
     * @param mahkemeKararDetay  (required)
     * @return Object
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public Object canakNoGuncelle(File mahkemeKararDosyasi, IDCanakGuncellemeRequest mahkemeKararDetay) throws RestClientException {
        return canakNoGuncelleWithHttpInfo(mahkemeKararDosyasi, mahkemeKararDetay).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param mahkemeKararDosyasi  (required)
     * @param mahkemeKararDetay  (required)
     * @return ResponseEntity&lt;Object&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<Object> canakNoGuncelleWithHttpInfo(File mahkemeKararDosyasi, IDCanakGuncellemeRequest mahkemeKararDetay) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'mahkemeKararDosyasi' is set
        if (mahkemeKararDosyasi == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'mahkemeKararDosyasi' when calling canakNoGuncelle");
        }
        
        // verify the required parameter 'mahkemeKararDetay' is set
        if (mahkemeKararDetay == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'mahkemeKararDetay' when calling canakNoGuncelle");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        if (mahkemeKararDosyasi != null)
            localVarFormParams.add("mahkemeKararDosyasi", new FileSystemResource(mahkemeKararDosyasi));
        if (mahkemeKararDetay != null)
            localVarFormParams.add("mahkemeKararDetay", mahkemeKararDetay);

        final String[] localVarAccepts = { 
            "*/*"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "multipart/form-data"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] { "BasicAuth" };

        ParameterizedTypeReference<Object> localReturnType = new ParameterizedTypeReference<Object>() {};
        return apiClient.invokeAPI("/mahkemeKarar/canakNoGuncelle", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param mahkemeKararDosyasi  (required)
     * @param mahkemeKararDetay  (required)
     * @return Object
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public Object hedefAdSoyadGuncelle(File mahkemeKararDosyasi, IDHedefAdSoyadGuncellemeRequest mahkemeKararDetay) throws RestClientException {
        return hedefAdSoyadGuncelleWithHttpInfo(mahkemeKararDosyasi, mahkemeKararDetay).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param mahkemeKararDosyasi  (required)
     * @param mahkemeKararDetay  (required)
     * @return ResponseEntity&lt;Object&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<Object> hedefAdSoyadGuncelleWithHttpInfo(File mahkemeKararDosyasi, IDHedefAdSoyadGuncellemeRequest mahkemeKararDetay) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'mahkemeKararDosyasi' is set
        if (mahkemeKararDosyasi == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'mahkemeKararDosyasi' when calling hedefAdSoyadGuncelle");
        }
        
        // verify the required parameter 'mahkemeKararDetay' is set
        if (mahkemeKararDetay == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'mahkemeKararDetay' when calling hedefAdSoyadGuncelle");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        if (mahkemeKararDosyasi != null)
            localVarFormParams.add("mahkemeKararDosyasi", new FileSystemResource(mahkemeKararDosyasi));
        if (mahkemeKararDetay != null)
            localVarFormParams.add("mahkemeKararDetay", mahkemeKararDetay);

        final String[] localVarAccepts = { 
            "*/*"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "multipart/form-data"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] { "BasicAuth" };

        ParameterizedTypeReference<Object> localReturnType = new ParameterizedTypeReference<Object>() {};
        return apiClient.invokeAPI("/mahkemeKarar/hedefAdSoyadGuncelle", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param mahkemeKararDosyasiID  (required)
     * @param mahkemeKararDetayID  (required)
     * @return IDKararResponse
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public IDKararResponse kararGonderID(File mahkemeKararDosyasiID, IDKararRequest mahkemeKararDetayID) throws RestClientException {
        return kararGonderIDWithHttpInfo(mahkemeKararDosyasiID, mahkemeKararDetayID).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param mahkemeKararDosyasiID  (required)
     * @param mahkemeKararDetayID  (required)
     * @return ResponseEntity&lt;IDKararResponse&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<IDKararResponse> kararGonderIDWithHttpInfo(File mahkemeKararDosyasiID, IDKararRequest mahkemeKararDetayID) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'mahkemeKararDosyasiID' is set
        if (mahkemeKararDosyasiID == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'mahkemeKararDosyasiID' when calling kararGonderID");
        }
        
        // verify the required parameter 'mahkemeKararDetayID' is set
        if (mahkemeKararDetayID == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'mahkemeKararDetayID' when calling kararGonderID");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        if (mahkemeKararDosyasiID != null)
            localVarFormParams.add("mahkemeKararDosyasiID", new FileSystemResource(mahkemeKararDosyasiID));
        if (mahkemeKararDetayID != null)
            localVarFormParams.add("mahkemeKararDetayID", mahkemeKararDetayID);

        final String[] localVarAccepts = { 
            "*/*"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "multipart/form-data"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] { "BasicAuth" };

        ParameterizedTypeReference<IDKararResponse> localReturnType = new ParameterizedTypeReference<IDKararResponse>() {};
        return apiClient.invokeAPI("/mahkemeKarar/kararGonderID", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param mahkemeKararDosyasiIT  (required)
     * @param mahkemeKararDetayIT  (required)
     * @return ITKararResponse
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ITKararResponse kararGonderIT(File mahkemeKararDosyasiIT, ITKararRequest mahkemeKararDetayIT) throws RestClientException {
        return kararGonderITWithHttpInfo(mahkemeKararDosyasiIT, mahkemeKararDetayIT).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param mahkemeKararDosyasiIT  (required)
     * @param mahkemeKararDetayIT  (required)
     * @return ResponseEntity&lt;ITKararResponse&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ITKararResponse> kararGonderITWithHttpInfo(File mahkemeKararDosyasiIT, ITKararRequest mahkemeKararDetayIT) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'mahkemeKararDosyasiIT' is set
        if (mahkemeKararDosyasiIT == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'mahkemeKararDosyasiIT' when calling kararGonderIT");
        }
        
        // verify the required parameter 'mahkemeKararDetayIT' is set
        if (mahkemeKararDetayIT == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'mahkemeKararDetayIT' when calling kararGonderIT");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        if (mahkemeKararDosyasiIT != null)
            localVarFormParams.add("mahkemeKararDosyasiIT", new FileSystemResource(mahkemeKararDosyasiIT));
        if (mahkemeKararDetayIT != null)
            localVarFormParams.add("mahkemeKararDetayIT", mahkemeKararDetayIT);

        final String[] localVarAccepts = { 
            "*/*"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "multipart/form-data"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] { "BasicAuth" };

        ParameterizedTypeReference<ITKararResponse> localReturnType = new ParameterizedTypeReference<ITKararResponse>() {};
        return apiClient.invokeAPI("/mahkemeKarar/kararGonderIT", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param mahkemeKararDosyasi  (required)
     * @param mahkemeKararDetay  (required)
     * @return Object
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public Object mahkemeKoduGuncelle(File mahkemeKararDosyasi, IDMahkemeKoduGuncellemeRequest mahkemeKararDetay) throws RestClientException {
        return mahkemeKoduGuncelleWithHttpInfo(mahkemeKararDosyasi, mahkemeKararDetay).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param mahkemeKararDosyasi  (required)
     * @param mahkemeKararDetay  (required)
     * @return ResponseEntity&lt;Object&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<Object> mahkemeKoduGuncelleWithHttpInfo(File mahkemeKararDosyasi, IDMahkemeKoduGuncellemeRequest mahkemeKararDetay) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'mahkemeKararDosyasi' is set
        if (mahkemeKararDosyasi == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'mahkemeKararDosyasi' when calling mahkemeKoduGuncelle");
        }
        
        // verify the required parameter 'mahkemeKararDetay' is set
        if (mahkemeKararDetay == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'mahkemeKararDetay' when calling mahkemeKoduGuncelle");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        if (mahkemeKararDosyasi != null)
            localVarFormParams.add("mahkemeKararDosyasi", new FileSystemResource(mahkemeKararDosyasi));
        if (mahkemeKararDetay != null)
            localVarFormParams.add("mahkemeKararDetay", mahkemeKararDetay);

        final String[] localVarAccepts = { 
            "*/*"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "multipart/form-data"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] { "BasicAuth" };

        ParameterizedTypeReference<Object> localReturnType = new ParameterizedTypeReference<Object>() {};
        return apiClient.invokeAPI("/mahkemeKarar/mahkemeKoduGuncelle", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
}
