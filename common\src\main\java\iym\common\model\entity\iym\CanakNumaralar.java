package iym.common.model.entity.iym;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * Entity class for CANAK_NUMARALAR table
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode

@Entity(name = "CanakNumaralar")
@Table(name = "CANAK_NUMARALAR")
public class CanakNumaralar implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "CANAK_NUMARALAR_SEQ")
    @SequenceGenerator(name = "CANAK_NUMARALAR_SEQ", sequenceName = "CANAK_NUMARALAR_SEQ", allocationSize = 1)
    private Long id;

    @Column(name = "CANAK_NO", nullable = false, length = 20)
    @NotNull
    @Size(max = 20)
    private String canakNo;

    @Column(name = "KURUM_KOD", nullable = false, length = 2)
    @NotNull
    @Size(max = 2)
    private String kurumKod;

    @Column(name = "EKLEME_TARIH")
    @Temporal(TemporalType.TIMESTAMP)
    private Date eklemeTarih;

    @Column(name = "KUTU")
    private Long kutu;

    @Column(name = "ACIKLAMA", length = 300)
    @Size(max = 300)
    private String aciklama;

    @Column(name = "EKLEYEN_ID")
    private Long ekleyenId;
}
