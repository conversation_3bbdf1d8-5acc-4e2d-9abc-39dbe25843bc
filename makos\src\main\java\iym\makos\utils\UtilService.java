package iym.makos.utils;

import iym.common.model.entity.iym.EvrakSiraNo;
import iym.db.jpa.dao.EvrakSiraNoRepo;
import iym.makos.service.makos.EvrakGelenKurumlarService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;

/**
 * Service for Iller operations
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UtilService {
    private final EvrakGelenKurumlarService evrakGelenKurumlarService;
    private final EvrakSiraNoRepo evrakSiraNoRepo;

    public String getEvrakSiraNumarasi(String kurumKod, String evrakTipi){
        String result = "";

        Long yil = Long.valueOf(LocalDate.now().getYear());

        EvrakSiraNo evrakSira = evrakSiraNoRepo.findByYil(yil)
                .orElseGet(() -> {
                    EvrakSiraNo yeniKayit = new EvrakSiraNo();
                    yeniKayit.setYil(yil);
                    yeniKayit.setSiraNo(1L);
                    return evrakSiraNoRepo.save(yeniKayit);
                });

        Long yeniSira = evrakSira.getSiraNo() + 1;
        evrakSira.setSiraNo(yeniSira);
        evrakSiraNoRepo.save(evrakSira);

        String siraNo = String.format("%04d-%05d", yil, yeniSira); // örn: 2025-00001

        return kurumKod + "." + siraNo;

        /*
        LocalDate today = LocalDate.now(); // Bugünün tarihi
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String formattedYilAyGun = today.format(formatter);

        int number = ThreadLocalRandom.current().nextInt(1, 10000); // 1 - 9999
        String formatted = String.format("%04d", number); // 4 haneli format, başına sıfır ekler

        result = kurumKod + "-" +  formattedYilAyGun + "." + formatted;


        return result;
        */
    }

}
