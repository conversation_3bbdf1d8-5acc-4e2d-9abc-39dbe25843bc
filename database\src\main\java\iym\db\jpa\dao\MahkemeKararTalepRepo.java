package iym.db.jpa.dao;

import iym.common.model.entity.iym.MahkemeKararTalep;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for MahkemeKararTalep entity
 */
@Repository
public interface MahkemeKararTalepRepo extends JpaRepository<MahkemeKararTalep, Long> {

    List<MahkemeKararTalep> findByEvrakId(Long evrakId);
    

}
