package iym.common.model.api;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum SorguTipi {

	// TODO: Complete enum with correct values
	TELEFON_GORUSME(1),
	IMEI_GORUSME(2),
	IMEI_KULLANAN_NUMARA(3);

	private final int sorguTip;

	SorguTipi(int sorguTip){
		this.sorguTip = sorguTip;
	}

	@JsonValue
	public int getSorguTip(){
		return this.sorguTip;
	}

	@JsonCreator
	public static SorguTipi fromName(String name) {
		for (SorguTipi hedefTip : SorguTipi.values()) {
			if (hedefTip.name().equals(name)) {
				return hedefTip;
			}
		}
		throw new IllegalArgumentException("Gecersiz hedefTip: '" + name + "'");
	}

	//@JsonCreator
	public static SorguTipi fromValue(int value) {
		for (SorguTipi hedefTip : SorguTipi.values()) {
			if (hedefTip.sorguTip == value) {
				return hedefTip;
			}
		}
		throw new IllegalArgumentException("Gecersiz hedefKodu: '" + value + "'");
	}
}
