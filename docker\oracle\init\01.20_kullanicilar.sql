-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;

-- Create sequence for <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> if it doesn't exist
DECLARE
  seq_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO seq_exists FROM user_sequences WHERE sequence_name = '<PERSON>U<PERSON><PERSON><PERSON><PERSON><PERSON>_SEQ';
  IF seq_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE SEQUENCE iym.KULL<PERSON><PERSON><PERSON>AR_SEQ
      START WITH 1
      INCREMENT BY 1
      NOCACHE
      NOCYCLE';
  END IF;
END;
/

-- Create KULLANICILAR table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'KU<PERSON><PERSON><PERSON>ILAR';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.KULLANICILAR (
      ID NUMBER NOT NULL,
      ADI VARCHAR2(20 BYTE) NOT NULL,
      SOYADI VARCHAR2(40 BYTE) NOT NULL,
      <PERSON><PERSON><PERSON><PERSON><PERSON>I_ADI VARCHAR2(20 BYTE) NOT NULL,
      SIF<PERSON> CHAR(32 BYTE) NOT NULL,
      GOREVI VARCHAR2(6 BYTE),
      YETKI NUMBER,
      BIRIMI VARCHAR2(4 BYTE),
      GOREV_TANIMI VARCHAR2(300 BYTE),
      TEL VARCHAR2(16 BYTE),
      POSTA VARCHAR2(55 BYTE),
      FAX VARCHAR2(55 BYTE),
      RESMI_KOD VARCHAR2(12 BYTE),
      KIM_KUL_ID NUMBER,
      DURUMU VARCHAR2(20 BYTE),
      IMZA_YETKISI CHAR(1 BYTE),
      IMZA_DOSYASI VARCHAR2(50 BYTE),
      EKSTRAGUVENLIK VARCHAR2(1 BYTE),
      TEMSIL_EDILEN_KURUM VARCHAR2(2 BYTE),
      CID VARCHAR2(32 BYTE),
      TCK VARCHAR2(11 BYTE),
      PAROLA_DEGISIM_TARIHI DATE,
      AKADEMIK_UNVAN VARCHAR2(10 BYTE),
      GRUP_KODU VARCHAR2(10 BYTE),
      CONSTRAINT KULLANICI_ID_IDX PRIMARY KEY (ID) ENABLE
    )';
    
    -- Create unique index on KULLANICI_ADI
    EXECUTE IMMEDIATE 'CREATE UNIQUE INDEX iym.KULLANICI_ADI ON iym.KULLANICILAR (KULLANICI_ADI ASC)';
  END IF;
END;
/

-- Insert sample data if table is empty
DECLARE
  user_count NUMBER;
BEGIN
  SELECT COUNT(*) INTO user_count FROM iym.KULLANICILAR;
  IF user_count = 0 THEN
    -- Admin user
    INSERT INTO iym.KULLANICILAR (
      ID, ADI, SOYADI, KULLANICI_ADI, SIFRE, GOREVI, YETKI, BIRIMI,
      GOREV_TANIMI, TEL, POSTA, DURUMU, IMZA_YETKISI
    ) VALUES (
      iym.KULLANICILAR_SEQ.NEXTVAL, 'Admin', 'User', 'admin', 'e10adc3949ba59abbe56e057f20f883e',
      'ADMIN', 1, '0001', 'Sistem Yöneticisi', '5551234567', '<EMAIL>', 'ACTIVE', 'E'
    );
    
    -- Regular user 1
    INSERT INTO iym.KULLANICILAR (
      ID, ADI, SOYADI, KULLANICI_ADI, SIFRE, GOREVI, YETKI, BIRIMI,
      GOREV_TANIMI, TEL, POSTA, DURUMU, IMZA_YETKISI
    ) VALUES (
      iym.KULLANICILAR_SEQ.NEXTVAL, 'Ahmet', 'Yılmaz', 'ahmet', 'e10adc3949ba59abbe56e057f20f883e',
      'UZM', 2, '0002', 'Uzman', '5551234568', '<EMAIL>', 'ACTIVE', 'H'
    );
    
    -- Regular user 2
    INSERT INTO iym.KULLANICILAR (
      ID, ADI, SOYADI, KULLANICI_ADI, SIFRE, GOREVI, YETKI, BIRIMI,
      GOREV_TANIMI, TEL, POSTA, DURUMU, IMZA_YETKISI
    ) VALUES (
      iym.KULLANICILAR_SEQ.NEXTVAL, 'Ayşe', 'Kaya', 'ayse', 'e10adc3949ba59abbe56e057f20f883e',
      'MEM', 3, '0003', 'Memur', '5551234569', '<EMAIL>', 'ACTIVE', 'H'
    );
  END IF;
END;
/

COMMIT;
