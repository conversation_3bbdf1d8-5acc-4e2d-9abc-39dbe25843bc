# Test configuration with H2 in-memory database
# Uses integration-test profile for SpringBootTest integration tests
spring.profiles.active=integration-test

spring.datasource.url=jdbc:h2:mem:makostestdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect

spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# Connection pool configuration for tests
spring.datasource.hikari.connectionTimeout=20000
spring.datasource.hikari.maximumPoolSize=5

# Logging configuration
logging.level.org.springframework=INFO
logging.level.org.hibernate=INFO
logging.level.iym=DEBUG

# Disable security for tests
spring.security.user.name=test
spring.security.user.password=test
