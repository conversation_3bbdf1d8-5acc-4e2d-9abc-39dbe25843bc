# Docker Development Environment for IYM Project

Bu dizin, IYM projesi için Docker tabanlı geliştirme ortamlarını içerir. Her bir servis için ayrı bir alt dizin bulunmaktadır.

## Mevcut Servisler

### Oracle Database

Oracle 11g veritabanı için Docker ortamı. Detaylı bilgi için [oracle/README.md](oracle/README.md) dosyasına bakınız.

```bash
cd docker/oracle
docker-compose up -d
```

## Yeni Servis Ekleme

Yeni bir servis eklemek için aşağıdaki adımları izleyin:

1. Servis için yeni bir dizin oluşturun:
   ```bash
   mkdir docker/yeni-servis
   ```

2. Servis için bir docker-compose.yml dosyası oluşturun:
   ```bash
   touch docker/yeni-servis/docker-compose.yml
   ```

3. <PERSON><PERSON> için gerekli yapılandırma dosyalarını ekleyin.

4. <PERSON><PERSON> için bir README.md dosyası oluşturun.

## Tüm Servisleri Başlatma

Tüm servisleri başlatmak için her bir servis dizininde docker-compose komutunu çalıştırın:

```bash
cd docker/oracle
docker-compose up -d
# Diğer servisler için de aynı işlemi tekrarlayın
```

## Tüm Servisleri Durdurma

Tüm servisleri durdurmak için her bir servis dizininde docker-compose down komutunu çalıştırın:

```bash
cd docker/oracle
docker-compose down
# Diğer servisler için de aynı işlemi tekrarlayın
```
