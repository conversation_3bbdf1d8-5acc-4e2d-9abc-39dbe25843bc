package iym.makos.service.makos;

import iym.common.model.entity.iym.MahkemeAidiyatDetayTalep;
import iym.common.service.db.DbMahkemeAidiyatDetayTalepService;
import iym.makos.dto.MahkemeAidiyatDetayTalepDTO;
import iym.makos.mapper.MahkemeAidiyatDetayTalepMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.util.Date;
import java.util.List;

/**
 * Service for MahkemeAidiyatDetayTalep operations
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MahkemeAidiyatDetayTalepService {

    private final DbMahkemeAidiyatDetayTalepService dbMahkemeAidiyatDetayTalepService;
    private final MahkemeAidiyatDetayTalepMapper mahkemeAidiyatDetayTalepMapper;

    /**
     * Get all mahkeme aidiyat detay talep records
     * @return List of MahkemeAidiyatDetayTalepDTO
     */
    public List<MahkemeAidiyatDetayTalepDTO> findAll() {
        List<MahkemeAidiyatDetayTalep> mahkemeAidiyatDetayTalepList = dbMahkemeAidiyatDetayTalepService.findAll();
        return mahkemeAidiyatDetayTalepMapper.toDtoList(mahkemeAidiyatDetayTalepList);
    }

    /**
     * Get mahkeme aidiyat detay talep by ID
     * @param id Mahkeme aidiyat detay talep ID
     * @return MahkemeAidiyatDetayTalepDTO
     * @throws ResponseStatusException if not found
     */
    public MahkemeAidiyatDetayTalepDTO findById(Long id) {
        return dbMahkemeAidiyatDetayTalepService.findById(id)
                .map(mahkemeAidiyatDetayTalepMapper::toDto)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Mahkeme aidiyat detay talep bulunamadı: " + id));
    }

    /**
     * Get mahkeme aidiyat detay talep records by mahkeme karar ID
     * @param mahkemeKararId Mahkeme karar ID
     * @return List of MahkemeAidiyatDetayTalepDTO
     */
    public List<MahkemeAidiyatDetayTalepDTO> findByMahkemeKararId(Long mahkemeKararId) {
        List<MahkemeAidiyatDetayTalep> mahkemeAidiyatDetayTalepList = dbMahkemeAidiyatDetayTalepService.findByMahkemeKararId(mahkemeKararId);
        return mahkemeAidiyatDetayTalepMapper.toDtoList(mahkemeAidiyatDetayTalepList);
    }

    /**
     * Get mahkeme aidiyat detay talep records by iliskili mahkeme karar ID
     * @param iliskiliMahkemeKararId İlişkili mahkeme karar ID
     * @return List of MahkemeAidiyatDetayTalepDTO
     */
    public List<MahkemeAidiyatDetayTalepDTO> findByIliskiliMahkemeKararId(Long iliskiliMahkemeKararId) {
        List<MahkemeAidiyatDetayTalep> mahkemeAidiyatDetayTalepList = dbMahkemeAidiyatDetayTalepService.findByIliskiliMahkemeKararId(iliskiliMahkemeKararId);
        return mahkemeAidiyatDetayTalepMapper.toDtoList(mahkemeAidiyatDetayTalepList);
    }

    /**
     * Get mahkeme aidiyat detay talep records by mahkeme karar detay ID
     * @param mahkemeKararDetayId Mahkeme karar detay ID
     * @return List of MahkemeAidiyatDetayTalepDTO
     */
    public List<MahkemeAidiyatDetayTalepDTO> findByMahkemeKararDetayId(Long mahkemeKararDetayId) {
        List<MahkemeAidiyatDetayTalep> mahkemeAidiyatDetayTalepList = dbMahkemeAidiyatDetayTalepService.findByMahkemeKararDetayId(mahkemeKararDetayId);
        return mahkemeAidiyatDetayTalepMapper.toDtoList(mahkemeAidiyatDetayTalepList);
    }

    /**
     * Get mahkeme aidiyat detay talep records by durum
     * @param durum Durum
     * @return List of MahkemeAidiyatDetayTalepDTO
     */
    public List<MahkemeAidiyatDetayTalepDTO> findByDurum(String durum) {
        List<MahkemeAidiyatDetayTalep> mahkemeAidiyatDetayTalepList = dbMahkemeAidiyatDetayTalepService.findByDurum(durum);
        return mahkemeAidiyatDetayTalepMapper.toDtoList(mahkemeAidiyatDetayTalepList);
    }

    /**
     * Get mahkeme aidiyat detay talep records by tarih between
     * @param startDate Start date
     * @param endDate End date
     * @return List of MahkemeAidiyatDetayTalepDTO
     */
    public List<MahkemeAidiyatDetayTalepDTO> findByTarihBetween(Date startDate, Date endDate) {
        List<MahkemeAidiyatDetayTalep> mahkemeAidiyatDetayTalepList = dbMahkemeAidiyatDetayTalepService.findByTarihBetween(startDate, endDate);
        return mahkemeAidiyatDetayTalepMapper.toDtoList(mahkemeAidiyatDetayTalepList);
    }


    /**
     * Get paginated mahkeme aidiyat detay talep records
     * @param pageable Pageable
     * @return Page of MahkemeAidiyatDetayTalepDTO
     */
    public Page<MahkemeAidiyatDetayTalepDTO> findAll(Pageable pageable) {
        Page<MahkemeAidiyatDetayTalep> mahkemeAidiyatDetayTalepPage = dbMahkemeAidiyatDetayTalepService.findAll(pageable);
        List<MahkemeAidiyatDetayTalepDTO> dtoList = mahkemeAidiyatDetayTalepMapper.toDtoList(mahkemeAidiyatDetayTalepPage.getContent());
        return new PageImpl<>(dtoList, pageable, mahkemeAidiyatDetayTalepPage.getTotalElements());
    }




    /**
     * Delete mahkeme aidiyat detay talep
     * @param id Mahkeme aidiyat detay talep ID
     */
    public void delete(Long id) {
        MahkemeAidiyatDetayTalep mahkemeAidiyatDetayTalep = dbMahkemeAidiyatDetayTalepService.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Mahkeme aidiyat detay talep bulunamadı: " + id));
        
        dbMahkemeAidiyatDetayTalepService.delete(mahkemeAidiyatDetayTalep);
        log.info("Mahkeme aidiyat detay talep silindi: {}", id);
    }
}
