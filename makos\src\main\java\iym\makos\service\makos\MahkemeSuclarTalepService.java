package iym.makos.service.makos;

import iym.common.model.entity.iym.MahkemeSuclarTalep;
import iym.common.service.db.DbMahkemeSuclarTalepService;
import iym.makos.dto.MahkemeSuclarTalepDTO;
import iym.makos.mapper.MahkemeSuclarTalepMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;
import java.util.Optional;

/**
 * Service for MahkemeSuclarTalep operations
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MahkemeSuclarTalepService {

    private final DbMahkemeSuclarTalepService dbMahkemeSuclarTalepService;
    private final MahkemeSuclarTalepMapper mahkemeSuclarTalepMapper;

    /**
     * Get all mahkeme suçlar talep records
     * @return List of MahkemeSuclarTalepDTO
     */

    public List<MahkemeSuclarTalepDTO> findAll() {
        List<MahkemeSuclarTalep> mahkemeSuclarTalepList = dbMahkemeSuclarTalepService.findAll();
        return mahkemeSuclarTalepMapper.toDtoList(mahkemeSuclarTalepList);
    }

    /**
     * Get all mahkeme suçlar talep records with pagination
     * @param pageable Pagination information
     * @return Page of MahkemeSuclarTalepDTO
     */

    public Page<MahkemeSuclarTalepDTO> findAll(Pageable pageable) {
        Page<MahkemeSuclarTalep> mahkemeSuclarTalepPage = dbMahkemeSuclarTalepService.findAll(pageable);
        List<MahkemeSuclarTalepDTO> dtoList = mahkemeSuclarTalepMapper.toDtoList(mahkemeSuclarTalepPage.getContent());
        return new PageImpl<>(dtoList, pageable, mahkemeSuclarTalepPage.getTotalElements());
    }

    /**
     * Get mahkeme suçlar talep by ID
     * @param id Mahkeme suçlar talep ID
     * @return MahkemeSuclarTalepDTO
     * @throws ResponseStatusException if not found
     */

    public MahkemeSuclarTalepDTO findById(Long id) {
        return dbMahkemeSuclarTalepService.findById(id)
                .map(mahkemeSuclarTalepMapper::toDto)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Mahkeme suçlar talep bulunamadı"));
    }

    /**
     * Get mahkeme suçlar talep by mahkeme karar ID
     * @param mahkemeKararTalepId Mahkeme karar ID
     * @return List of MahkemeSuclarTalepDTO
     */

    public List<MahkemeSuclarTalepDTO> findByMahkemeKararId(Long mahkemeKararTalepId) {
        List<MahkemeSuclarTalep> mahkemeSuclarTalepList = dbMahkemeSuclarTalepService.findByMahkemeKararTalepId(mahkemeKararTalepId);
        return mahkemeSuclarTalepMapper.toDtoList(mahkemeSuclarTalepList);
    }



    /**
     * Get mahkeme suçlar talep by mahkeme karar ID and mahkeme suç tip kod
     * @param mahkemeKararTalepId Mahkeme karar ID
     * @param sucTipKod Mahkeme suç tip kod
     * @return MahkemeSuclarTalepDTO
     * @throws ResponseStatusException if not found
     */

    public MahkemeSuclarTalepDTO findByMahkemeKararTalepIdAndSucTipKodu(Long mahkemeKararTalepId, String sucTipKod) {
        return dbMahkemeSuclarTalepService.findByMahkemeKararTalepIdAndSucTipKodu(mahkemeKararTalepId, sucTipKod)
                .map(mahkemeSuclarTalepMapper::toDto)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Mahkeme suçlar talep bulunamadı"));
    }

    /**
     * Create new mahkeme suçlar talep
     * @param mahkemeSuclarTalepDTO MahkemeSuclarTalepDTO
     * @return Created MahkemeSuclarTalepDTO
     */

    public MahkemeSuclarTalepDTO create(MahkemeSuclarTalepDTO mahkemeSuclarTalepDTO) {
        // Check if mahkeme suçlar talep already exists
        if (mahkemeSuclarTalepDTO.getMahkemeKararTalepId() != null &&
            mahkemeSuclarTalepDTO.getSucTipKodu() != null) {

            Optional<MahkemeSuclarTalep> existingMahkemeSuclarTalep =
                dbMahkemeSuclarTalepService.findByMahkemeKararTalepIdAndSucTipKodu(
                    mahkemeSuclarTalepDTO.getMahkemeKararTalepId(),
                    mahkemeSuclarTalepDTO.getSucTipKodu());

            if (existingMahkemeSuclarTalep.isPresent()) {
                throw new ResponseStatusException(HttpStatus.CONFLICT, "Bu mahkeme karar ID ve suç tip kodu için kayıt zaten mevcut");
            }
        }

        MahkemeSuclarTalep mahkemeSuclarTalep = mahkemeSuclarTalepMapper.toEntity(mahkemeSuclarTalepDTO);
        dbMahkemeSuclarTalepService.save(mahkemeSuclarTalep);
        return mahkemeSuclarTalepMapper.toDto(mahkemeSuclarTalep);
    }

    /**
     * Update existing mahkeme suçlar talep
     * @param id Mahkeme suçlar talep ID
     * @param mahkemeSuclarTalepDTO MahkemeSuclarTalepDTO with updated values
     * @return Updated MahkemeSuclarTalepDTO
     * @throws ResponseStatusException if not found
     */

    public MahkemeSuclarTalepDTO update(Long id, MahkemeSuclarTalepDTO mahkemeSuclarTalepDTO) {
        MahkemeSuclarTalep existingMahkemeSuclarTalep = dbMahkemeSuclarTalepService.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Mahkeme suçlar talep bulunamadı"));

        // Check if the update would create a duplicate
        if (mahkemeSuclarTalepDTO.getMahkemeKararTalepId() != null &&
            mahkemeSuclarTalepDTO.getSucTipKodu() != null) {

            Optional<MahkemeSuclarTalep> duplicateCheck =
                dbMahkemeSuclarTalepService.findByMahkemeKararTalepIdAndSucTipKodu(
                    mahkemeSuclarTalepDTO.getMahkemeKararTalepId(),
                    mahkemeSuclarTalepDTO.getSucTipKodu());

            if (duplicateCheck.isPresent() && !duplicateCheck.get().getId().equals(id)) {
                throw new ResponseStatusException(HttpStatus.CONFLICT, "Bu mahkeme karar ID ve suç tip kodu için başka bir kayıt zaten mevcut");
            }
        }

        MahkemeSuclarTalep updatedMahkemeSuclarTalep = mahkemeSuclarTalepMapper.updateEntityFromDto(existingMahkemeSuclarTalep, mahkemeSuclarTalepDTO);
        dbMahkemeSuclarTalepService.update(updatedMahkemeSuclarTalep);
        return mahkemeSuclarTalepMapper.toDto(updatedMahkemeSuclarTalep);
    }

    /**
     * Delete mahkeme suçlar talep by ID
     * @param id Mahkeme suçlar talep ID
     * @throws ResponseStatusException if not found
     */

    public void delete(Long id) {
        MahkemeSuclarTalep mahkemeSuclarTalep = dbMahkemeSuclarTalepService.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Mahkeme suçlar talep bulunamadı"));

        dbMahkemeSuclarTalepService.delete(mahkemeSuclarTalep);
    }
}
