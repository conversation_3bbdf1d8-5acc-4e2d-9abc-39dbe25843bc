
/********************
DDL 
********************/



CREATE TABLE IYM.GOREVLER2 
(
  GOREV VARCHAR2(75 BYTE) NOT NULL 
, GOREV_KODU NUMBER NOT NULL 
, GOREV_IMZA_ADI VARCHAR2(100 BYTE) 
, IMZA_YETKI CHAR(1 BYTE) 
, SILINDI NUMBER 
, GOREV_KODU2 VARCHAR2(100 BYTE) 
, GOREV_TIPI VARCHAR2(100 BYTE) 
, ONCELIK NUMBER 
, BASLAMA_TARIHI DATE 
, BITIS_TARIHI DATE 
, CONSTRAINT GOREVLER2Y1_PRM PRIMARY KEY (GOREV , GOREV_KODU )  ENABLE 
) 
CREATE INDEX IYM.I_GOREVLER2Y_GK ON IYM.GOREVLER2 (GOREV_KODU ASC) 
;


CREATE TABLE IYM.KULLANICILAR 
(
  ID NUMBER NOT NULL 
, ADI VARCHAR2(20 BYTE) NOT NULL 
, SOYADI VARCHAR2(40 BYTE) NOT NULL 
, KULLANICI_ADI VARCHAR2(20 BYTE) NOT NULL 
, SIFRE CHAR(32 BYTE) NOT NULL 
, GOREVI VARCHAR2(6 BYTE) 
, YETKI NUMBER 
, BIRIMI VARCHAR2(4 BYTE) 
, GOREV_TANIMI VARCHAR2(300 BYTE) 
, TEL VARCHAR2(16 BYTE) 
, POSTA VARCHAR2(55 BYTE) 
, FAX VARCHAR2(55 BYTE) 
, RESMI_KOD VARCHAR2(12 BYTE) 
, KIM_KUL_ID NUMBER 
, DURUMU VARCHAR2(20 BYTE) 
, IMZA_YETKISI CHAR(1 BYTE) 
, IMZA_DOSYASI VARCHAR2(50 BYTE) 
, EKSTRAGUVENLIK VARCHAR2(1 BYTE) 
, TEMSIL_EDILEN_KURUM VARCHAR2(2 BYTE) 
, CID VARCHAR2(32 BYTE) 
, TCK VARCHAR2(11 BYTE) 
, PAROLA_DEGISIM_TARIHI DATE 
, AKADEMIK_UNVAN VARCHAR2(10 BYTE) 
, GRUP_KODU VARCHAR2(10 BYTE) 
, CONSTRAINT KULLANICI_ID_IDX PRIMARY KEY ( ID  ) ENABLE 
)   

CREATE UNIQUE INDEX IYM.KULLANICI_ADI ON IYM.KULLANICILAR (KULLANICI_ADI ASC) 
;

CREATE TABLE IYM.KULLANICI_KURUMLAR 
(
  KURUM_KOD NUMBER 
, KURUM_AD VARCHAR2(100 BYTE) 
, KURUM VARCHAR2(10 BYTE) 
) ;

CREATE TABLE IYM.KULLANICI_KURUM 
(
  KULLANICI_ID NUMBER NOT NULL 
, KURUM_KOD VARCHAR2(20 BYTE) NOT NULL 
, ID NUMBER 
, CONSTRAINT KURUM_KULLANICI_IDX PRIMARY KEY 
  (
    KULLANICI_ID 
  , KURUM_KOD 
  )
  ENABLE 
);



CREATE TABLE IYM.KULLANICI_GOREV2 
(
  ID NUMBER NOT NULL 
, KULLANICI_ID NUMBER NOT NULL 
, GOREV_ID NUMBER NOT NULL 
, VEKIL CHAR(1 BYTE) NOT NULL 
, TARIH DATE 
, DURUM CHAR(1 BYTE) 
, TARIH2 DATE 
, ONCELIK NUMBER 
, CONSTRAINT K_GOREV21 PRIMARY KEY (ID) ENABLE 
) 
CREATE INDEX IYM.IND_KULL_GOR_ID ON IYM.KULLANICI_GOREV2 (KULLANICI_ID ASC) 
CREATE INDEX IYM.KULLANICI_GOREV_ID ON IYM.KULLANICI_GOREV2 (GOREV_ID ASC) 
;


CREATE TABLE IYM.EVRAK_KAYIT 
(
  ID NUMBER NOT NULL 
, EVRAK_SIRA_NO VARCHAR2(30 BYTE) 
, EVRAK_NO VARCHAR2(50 BYTE) 
, GIRIS_TARIH DATE NOT NULL 
, EVRAK_TARIHI DATE NOT NULL 
, EVRAK_GELDIGI_KURUM VARCHAR2(10 BYTE) 
, KAY_KULLANICI NUMBER 
, EVRAK_TIPI VARCHAR2(10 BYTE) 
, HAVALE_BIRIM VARCHAR2(10 BYTE) 
, ACIKLAMA VARCHAR2(4000 BYTE) 
, GEL_IL VARCHAR2(4 BYTE) 
, EVRAK_KONUSU VARCHAR2(200 BYTE) 
, ARSIV_DOSYA_NO VARCHAR2(20 BYTE) 
, DURUMU VARCHAR2(20 BYTE) 
, EVRAK_YONU VARCHAR2(100 BYTE) 
, ONAY_TARIHI DATE 
, ACILMI CHAR(1 BYTE) 
, SORUSTURMA_NO VARCHAR2(20 BYTE) 
, MAHKEME_KARAR_NO VARCHAR2(20 BYTE) 
, UNIQ_COL NUMBER 
, ELDEN_TESLIM VARCHAR2(1 BYTE) 
, TEKITMI CHAR(1 BYTE) 
, ASIL_EVRAK CHAR(1 BYTE) DEFAULT 'H' 
, ONCELIK VARCHAR2(50 BYTE) DEFAULT NULL 
, EVRAK_GELDIGI_KURUM_ESKI VARCHAR2(10 BYTE) 
, EVRAK_GRUP VARCHAR2(300 BYTE) 
, ONAMA VARCHAR2(1 BYTE) 
) 
CREATE INDEX IYM.EVRAK_DURUM ON IYM.EVRAK_KAYIT (DURUMU ASC) 
CREATE INDEX IYM.EVRAK_GEL_KURUM ON IYM.EVRAK_KAYIT (EVRAK_GELDIGI_KURUM ASC) 
CREATE INDEX IYM.EVRAK_KAYIT_ACIL_GIRIS_TAR_IDX ON IYM.EVRAK_KAYIT (ACILMI ASC, GIRIS_TARIH ASC) 
CREATE INDEX IYM.EVRAK_KAYIT_GELID_DRM_IDX ON IYM.EVRAK_KAYIT (ID ASC) 
CREATE INDEX IYM.EVRAK_KAYIT_GELID_SORNO_IDX ON IYM.EVRAK_KAYIT (SORUSTURMA_NO ASC, GEL_IL ASC) 
CREATE INDEX IYM.EVRAK_KAYIT_HAV_BIR ON IYM.EVRAK_KAYIT (HAVALE_BIRIM ASC) 
CREATE INDEX IYM.EVRAK_KAYIT_SNO_IDX ON IYM.EVRAK_KAYIT (SUBSTR(EVRAK_SIRA_NO, -10, 10) ASC) 
CREATE INDEX IYM.EVRAK_KAYIT_TRH_DRM_IDX ON IYM.EVRAK_KAYIT (GIRIS_TARIH ASC, DURUMU ASC) 
CREATE UNIQUE INDEX IYM.EVRAK_NO_TARIH_KURUM_ILD_IDX ON IYM.EVRAK_KAYIT (EVRAK_NO ASC, EVRAK_TARIHI ASC, GEL_IL ASC, EVRAK_GELDIGI_KURUM ASC, DURUMU ASC) 
CREATE UNIQUE INDEX IYM.EVRAK_SNO_IDX ON IYM.EVRAK_KAYIT (EVRAK_SIRA_NO ASC) 
CREATE INDEX IYM.IDX_ID_GIRIS_TAR ON IYM.EVRAK_KAYIT (ID ASC, GIRIS_TARIH ASC) 

CREATE TABLE IYM.EVRAK_GELEN_KURUMLAR 
(
  ID NUMBER NOT NULL 
, KURUM_KOD VARCHAR2(10 BYTE) NOT NULL 
, KURUM_ADI VARCHAR2(50 BYTE) NOT NULL 
, KURUM VARCHAR2(64 BYTE) 
, IDX NUMBER 
, CONSTRAINT EVRAK_GELEN_KURUMLAR_PRM PRIMARY KEY 
  (
    KURUM_KOD 
  )
  ENABLE 
)

CREATE TABLE IYM.ILLER 
(
  IL_KOD VARCHAR2(4 BYTE) NOT NULL 
, IL_ADI VARCHAR2(50 BYTE) 
, ILCE_ADI VARCHAR2(50 BYTE) 
, CONSTRAINT IL_KOD_IDX PRIMARY KEY 
  (
    IL_KOD 
  )
  ENABLE 
) 





/*

butun tipler icin oncelikler EVRAK_KAYIT tablosuna kayit

MAH_KARAR_TIPLERI
EVRAK_GELEN_KURUMLAR
ILLER
KULLANICI_KURUM

----------------ILETISIMIN_DENETLENMESI
MAHKEME_KARAR_TALEP
HEDEFLER_AIDIYAT_TALEP
xmlEvrakKaydet
	herbir mahkeme karar icin
		mahkemeKararTalepKaydet -> MAHKEME_KARAR_TALEP
			herbir mahkeme karar Aidiyat Talep Kaydet -> MAHKEME_AIDIYAT_TALEP
			herbir mahkeme karar suc Talep Kaydet -> MAHKEME_SUCLAR_TALEP
			herbir mahkeme karar Detay icin detay kaydet -> DMAHKEME_KARAR_TALEP
				herbir detay icin MAHKEME_AIDIYAT_DETAY_TALEP
			her bir hedef icin: -> HEDEFLER_TALEP
			HEDEFLER_TALEP	
				her bir hedef icin 
				detay ve aidiyat lari kaydet: 
				aidiyat : MAHKEME_HEDEFLER_AIDIYAT_TALEP
				detay : DMAHKEME_KARAR_TALEP
				
	
	onayli hedef canak  canak_numaralar	tablosundan kontrol ediliyor	
	evrak_mahkeme_karar_islem
	
			
-----------------------ILETISIMIN_TESPITI
xmlEvrakKaydet
	herbir mahkeme karar icin
	HTS_MAHKEME_KARAR_TALEP
	her bir hedefler talebi kaydet  : HTS_HEDEFLER_TALEP
	
	HTS_MAHKEME_KARAR_TALEP
	
	
-----------------------GENEL_EVRAK
	
			

		

*/






CREATE TABLE IYM.MAHKEME_KARAR_TALEP 
(
  ID NUMBER NOT NULL 
, EVRAK_ID NUMBER NOT NULL 
, KULLANICI_ID NUMBER NOT NULL 
, KAYIT_TARIHI DATE NOT NULL 
, DURUM VARCHAR2(20 BYTE) 
, HUKUK_BIRIM VARCHAR2(50 BYTE) 
, KARAR_TIP VARCHAR2(20 BYTE) 
, MAH_KARAR_BAS_TAR DATE 
, MAH_KARAR_BITIS_TAR DATE 
, MAHKEME_ADI VARCHAR2(250 BYTE) 
, MAHKEME_KARAR_NO VARCHAR2(50 BYTE) 
, MAHKEME_ILI VARCHAR2(4 BYTE) NOT NULL 
, ACIKLAMA VARCHAR2(500 BYTE) 
, HAKIM_SICIL_NO VARCHAR2(20 BYTE) 
, SORUSTURMA_NO VARCHAR2(50 BYTE) 
, GERCEK_MAH_ID NUMBER 
, MAHKEME_KODU VARCHAR2(10 BYTE) 
, CONSTRAINT MAHKEME_KARAR_TALEP_ID_IDX PRIMARY KEY 
  (
    ID 
  )
  ENABLE 
) 
CREATE INDEX IYM.MAHKEME_KAR_TALEP_EVRAK_ID_IDX ON IYM.MAHKEME_KARAR_TALEP (EVRAK_ID ASC) 
CREATE INDEX IYM.MAHKEME_KAR_TALEP_UNIQ ON IYM.MAHKEME_KARAR_TALEP (MAHKEME_KARAR_NO ASC, MAHKEME_ADI ASC, MAHKEME_ILI ASC) 



CREATE TABLE IYM.MAHKEME_AIDIYAT_TALEP 
(
  ID NUMBER NOT NULL 
, MAHKEME_ID NUMBER NOT NULL 
, AIDIYAT_KOD VARCHAR2(25 BYTE) NOT NULL 
, DURUMU VARCHAR2(10 BYTE) 
) 
CREATE UNIQUE INDEX IYM.MAH_AIDIYAT_TALEP_ID ON IYM.MAHKEME_AIDIYAT_TALEP (MAHKEME_ID ASC, AIDIYAT_KOD ASC) 


CREATE TABLE IYM.MAHKEME_SUCLAR_TALEP 
(
  ID NUMBER NOT NULL 
, MAHKEME_KARAR_ID NUMBER NOT NULL 
, MAHKEME_SUC_TIP_KOD VARCHAR2(10 BYTE) NOT NULL 
, DURUMU VARCHAR2(20 BYTE) 
)CREATE UNIQUE INDEX IYM.MAH_SUCLAR_TALEP_IDX ON IYM.MAHKEME_SUCLAR_TALEP (MAHKEME_KARAR_ID ASC, MAHKEME_SUC_TIP_KOD ASC) 


CREATE TABLE IYM.DMAHKEME_KARAR_TALEP 
(
  MAHKEME_KARAR_ID NUMBER NOT NULL 
, EVRAK_ID NUMBER NOT NULL 
, KULLANICI_ID NUMBER NOT NULL 
, KAYIT_TARIHI DATE NOT NULL 
, DURUM VARCHAR2(20 BYTE) 
, KARAR_TIP_DETAY VARCHAR2(20 BYTE) 
, MAHKEME_ADI_DETAY VARCHAR2(250 BYTE) 
, MAHKEME_KARAR_NO_DETAY VARCHAR2(50 BYTE) 
, MAHKEME_ILI_DETAY VARCHAR2(6 BYTE) NOT NULL 
, SORUSTURMA_NO_DETAY VARCHAR2(50 BYTE) 
, ILISKILI_MAHKEME_KARAR_ID NUMBER 
, MAHKEME_KODU_DETAY VARCHAR2(10 BYTE) 
, ACIKLAMA_DETAY VARCHAR2(500 BYTE) 
, ID NUMBER 
) 
CREATE INDEX IYM.DMAHKEME_KARAR_TALEP_ID ON IYM.DMAHKEME_KARAR_TALEP (ID ASC) 


CREATE TABLE IYM.MAHKEME_AIDIYAT_DETAY_TALEP 
(
  ID NUMBER NOT NULL 
, ILISKILI_MAHKEME_KARAR_ID NUMBER 
, MAHKEME_KARAR_ID NUMBER 
, MAHKEME_AIDIYAT_KODU_EKLE VARCHAR2(25 BYTE) 
, MAHKEME_AIDIYAT_KODU_CIKAR VARCHAR2(25 BYTE) 
, TARIH DATE NOT NULL 
, DURUM VARCHAR2(15 BYTE) 
, MAHKEME_KARAR_DETAY_ID NUMBER 
, CONSTRAINT MAHKEME_AIDIYAT_DETAY_PK PRIMARY KEY 
  (
    ID 
  )
  ENABLE 
) 


CREATE TABLE IYM.HEDEFLER_TALEP 
(
  ID NUMBER NOT NULL 
, BIRIM_KOD NUMBER 
, KULLANICI_ID NUMBER 
, TEK_MASA_KUL_ID NUMBER 
, HEDEF_NO VARCHAR2(100 BYTE) 
, HEDEF_ADI VARCHAR2(100 BYTE) 
, HEDEF_SOYADI VARCHAR2(100 BYTE) 
, BASLAMA_TARIHI DATE 
, SURESI NUMBER 
, SURE_TIPI NUMBER 
, UZATMA_SAYISI NUMBER 
, DURUMU VARCHAR2(100 BYTE) 
, ACIKLAMA VARCHAR2(250 BYTE) 
, MAHKEME_KARAR_ID NUMBER 
, HEDEF_AIDIYAT_ID NUMBER 
, GRUP_KOD NUMBER 
, AIDIYAT_KOD VARCHAR2(35 BYTE) 
, UNIQ_KOD NUMBER 
, KAYIT_TARIHI DATE 
, TANIMLAMA_TARIHI DATE 
, KAPATMA_KARAR_ID NUMBER 
, KAPATMA_TARIHI DATE 
, IMHA VARCHAR2(20 BYTE) 
, IMHA_TARIHI DATE 
, UZATMA_ID NUMBER 
, ACILMI CHAR(1 BYTE) 
, HEDEF_118_ADI VARCHAR2(50 BYTE) 
, HEDEF_118_SOYADI VARCHAR2(50 BYTE) 
, HEDEF_118_ADRES VARCHAR2(250 BYTE) 
, HEDEF_TIPI NUMBER 
, CANAK_NO VARCHAR2(100 CHAR) 
, CONSTRAINT HEDEF_TALEP_TELNO_ID_IDX PRIMARY KEY 
  (
    ID 
  )
  ENABLE 
) 
CREATE INDEX IYM.HEDEFLER_TALEP_AIDIYAT_IDX ON IYM.HEDEFLER_TALEP (AIDIYAT_KOD ASC) 
CREATE INDEX IYM.HEDEFLER_TALEP_DURUM ON IYM.HEDEFLER_TALEP (DURUMU ASC) 
CREATE INDEX IYM.HEDEFLER_TALEP_MAHKEME_ID_IDX ON IYM.HEDEFLER_TALEP (MAHKEME_KARAR_ID ASC) 
CREATE INDEX IYM.HEDEF_TALEP_GRUP_KOD ON IYM.HEDEFLER_TALEP (GRUP_KOD ASC) 
CREATE UNIQUE INDEX IYM.HEDEF_TALEP_NO_MKNO_UNIQ_KOD ON IYM.HEDEFLER_TALEP (HEDEF_NO ASC, MAHKEME_KARAR_ID ASC, HEDEF_TIPI ASC, UNIQ_KOD ASC) 
ALTER TABLE IYM.HEDEFLER_TALEP
ADD CONSTRAINT HEDEF_TALEP_HEDEF_TIPI_FRG FOREIGN KEY
(
  HEDEF_TIPI 
)
REFERENCES IYM.HEDEF_TIPLERI
(
  HEDEF_KODU 
)
ENABLE
ALTER TABLE IYM.HEDEFLER_TALEP
ADD CONSTRAINT HEDEF_TALEP_MAHKEME_KARAR_FRG FOREIGN KEY
(
  MAHKEME_KARAR_ID 
)
REFERENCES IYM.MAHKEME_KARAR_TALEP
(
  ID 
)
ENABLE


CREATE TABLE IYM.HEDEF_TIPLERI 
(
  ID NUMBER 
, HEDEF_KODU NUMBER NOT NULL 
, HEDEF_TIPI VARCHAR2(25 BYTE) 
, SONLANDIRMAMI CHAR(1 BYTE) 
, KARSILIGI NUMBER 
, SNO NUMBER 
, HEDEF_TANIM VARCHAR2(16 BYTE) 
, DURUM VARCHAR2(8 BYTE) 
, HITAP_TIP VARCHAR2(8 BYTE) 
, HITAP_ICERIK_TIP VARCHAR2(8 BYTE) 
, HITAP_ICINDEMI VARCHAR2(8 BYTE) 
, HITAP_EH CHAR(1 BYTE) 
, MINL NUMBER 
, MAXL NUMBER 
, IMHA_YAPILSINMI VARCHAR2(8 BYTE) 
, TASINABILIRMI VARCHAR2(1 BYTE) 
, AKTIFMI NUMBER DEFAULT 1 
, HITAPA_GONDERILECEKMI NUMBER(1, 0) DEFAULT 0 
, CONSTRAINT HEDEF_TIPLERI_PRM PRIMARY KEY 
  (
    HEDEF_KODU 
  )
  ENABLE 
) 
CREATE INDEX IYM.IND_HEDEF_TIPI_UPPER ON IYM.HEDEF_TIPLERI (UPPER(HEDEF_TIPI) ASC) 


CREATE TABLE IYM.MAHKEME_HEDEFLER_AIDIYAT_TALEP 
(
  ID NUMBER NOT NULL 
, HEDEF_ID NUMBER NOT NULL 
, AIDIYAT_KOD VARCHAR2(15 BYTE) NOT NULL 
, TARIH DATE NOT NULL 
, MAHKEME_KARAR_ID NUMBER NOT NULL 
, DURUMU VARCHAR2(10 BYTE) 
, KULLANICI_ID NUMBER 
, CONSTRAINT MT_HEDEF_AIDIYAT_ID_IDX PRIMARY KEY 
  (
    ID 
  )
  ENABLE 
) 
CREATE INDEX IYM.MT_IND_HED_AID_HED_ID ON IYM.MAHKEME_HEDEFLER_AIDIYAT_TALEP (HEDEF_ID ASC) 
CREATE INDEX IYM.MT_IND_HED_AID_MAH_ID ON IYM.MAHKEME_HEDEFLER_AIDIYAT_TALEP (MAHKEME_KARAR_ID ASC) 




CREATE TABLE IYM.HEDEFLER_AIDIYAT_TALEP 
(
  ID NUMBER NOT NULL 
, HEDEF_ID NUMBER NOT NULL 
, AIDIYAT_KOD VARCHAR2(15 BYTE) NOT NULL 
, TARIH DATE NOT NULL 
, KULLANICI_ID NUMBER NOT NULL 
, DURUMU VARCHAR2(15 BYTE) 
, CONSTRAINT HEDEFLER_AIDIYAT_TALEP_ID_IDX PRIMARY KEY 
  (
    ID 
  )
  ENABLE 
) 
ALTER TABLE IYM.HEDEFLER_AIDIYAT_TALEP
ADD CONSTRAINT HEDEFLER_AIDIYAT_TALEP_U_IDX UNIQUE 
(
  HEDEF_ID 
, AIDIYAT_KOD 
)
ENABLE


CREATE TABLE IYM.CANAK_NUMARALAR 
(
  ID NUMBER NOT NULL 
, CANAK_NO VARCHAR2(20 BYTE) NOT NULL 
, KURUM_KOD VARCHAR2(2 BYTE) NOT NULL 
, EKLEME_TARIH DATE 
, KUTU NUMBER 
, ACIKLAMA VARCHAR2(300 BYTE) 
, EKLEYEN_ID NUMBER 
) 
CREATE INDEX IYM.CANAK_NUMARALAR_IDX ON IYM.CANAK_NUMARALAR (CANAK_NO ASC) 






--Seviye 0 oldugu zaman ilgili kullanici onune duser
CREATE TABLE IYM.EVRAK_MAHKEME_KARAR_ISLEM 
(
  EVRAK_ID NUMBER NOT NULL 
, KURUM VARCHAR2(10 BYTE) 
, SEVIYE VARCHAR2(1 BYTE) DEFAULT 0 
, CONSTRAINT PK_EVRAK_ID PRIMARY KEY 
  (
    EVRAK_ID 
  )
  ENABLE 
) 


--------------------------ILET

CREATE TABLE IYM.HTS_MAHKEME_KARAR_TALEP 
(
  ID NUMBER NOT NULL 
, EVRAK_ID NUMBER NOT NULL 
, KULLANICI_ID NUMBER NOT NULL 
, KAYIT_TARIHI DATE NOT NULL 
, DURUM VARCHAR2(100 BYTE) 
, KARAR_TIP VARCHAR2(100 BYTE) NOT NULL 
, HUKUK_BIRIM VARCHAR2(100 BYTE) NOT NULL 
, MAHKEME_ILI VARCHAR2(100 BYTE) NOT NULL 
, MAHKEME_KODU VARCHAR2(100 BYTE) NOT NULL 
, MAHKEME_ADI VARCHAR2(1000 BYTE) NOT NULL 
, ACIKLAMA VARCHAR2(1000 BYTE) 
, MAHKEME_KARAR_NO VARCHAR2(100 BYTE) 
, SORUSTURMA_NO VARCHAR2(100 BYTE) 
, CONSTRAINT PK_HTS_MAHKEME_ID PRIMARY KEY 
  (
    ID 
  )
  ENABLE 
) 
CREATE INDEX IYM.EVRAK_ID_IDX ON IYM.HTS_MAHKEME_KARAR_TALEP (EVRAK_ID ASC) 


CREATE TABLE IYM.HTS_HEDEFLER_TALEP 
(
  ID NUMBER NOT NULL 
, MAHKEME_KARAR_ID NUMBER NOT NULL 
, HEDEF_NO VARCHAR2(100 BYTE) NOT NULL 
, KARSI_HEDEF_NO VARCHAR2(100 BYTE) 
, SORGU_TIPI VARCHAR2(100 BYTE) NOT NULL 
, BASLANGIC_TARIHI DATE 
, BITIS_TARIHI DATE 
, TESPIT_TURU VARCHAR2(100 BYTE) NOT NULL 
, KULLANICI_ID VARCHAR2(10 BYTE) NOT NULL 
, DURUMU VARCHAR2(100 BYTE) 
, CONSTRAINT PK_HTS_HEDEF_ID PRIMARY KEY 
  (
    ID 
  )
  ENABLE 
) 
CREATE INDEX IYM.HTS_HEDEF_NO_IDX ON IYM.HTS_HEDEFLER_TALEP (HEDEF_NO ASC) 
CREATE INDEX IYM.HTS_MAHKEME_KARAR_ID_IDX ON IYM.HTS_HEDEFLER_TALEP (MAHKEME_KARAR_ID ASC) 




