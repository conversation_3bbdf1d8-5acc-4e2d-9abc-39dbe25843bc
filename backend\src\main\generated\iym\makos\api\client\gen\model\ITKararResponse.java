/*
 * MAKOS OpenAPI definition
 * MAKOS Application
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package iym.makos.api.client.gen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.util.UUID;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * ITKararResponse
 */
@JsonPropertyOrder({
  ITKararResponse.JSON_PROPERTY_REQUEST_ID,
  ITKararResponse.JSON_PROPERTY_RESPONSE_CODE,
  ITKararResponse.JSON_PROPERTY_RESPONSE_MESSAGE
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class ITKararResponse {
  public static final String JSON_PROPERTY_REQUEST_ID = "requestId";
  private UUID requestId;

  /**
   * Gets or Sets responseCode
   */
  public enum ResponseCodeEnum {
    SUCCESS("SUCCESS"),
    
    FAILED("FAILED");

    private String value;

    ResponseCodeEnum(String value) {
      this.value = value;
    }

    @JsonValue
    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static ResponseCodeEnum fromValue(String value) {
      for (ResponseCodeEnum b : ResponseCodeEnum.values()) {
        if (b.value.equals(value)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + value + "'");
    }
  }

  public static final String JSON_PROPERTY_RESPONSE_CODE = "responseCode";
  private ResponseCodeEnum responseCode;

  public static final String JSON_PROPERTY_RESPONSE_MESSAGE = "responseMessage";
  private String responseMessage;

  public ITKararResponse() {
  }

  public ITKararResponse requestId(UUID requestId) {
    
    this.requestId = requestId;
    return this;
  }

   /**
   * Get requestId
   * @return requestId
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_REQUEST_ID)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public UUID getRequestId() {
    return requestId;
  }


  @JsonProperty(JSON_PROPERTY_REQUEST_ID)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setRequestId(UUID requestId) {
    this.requestId = requestId;
  }


  public ITKararResponse responseCode(ResponseCodeEnum responseCode) {
    
    this.responseCode = responseCode;
    return this;
  }

   /**
   * Get responseCode
   * @return responseCode
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_RESPONSE_CODE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public ResponseCodeEnum getResponseCode() {
    return responseCode;
  }


  @JsonProperty(JSON_PROPERTY_RESPONSE_CODE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setResponseCode(ResponseCodeEnum responseCode) {
    this.responseCode = responseCode;
  }


  public ITKararResponse responseMessage(String responseMessage) {
    
    this.responseMessage = responseMessage;
    return this;
  }

   /**
   * Get responseMessage
   * @return responseMessage
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_RESPONSE_MESSAGE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getResponseMessage() {
    return responseMessage;
  }


  @JsonProperty(JSON_PROPERTY_RESPONSE_MESSAGE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setResponseMessage(String responseMessage) {
    this.responseMessage = responseMessage;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ITKararResponse itKararResponse = (ITKararResponse) o;
    return Objects.equals(this.requestId, itKararResponse.requestId) &&
        Objects.equals(this.responseCode, itKararResponse.responseCode) &&
        Objects.equals(this.responseMessage, itKararResponse.responseMessage);
  }

  @Override
  public int hashCode() {
    return Objects.hash(requestId, responseCode, responseMessage);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ITKararResponse {\n");
    sb.append("    requestId: ").append(toIndentedString(requestId)).append("\n");
    sb.append("    responseCode: ").append(toIndentedString(responseCode)).append("\n");
    sb.append("    responseMessage: ").append(toIndentedString(responseMessage)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

