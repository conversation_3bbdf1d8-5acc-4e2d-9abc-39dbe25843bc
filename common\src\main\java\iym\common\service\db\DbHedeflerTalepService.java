package iym.common.service.db;

import iym.common.model.entity.iym.HedeflerTalep;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Service interface for HedeflerTalep entity
 */
public interface DbHedeflerTalepService extends GenericDbService<HedeflerTalep, Long> {

    List<HedeflerTalep> findByMahkemeKararTalepId(Long mahkemeKararTalepId);

    List<HedeflerTalep> findByHedefNo(String hedefNo);

    List<HedeflerTalep> findByBaslamaTarihiBetween(Date startDate, Date endDate);
    
    List<HedeflerTalep> findByKayitTarihiBetween(Date startDate, Date endDate);
    
    List<HedeflerTalep> findByTanimlamaTarihiBetween(Date startDate, Date endDate);
    
    List<HedeflerTalep> findByKapatmaTarihiBetween(Date startDate, Date endDate);
    
    List<HedeflerTalep> findByImhaTarihiBetween(Date startDate, Date endDate);
    

}
