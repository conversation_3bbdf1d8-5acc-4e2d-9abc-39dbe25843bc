package iym.makos.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * DTO for Iller entity
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Mahkeme Bilgi")
public class MahkemeBilgiDTO {

    private String mahkemeKodu;

    private String ilIlceKodu;

    private String mahkemeTuruKodu;

    private String mahkemeSayi;

    private String mahkemeAdi;

    private Date eklemeTarihi;

    private Long ekleyenKullaniciId;

    private String silindi;

}
