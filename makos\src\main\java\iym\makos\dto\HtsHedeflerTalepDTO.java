package iym.makos.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * DTO for HtsHedeflerTalep entity
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "HTS Hedefler Talep bilgilerini içerir")
public class HtsHedeflerTalepDTO {

    @Schema(description = "HTS Hedefler Talep ID")
    private Long id;

    @Schema(description = "Mahkeme karar ID", example = "1")
    @NotNull(message = "Mahkeme karar ID boş olamaz")
    private Long mahkemeKararId;

    @Schema(description = "Hedef numarası", example = "5551234567")
    @NotNull(message = "Hedef numarası boş olamaz")
    @Size(max = 100, message = "Hedef numarası 100 karakterden fazla olamaz")
    private String hedefNo;

    @Schema(description = "Karşı hedef numarası", example = "5559876543")
    @Size(max = 100, message = "Karşı hedef numarası 100 karakterden fazla olamaz")
    private String karsiHedefNo;

    @Schema(description = "Sorgu tipi", example = "ARAMA")
    @NotNull(message = "Sorgu tipi boş olamaz")
    @Size(max = 100, message = "Sorgu tipi 100 karakterden fazla olamaz")
    private String sorguTipi;

    @Schema(description = "Başlangıç tarihi", example = "2023-01-01T00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "Europe/Istanbul")
    private Date baslangicTarihi;

    @Schema(description = "Bitiş tarihi", example = "2023-01-31T23:59:59")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "Europe/Istanbul")
    private Date bitisTarihi;

    @Schema(description = "Tespit türü", example = "DETAYLI")
    @NotNull(message = "Tespit türü boş olamaz")
    @Size(max = 100, message = "Tespit türü 100 karakterden fazla olamaz")
    private String tespitTuru;

    @Schema(description = "Kullanıcı ID", example = "1")
    @NotNull(message = "Kullanıcı ID boş olamaz")
    @Size(max = 10, message = "Kullanıcı ID 10 karakterden fazla olamaz")
    private String kullaniciId;

    @Schema(description = "Durumu", example = "AKTIF")
    @Size(max = 100, message = "Durumu 100 karakterden fazla olamaz")
    private String durumu;
}
