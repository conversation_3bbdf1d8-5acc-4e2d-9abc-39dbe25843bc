-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;

-- Create sequence for HEDEFLER_TALEP if it doesn't exist
DECLARE
  seq_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO seq_exists FROM user_sequences WHERE sequence_name = 'HEDEFLER_TALEP_SEQ';
  IF seq_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE SEQUENCE iym.HEDEFLER_TALEP_SEQ
      START WITH 1
      INCREMENT BY 1
      NOCACHE
      NOCYCLE';
  END IF;
END;
/

-- Create HEDEFLER_TALEP table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'HEDEFLER_TALEP';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.HEDEFLER_TALEP (
      ID NUMBER NOT NULL,
      BIRIM_KOD NUMBER,
      KULLANICI_ID NUMBER,
      TEK_MASA_KUL_ID NUMBER,
      HEDEF_NO VARCHAR2(100 BYTE),
      HEDEF_ADI VARCHAR2(100 BYTE),
      HEDEF_SOYADI VARCHAR2(100 BYTE),
      BASLAMA_TARIHI DATE,
      SURESI NUMBER,
      SURE_TIPI NUMBER,
      UZATMA_SAYISI NUMBER,
      DURUMU VARCHAR2(100 BYTE),
      ACIKLAMA VARCHAR2(250 BYTE),
      MAHKEME_KARAR_ID NUMBER,
      HEDEF_AIDIYAT_ID NUMBER,
      GRUP_KOD NUMBER,
      AIDIYAT_KOD VARCHAR2(35 BYTE),
      UNIQ_KOD NUMBER,
      KAYIT_TARIHI DATE,
      TANIMLAMA_TARIHI DATE,
      KAPATMA_KARAR_ID NUMBER,
      KAPATMA_TARIHI DATE,
      IMHA VARCHAR2(20 BYTE),
      IMHA_TARIHI DATE,
      UZATMA_ID NUMBER,
      ACILMI CHAR(1 BYTE),
      HEDEF_118_ADI VARCHAR2(50 BYTE),
      HEDEF_118_SOYADI VARCHAR2(50 BYTE),
      HEDEF_118_ADRES VARCHAR2(250 BYTE),
      HEDEF_TIPI NUMBER,
      CANAK_NO VARCHAR2(100 CHAR),
      CONSTRAINT HED_TAL_PK PRIMARY KEY (ID) ENABLE
    )';

    -- Create indexes
    EXECUTE IMMEDIATE 'CREATE INDEX iym.HED_TAL_AID_IDX ON iym.HEDEFLER_TALEP (AIDIYAT_KOD ASC)';
    EXECUTE IMMEDIATE 'CREATE INDEX iym.HEDEFLER_TALEP_DURUM ON iym.HEDEFLER_TALEP (DURUMU ASC)';
    EXECUTE IMMEDIATE 'CREATE INDEX iym.HED_TAL_MAH_ID_IDX ON iym.HEDEFLER_TALEP (MAHKEME_KARAR_ID ASC)';
    EXECUTE IMMEDIATE 'CREATE INDEX iym.HEDEF_TALEP_GRUP_KOD ON iym.HEDEFLER_TALEP (GRUP_KOD ASC)';
    EXECUTE IMMEDIATE 'CREATE UNIQUE INDEX iym.HED_TAL_NO_MK_UK_IDX ON iym.HEDEFLER_TALEP (HEDEF_NO ASC, MAHKEME_KARAR_ID ASC, HEDEF_TIPI ASC, UNIQ_KOD ASC)';
  END IF;
END;
/

-- Insert sample data if table is empty
DECLARE
  row_count NUMBER;
BEGIN
  SELECT COUNT(*) INTO row_count FROM iym.HEDEFLER_TALEP;
  IF row_count = 0 THEN
    -- Make sure we have mahkeme_karar_talep and hedef_tipleri records
    DECLARE
      mahkeme_count NUMBER;
      hedef_tip_count NUMBER;
    BEGIN
      SELECT COUNT(*) INTO mahkeme_count FROM iym.MAHKEME_KARAR_TALEP;
      SELECT COUNT(*) INTO hedef_tip_count FROM iym.HEDEF_TIPLERI;

      IF mahkeme_count > 0 AND hedef_tip_count > 0 THEN
        -- Get the IDs of the mahkeme_karar_talep records
        FOR mahkeme_rec IN (
          SELECT m.ID as mahkeme_id, m.KULLANICI_ID
          FROM iym.MAHKEME_KARAR_TALEP m
        ) LOOP
          -- Sample data 1 - Telefon hedefi
          INSERT INTO iym.HEDEFLER_TALEP (
            ID, BIRIM_KOD, KULLANICI_ID, HEDEF_NO, HEDEF_ADI, HEDEF_SOYADI,
            BASLAMA_TARIHI, SURESI, SURE_TIPI, UZATMA_SAYISI, DURUMU,
            ACIKLAMA, MAHKEME_KARAR_ID, GRUP_KOD, AIDIYAT_KOD, UNIQ_KOD,
            KAYIT_TARIHI, TANIMLAMA_TARIHI, ACILMI, HEDEF_TIPI, CANAK_NO
          ) VALUES (
            iym.HEDEFLER_TALEP_SEQ.NEXTVAL, 1, mahkeme_rec.KULLANICI_ID, '5551234567', 'Ahmet', 'Yılmaz',
            SYSDATE, 30, 1, 0, 'AKTIF',
            'Telefon hedefi', mahkeme_rec.mahkeme_id, 1, 'AIDIYAT1', iym.HEDEFLER_TALEP_SEQ.CURRVAL,
            SYSDATE, SYSDATE, 'H', 1, 'CANAK001'
          );

          -- Sample data 2 - E-posta hedefi
          INSERT INTO iym.HEDEFLER_TALEP (
            ID, BIRIM_KOD, KULLANICI_ID, HEDEF_NO, HEDEF_ADI, HEDEF_SOYADI,
            BASLAMA_TARIHI, SURESI, SURE_TIPI, UZATMA_SAYISI, DURUMU,
            ACIKLAMA, MAHKEME_KARAR_ID, GRUP_KOD, AIDIYAT_KOD, UNIQ_KOD,
            KAYIT_TARIHI, TANIMLAMA_TARIHI, ACILMI, HEDEF_TIPI, CANAK_NO
          ) VALUES (
            iym.HEDEFLER_TALEP_SEQ.NEXTVAL, 1, mahkeme_rec.KULLANICI_ID, '<EMAIL>', 'Ayşe', 'Kaya',
            SYSDATE, 30, 1, 0, 'AKTIF',
            'E-posta hedefi', mahkeme_rec.mahkeme_id, 1, 'AIDIYAT1', iym.HEDEFLER_TALEP_SEQ.CURRVAL,
            SYSDATE, SYSDATE, 'H', 2, 'CANAK002'
          );

          -- Sample data 3 - IP hedefi
          INSERT INTO iym.HEDEFLER_TALEP (
            ID, BIRIM_KOD, KULLANICI_ID, HEDEF_NO, HEDEF_ADI, HEDEF_SOYADI,
            BASLAMA_TARIHI, SURESI, SURE_TIPI, UZATMA_SAYISI, DURUMU,
            ACIKLAMA, MAHKEME_KARAR_ID, GRUP_KOD, AIDIYAT_KOD, UNIQ_KOD,
            KAYIT_TARIHI, TANIMLAMA_TARIHI, ACILMI, HEDEF_TIPI, CANAK_NO
          ) VALUES (
            iym.HEDEFLER_TALEP_SEQ.NEXTVAL, 1, mahkeme_rec.KULLANICI_ID, '***********', 'Mehmet', 'Demir',
            SYSDATE, 30, 1, 0, 'AKTIF',
            'IP hedefi', mahkeme_rec.mahkeme_id, 1, 'AIDIYAT1', iym.HEDEFLER_TALEP_SEQ.CURRVAL,
            SYSDATE, SYSDATE, 'H', 3, 'CANAK003'
          );
        END LOOP;
      END IF;
    END;
  END IF;
END;
/

-- Add foreign key constraints if they don't exist
DECLARE
  constraint_count NUMBER;
BEGIN
  -- Check if HEDEF_TALEP_HEDEF_TIPI_FRG constraint exists
  SELECT COUNT(*) INTO constraint_count FROM user_constraints
  WHERE constraint_name = 'HED_TAL_HED_TIP_FK' AND table_name = 'HEDEFLER_TALEP';

  IF constraint_count = 0 THEN
    EXECUTE IMMEDIATE 'ALTER TABLE iym.HEDEFLER_TALEP
    ADD CONSTRAINT HED_TAL_HED_TIP_FK FOREIGN KEY (HEDEF_TIPI)
    REFERENCES iym.HEDEF_TIPLERI (HEDEF_KODU) ENABLE';
  END IF;

  -- Check if HEDEF_TALEP_MAHKEME_KARAR_FRG constraint exists
  SELECT COUNT(*) INTO constraint_count FROM user_constraints
  WHERE constraint_name = 'HED_TAL_MAH_KAR_FK' AND table_name = 'HEDEFLER_TALEP';

  IF constraint_count = 0 THEN
    EXECUTE IMMEDIATE 'ALTER TABLE iym.HEDEFLER_TALEP
    ADD CONSTRAINT HED_TAL_MAH_KAR_FK FOREIGN KEY (MAHKEME_KARAR_ID)
    REFERENCES iym.MAHKEME_KARAR_TALEP (ID) ENABLE';
  END IF;
END;
/

COMMIT;
