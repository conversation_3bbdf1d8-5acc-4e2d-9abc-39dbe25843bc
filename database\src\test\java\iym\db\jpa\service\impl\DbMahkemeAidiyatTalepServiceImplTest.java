package iym.db.jpa.service.impl;

import iym.common.model.entity.iym.MahkemeAidiyatTalep;
import iym.db.jpa.dao.MahkemeAidiyatTalepRepo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DbMahkemeAidiyatTalepServiceImplTest {

    @Mock
    private MahkemeAidiyatTalepRepo mahkemeAidiyatTalepRepo;

    @InjectMocks
    private DbMahkemeAidiyatTalepServiceImpl dbMahkemeAidiyatTalepService;

    private MahkemeAidiyatTalep mahkemeAidiyatTalep;

    @BeforeEach
    void setUp() {
        mahkemeAidiyatTalep = MahkemeAidiyatTalep.builder()
                .id(1L)
                .mahkemeKararTalepId(100L)
                .aidiyatKod("02")
                .durumu("AKTIF")
                .build();
    }

    @Test
    void findByMahkemeId_shouldReturnListOfMahkemeAidiyatTalep() {
        // Given
        Long mahkemeId = 100L;
        List<MahkemeAidiyatTalep> expectedList = Arrays.asList(mahkemeAidiyatTalep);
        when(mahkemeAidiyatTalepRepo.findByMahkemeKararTalepId(mahkemeId)).thenReturn(expectedList);

        // When
        List<MahkemeAidiyatTalep> result = dbMahkemeAidiyatTalepService.findByMahkemeKararTalepId(mahkemeId);

        // Then
        assertThat(result).isEqualTo(expectedList);
        verify(mahkemeAidiyatTalepRepo).findByMahkemeKararTalepId(mahkemeId);
    }



}
