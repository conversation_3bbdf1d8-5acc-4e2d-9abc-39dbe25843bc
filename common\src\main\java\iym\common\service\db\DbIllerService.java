package iym.common.service.db;

import iym.common.model.entity.iym.Iller;

import java.util.List;
import java.util.Optional;

/**
 * Service interface for Iller entity
 */
public interface DbIllerService extends GenericDbService<Iller, String> {

    //todo: mkale
    Optional<Iller> getByIlIlceKodu(String kodu);

    List<Iller> findByIlAdi(String ilAdi);

    List<Iller> findByIlceAdi(String ilceAdi);

    List<Iller> findByIlAdiAndIlceAdi(String ilAdi, String ilceAdi);

    List<Iller> findByIlKodStartingWith(String ilKodPrefix);

    List<Iller> findByIlAdiContainingIgnoreCase(String ilAdi);

    List<Iller> findByIlceAdiContainingIgnoreCase(String ilceAdi);

    List<Iller> findAllByOrderByIlAdiAsc();

    List<Iller> findAllByOrderByIlKodAsc();

    List<Iller> findByIlAdiOrderByIlceAdiAsc(String ilAdi);

    boolean existsByIlAdiAndIlceAdi(String ilAdi, String ilceAdi);

    boolean existsById(String id);
}
