package iym.makos.service.makos;

import iym.common.service.db.DbSucTipiService;
import iym.makos.dto.SucTipiDTO;
import iym.makos.mapper.SucTipiMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

/**
 * Service for Iller operations
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SucTipiService {

    private final DbSucTipiService dbSucTipiService;
    private final SucTipiMapper sucTipiMapper;


    public SucTipiDTO getSucTipiByKodu(String sucTipiKodu){

        return dbSucTipiService.findBySucTipiKodu(sucTipiKodu)
                .map(sucTipiMapper::toDto)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Suc tipi bilgisi bulunamadı"));

    }

}
