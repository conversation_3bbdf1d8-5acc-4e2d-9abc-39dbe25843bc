package iym.makos.dto.id;

import io.swagger.v3.oas.annotations.media.Schema;
import iym.common.model.api.KararTuru;
import iym.common.model.api.MahkemeKararTip;
import iym.common.validation.ValidationResult;
import iym.makos.model.api.MahkemeKararDetay;
import iym.makos.model.api.MahkemeKoduGuncellemeDetay;
import iym.makos.model.reqrep.MahkemeKararRequest;
import iym.makos.validator.MakosRequestValid;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Jacksonized
@Data
@NoArgsConstructor
@SuperBuilder
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@MakosRequestValid
@Slf4j
public class IDMahkemeKoduGuncellemeRequest extends MahkemeKararRequest {

    @NotNull
    @Valid
    @Size(min = 1)
    @Schema(description = "Güncelleme yapılacak mahkeme karar bilgisi ve karara ait güncellenecek yeni kod/il bilgileri")
    private List<MahkemeKoduGuncellemeDetay> mahkemeKoduGuncellemeDetayListesi;

    @Override
    public ValidationResult isValid() {
        log.trace("Checking if MahkemeBilgisiGuncellemeRequest is valid");

        try {
            ValidationResult validationResult = new ValidationResult(true);

            if (kararTuru != KararTuru.ILETISIMIN_DENETLENMESI_MAHKEMEKODU_GUNCELLEME) {
                validationResult.addFailedReason("Karar türü: " + KararTuru.ILETISIMIN_DENETLENMESI_MAHKEMEKODU_GUNCELLEME.name() + " olmalıdır");
                return validationResult;
            }

            MahkemeKararTip kararTip = mahkemeKararBilgisi.getMahkemeKararTipi();
            if (kararTip != MahkemeKararTip.MAHKEME_KODU_DEGISTIRME) {
                validationResult.addFailedReason("Mahkeme karar Tipi " + MahkemeKararTip.MAHKEME_KODU_DEGISTIRME.name() + " olmalıdır");
            }

            if (mahkemeKoduGuncellemeDetayListesi == null || mahkemeKoduGuncellemeDetayListesi.isEmpty()) {
                validationResult.addFailedReason("Güncellemeye konu olan en az bir detay girilmelidir!");
            } else {
                for (MahkemeKoduGuncellemeDetay mahkemeKoduGuncellemeDetay : mahkemeKoduGuncellemeDetayListesi) {

                    MahkemeKararDetay iliskiliMahkemeKararDetay = mahkemeKoduGuncellemeDetay.getMahkemeKararDetay();
                    if (iliskiliMahkemeKararDetay == null) {
                        validationResult.addFailedReason("Güncellemeye konu mahkeme karar bilgileri boş olamaz.!");
                    }
                }
            }

            return validationResult;
        } catch (Exception e) {
            log.error("Validation failed", e);
            return new ValidationResult("Validation failed. Internal error");
        }

    }

    @Override
    protected void assignKararTuru() {
        this.kararTuru = KararTuru.ILETISIMIN_DENETLENMESI_MAHKEMEKODU_GUNCELLEME;
    }
}

