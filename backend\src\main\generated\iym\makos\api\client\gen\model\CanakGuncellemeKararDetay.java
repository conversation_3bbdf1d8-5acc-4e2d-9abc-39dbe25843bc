/*
 * MAKOS OpenAPI definition
 * MAKOS Application
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package iym.makos.api.client.gen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import iym.makos.api.client.gen.model.CanakGuncellemeDetay;
import iym.makos.api.client.gen.model.MahkemeKararDetay;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * <PERSON><PERSON>ncelleme yapılacak mahkeme karar bilgisi ve karara ait güncellenecek aidiyat bilgileri
 */
@JsonPropertyOrder({
  CanakGuncellemeKararDetay.JSON_PROPERTY_MAHKEME_KARAR_DETAY,
  CanakGuncellemeKararDetay.JSON_PROPERTY_CANAK_GUNCELLEME_DETAY_LIST
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class CanakGuncellemeKararDetay {
  public static final String JSON_PROPERTY_MAHKEME_KARAR_DETAY = "mahkemeKararDetay";
  private MahkemeKararDetay mahkemeKararDetay;

  public static final String JSON_PROPERTY_CANAK_GUNCELLEME_DETAY_LIST = "canakGuncellemeDetayList";
  private List<CanakGuncellemeDetay> canakGuncellemeDetayList = new ArrayList<>();

  public CanakGuncellemeKararDetay() {
  }

  public CanakGuncellemeKararDetay mahkemeKararDetay(MahkemeKararDetay mahkemeKararDetay) {
    
    this.mahkemeKararDetay = mahkemeKararDetay;
    return this;
  }

   /**
   * Get mahkemeKararDetay
   * @return mahkemeKararDetay
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_DETAY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public MahkemeKararDetay getMahkemeKararDetay() {
    return mahkemeKararDetay;
  }


  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_DETAY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMahkemeKararDetay(MahkemeKararDetay mahkemeKararDetay) {
    this.mahkemeKararDetay = mahkemeKararDetay;
  }


  public CanakGuncellemeKararDetay canakGuncellemeDetayList(List<CanakGuncellemeDetay> canakGuncellemeDetayList) {
    
    this.canakGuncellemeDetayList = canakGuncellemeDetayList;
    return this;
  }

  public CanakGuncellemeKararDetay addCanakGuncellemeDetayListItem(CanakGuncellemeDetay canakGuncellemeDetayListItem) {
    if (this.canakGuncellemeDetayList == null) {
      this.canakGuncellemeDetayList = new ArrayList<>();
    }
    this.canakGuncellemeDetayList.add(canakGuncellemeDetayListItem);
    return this;
  }

   /**
   * Get canakGuncellemeDetayList
   * @return canakGuncellemeDetayList
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_CANAK_GUNCELLEME_DETAY_LIST)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public List<CanakGuncellemeDetay> getCanakGuncellemeDetayList() {
    return canakGuncellemeDetayList;
  }


  @JsonProperty(JSON_PROPERTY_CANAK_GUNCELLEME_DETAY_LIST)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setCanakGuncellemeDetayList(List<CanakGuncellemeDetay> canakGuncellemeDetayList) {
    this.canakGuncellemeDetayList = canakGuncellemeDetayList;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CanakGuncellemeKararDetay canakGuncellemeKararDetay = (CanakGuncellemeKararDetay) o;
    return Objects.equals(this.mahkemeKararDetay, canakGuncellemeKararDetay.mahkemeKararDetay) &&
        Objects.equals(this.canakGuncellemeDetayList, canakGuncellemeKararDetay.canakGuncellemeDetayList);
  }

  @Override
  public int hashCode() {
    return Objects.hash(mahkemeKararDetay, canakGuncellemeDetayList);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CanakGuncellemeKararDetay {\n");
    sb.append("    mahkemeKararDetay: ").append(toIndentedString(mahkemeKararDetay)).append("\n");
    sb.append("    canakGuncellemeDetayList: ").append(toIndentedString(canakGuncellemeDetayList)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

