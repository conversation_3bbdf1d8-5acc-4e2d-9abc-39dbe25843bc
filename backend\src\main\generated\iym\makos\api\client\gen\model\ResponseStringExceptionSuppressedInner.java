/*
 * MAKOS OpenAPI definition
 * MAKOS Application
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package iym.makos.api.client.gen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import iym.makos.api.client.gen.model.ResponseStringExceptionStackTraceInner;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * ResponseStringExceptionSuppressedInner
 */
@JsonPropertyOrder({
  ResponseStringExceptionSuppressedInner.JSON_PROPERTY_STACK_TRACE,
  ResponseStringExceptionSuppressedInner.JSON_PROPERTY_MESSAGE,
  ResponseStringExceptionSuppressedInner.JSON_PROPERTY_LOCALIZED_MESSAGE
})
@JsonTypeName("ResponseString_exception_suppressed_inner")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class ResponseStringExceptionSuppressedInner {
  public static final String JSON_PROPERTY_STACK_TRACE = "stackTrace";
  private List<ResponseStringExceptionStackTraceInner> stackTrace;

  public static final String JSON_PROPERTY_MESSAGE = "message";
  private String message;

  public static final String JSON_PROPERTY_LOCALIZED_MESSAGE = "localizedMessage";
  private String localizedMessage;

  public ResponseStringExceptionSuppressedInner() {
  }

  public ResponseStringExceptionSuppressedInner stackTrace(List<ResponseStringExceptionStackTraceInner> stackTrace) {
    
    this.stackTrace = stackTrace;
    return this;
  }

  public ResponseStringExceptionSuppressedInner addStackTraceItem(ResponseStringExceptionStackTraceInner stackTraceItem) {
    if (this.stackTrace == null) {
      this.stackTrace = new ArrayList<>();
    }
    this.stackTrace.add(stackTraceItem);
    return this;
  }

   /**
   * Get stackTrace
   * @return stackTrace
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_STACK_TRACE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<ResponseStringExceptionStackTraceInner> getStackTrace() {
    return stackTrace;
  }


  @JsonProperty(JSON_PROPERTY_STACK_TRACE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setStackTrace(List<ResponseStringExceptionStackTraceInner> stackTrace) {
    this.stackTrace = stackTrace;
  }


  public ResponseStringExceptionSuppressedInner message(String message) {
    
    this.message = message;
    return this;
  }

   /**
   * Get message
   * @return message
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MESSAGE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getMessage() {
    return message;
  }


  @JsonProperty(JSON_PROPERTY_MESSAGE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMessage(String message) {
    this.message = message;
  }


  public ResponseStringExceptionSuppressedInner localizedMessage(String localizedMessage) {
    
    this.localizedMessage = localizedMessage;
    return this;
  }

   /**
   * Get localizedMessage
   * @return localizedMessage
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_LOCALIZED_MESSAGE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getLocalizedMessage() {
    return localizedMessage;
  }


  @JsonProperty(JSON_PROPERTY_LOCALIZED_MESSAGE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setLocalizedMessage(String localizedMessage) {
    this.localizedMessage = localizedMessage;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ResponseStringExceptionSuppressedInner responseStringExceptionSuppressedInner = (ResponseStringExceptionSuppressedInner) o;
    return Objects.equals(this.stackTrace, responseStringExceptionSuppressedInner.stackTrace) &&
        Objects.equals(this.message, responseStringExceptionSuppressedInner.message) &&
        Objects.equals(this.localizedMessage, responseStringExceptionSuppressedInner.localizedMessage);
  }

  @Override
  public int hashCode() {
    return Objects.hash(stackTrace, message, localizedMessage);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ResponseStringExceptionSuppressedInner {\n");
    sb.append("    stackTrace: ").append(toIndentedString(stackTrace)).append("\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("    localizedMessage: ").append(toIndentedString(localizedMessage)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

