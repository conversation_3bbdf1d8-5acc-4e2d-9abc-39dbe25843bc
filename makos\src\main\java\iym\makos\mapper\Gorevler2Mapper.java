package iym.makos.mapper;

import iym.common.model.entity.iym.Gorevler2;
import org.springframework.stereotype.Component;
import iym.makos.dto.Gorevler2DTO;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper for Gorevler2 entity and DTO
 */
@Component
public class Gorevler2Mapper {

    /**
     * Convert entity to DTO
     * @param entity Gorevler2 entity
     * @return Gorevler2DTO
     */
    public Gorevler2DTO toDto(Gorevler2 entity) {
        if (entity == null) {
            return null;
        }

        return Gorevler2DTO.builder()
                .gorev(entity.getGorev())
                .gorevKodu(entity.getGorevKodu())
                .gorevImzaAdi(entity.getGorevImzaAdi())
                .imzaYetki(entity.getImzaYetki())
                .silindi(entity.getSilindi())
                .gorevKodu2(entity.getGorevKodu2())
                .gorevTipi(entity.getGorevTipi())
                .oncelik(entity.getOncelik())
                .baslama<PERSON><PERSON>hi(entity.getBaslamaTarihi())
                .bitisT<PERSON>hi(entity.getBitisTarihi())
                .build();
    }

    /**
     * Convert DTO to entity
     * @param dto Gorevler2DTO
     * @return Gorevler2 entity
     */
    public Gorevler2 toEntity(Gorevler2DTO dto) {
        if (dto == null) {
            return null;
        }

        return Gorevler2.builder()
                .gorev(dto.getGorev())
                .gorevKodu(dto.getGorevKodu())
                .gorevImzaAdi(dto.getGorevImzaAdi())
                .imzaYetki(dto.getImzaYetki())
                .silindi(dto.getSilindi())
                .gorevKodu2(dto.getGorevKodu2())
                .gorevTipi(dto.getGorevTipi())
                .oncelik(dto.getOncelik())
                .baslamaTarihi(dto.getBaslamaTarihi())
                .bitisTarihi(dto.getBitisTarihi())
                .build();
    }

    /**
     * Update entity from DTO
     * @param entity Gorevler2 entity to update
     * @param dto Gorevler2DTO with new values
     * @return Updated Gorevler2 entity
     */
    public Gorevler2 updateEntityFromDto(Gorevler2 entity, Gorevler2DTO dto) {
        if (entity == null || dto == null) {
            return entity;
        }

        // Primary key fields should not be updated
        // entity.setGorev(dto.getGorev());
        // entity.setGorevKodu(dto.getGorevKodu());
        
        entity.setGorevImzaAdi(dto.getGorevImzaAdi());
        entity.setImzaYetki(dto.getImzaYetki());
        entity.setSilindi(dto.getSilindi());
        entity.setGorevKodu2(dto.getGorevKodu2());
        entity.setGorevTipi(dto.getGorevTipi());
        entity.setOncelik(dto.getOncelik());
        entity.setBaslamaTarihi(dto.getBaslamaTarihi());
        entity.setBitisTarihi(dto.getBitisTarihi());

        return entity;
    }

    /**
     * Convert list of entities to list of DTOs
     * @param entityList List of Gorevler2 entities
     * @return List of Gorevler2DTO
     */
    public List<Gorevler2DTO> toDtoList(List<Gorevler2> entityList) {
        if (entityList == null) {
            return null;
        }

        return entityList.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }

    /**
     * Convert list of DTOs to list of entities
     * @param dtoList List of Gorevler2DTO
     * @return List of Gorevler2 entities
     */
    public List<Gorevler2> toEntityList(List<Gorevler2DTO> dtoList) {
        if (dtoList == null) {
            return null;
        }

        return dtoList.stream()
                .map(this::toEntity)
                .collect(Collectors.toList());
    }
}
