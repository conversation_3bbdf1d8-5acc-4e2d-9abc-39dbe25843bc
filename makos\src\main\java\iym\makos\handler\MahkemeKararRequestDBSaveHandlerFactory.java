package iym.makos.handler;


import iym.makos.model.reqrep.MahkemeKararRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class MahkemeKararRequestDBSaveHandlerFactory {

    private final Map<Class<? extends MahkemeKararRequest>, MahkemeKararDBSaveHandler<?>> handlerMap = new HashMap<>();

    @Autowired
    public MahkemeKararRequestDBSaveHandlerFactory(List<MahkemeKararDBSaveHandler<?>> handlers) {
        for (MahkemeKararDBSaveHandler<?> handler : handlers) {
            handlerMap.put(handler.getRelatedRequestType(), handler);
        }
    }

    @SuppressWarnings("unchecked")
    public <T extends MahkemeKararRequest> MahkemeKararDBSaveHandler<T> getHandler(Class<T> requestType) {
        return (MahkemeKararDBSaveHandler<T>) handlerMap.get(requestType);
    }
}