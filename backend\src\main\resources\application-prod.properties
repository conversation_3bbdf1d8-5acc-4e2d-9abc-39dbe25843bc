# Production Environment Configuration for Backend
# Multi-DataSource Configuration for Production

# Primary DataSource - Oracle (existing functionality)
spring.datasource.oracle.jdbcUrl=jdbc:oracle:thin:@${ORACLE_DB_HOST}:${ORACLE_DB_PORT:1521}/${ORACLE_DB_SERVICE}
spring.datasource.oracle.driverClassName=oracle.jdbc.OracleDriver
spring.datasource.oracle.username=${ORACLE_DB_USERNAME}
spring.datasource.oracle.password=${ORACLE_DB_PASSWORD}

# Secondary DataSource - PostgreSQL (new functionality)
spring.datasource.postgresql.jdbcUrl=jdbc:postgresql://${POSTGRESQL_DB_HOST}:${POSTGRESQL_DB_PORT:5432}/${POSTGRESQL_DB_NAME:demo_db}
spring.datasource.postgresql.driverClassName=org.postgresql.Driver
spring.datasource.postgresql.username=${POSTGRESQL_DB_USERNAME:demo_user}
spring.datasource.postgresql.password=${POSTGRESQL_DB_PASSWORD:demo_password}

# JPA/Hibernate Configuration for Production
# Oracle dialect and schema
spring.jpa.properties.hibernate.dialect.oracle=org.hibernate.dialect.Oracle12cDialect
spring.jpa.properties.hibernate.default_schema.oracle=${ORACLE_DB_SCHEMA:iym}

# PostgreSQL dialect and schema
spring.jpa.properties.hibernate.dialect.postgresql=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.default_schema.postgresql=${POSTGRESQL_DB_SCHEMA:public}

# JPA configuration for production
spring.jpa.hibernate.ddl-auto=none
spring.jpa.properties.hibernate.hbm2ddl.auto=none
spring.jpa.properties.hibernate.validator.apply_to_ddl=false
spring.jpa.properties.hibernate.temp.use_jdbc_metadata_defaults=false
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false

# Connection pool configuration for production
spring.datasource.hikari.connectionTimeout=20000
spring.datasource.hikari.maximumPoolSize=10

# Production logging configuration
logging.level.root=WARN
logging.level.org.springframework.web=WARN
logging.level.org.hibernate=ERROR
logging.level.iym=INFO
logging.level.com.zaxxer.hikari.HikariConfig=ERROR
logging.level.com.zaxxer.hikari=ERROR

# Swagger configuration for production (disabled for security)
springdoc.swagger-ui.enabled=false
springdoc.api-docs.enabled=false

# Application specific properties for production
app.init-db=false

# JWT Configuration for production
app.jwtSecret=${JWT_SECRET}
# 8 hours for production (more secure)
app.jwtExpirationInSec=${JWT_EXPIRATION_SEC:28800}

# CORS Configuration for production
cors.allowed.origins=${CORS_ALLOWED_ORIGINS}

makos.api.base-url=${MAKOS_API_BASE_URL}
makos.api.username=${MAKOS_API_USERNAME:}
makos.api.password=${MAKOS_API_PASSWORD:}
makos.api.connect-timeout=${MAKOS_API_CONNECT_TIMEOUT:5000}
makos.api.read-timeout=${MAKOS_API_READ_TIMEOUT:30000}
