# Production Environment Configuration for Backend
# Oracle Database configuration for production
spring.datasource.url=jdbc:oracle:thin:@${DB_HOST}:${DB_PORT:1521}/${DB_SERVICE}
spring.datasource.driverClassName=oracle.jdbc.OracleDriver
spring.datasource.username=${DB_USERNAME}
spring.datasource.password=${DB_PASSWORD}
spring.jpa.database-platform=org.hibernate.dialect.Oracle12cDialect
spring.jpa.properties.hibernate.default_schema=${DB_SCHEMA}

# JPA configuration for production
spring.jpa.hibernate.ddl-auto=none
spring.jpa.properties.hibernate.hbm2ddl.auto=none
spring.jpa.properties.hibernate.validator.apply_to_ddl=false
spring.jpa.properties.hibernate.temp.use_jdbc_metadata_defaults=false
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false

# Connection pool configuration for production
spring.datasource.hikari.connectionTimeout=20000
spring.datasource.hikari.maximumPoolSize=10

# Production logging configuration
logging.level.root=WARN
logging.level.org.springframework.web=WARN
logging.level.org.hibernate=ERROR
logging.level.iym=INFO
logging.level.com.zaxxer.hikari.HikariConfig=ERROR
logging.level.com.zaxxer.hikari=ERROR

# Swagger configuration for production (disabled for security)
springdoc.swagger-ui.enabled=false
springdoc.api-docs.enabled=false

# Application specific properties for production
app.init-db=false

# JWT Configuration for production
app.jwtSecret=${JWT_SECRET}
# 8 hours for production (more secure)
app.jwtExpirationInSec=${JWT_EXPIRATION_SEC:28800}

# CORS Configuration for production
cors.allowed.origins=${CORS_ALLOWED_ORIGINS}

makos.api.base-url=${MAKOS_API_BASE_URL}
makos.api.username=${MAKOS_API_USERNAME:}
makos.api.password=${MAKOS_API_PASSWORD:}
makos.api.connect-timeout=${MAKOS_API_CONNECT_TIMEOUT:5000}
makos.api.read-timeout=${MAKOS_API_READ_TIMEOUT:30000}
