package iym.makos.mapper;

import iym.common.model.entity.iym.MahkemeBilgi;
import iym.common.model.entity.iym.SucTipi;
import iym.makos.dto.MahkemeBilgiDTO;
import iym.makos.dto.SucTipiDTO;
import org.springframework.stereotype.Component;

/**
 * Mapper for MahkemeBilgiMapper entity and DTO
 */
@Component
public class SucTipiMapper {


    public SucTipiDTO toDto(SucTipi entity) {
        if (entity == null) {
            return null;
        }

        return SucTipiDTO.builder()
                .sucTipiKodu(entity.getSucTipiKodu())
                .aciklama(entity.getAciklama())
                .mahkemeKaraTipiKodu(entity.getMahkemeKaraTipiKodu())
                .durum(entity.getDurum())
                .build();
    }


    public SucTipi toEntity(SucTipiDTO dto) {
        if (dto == null) {
            return null;
        }

        return SucTipi.builder()
                .sucTipiKodu(dto.getSucTipiKodu())
                .aciklama(dto.getAciklama())
                .mahkemeKaraTipiKodu(dto.getMahkemeKaraTipiKodu())
                .durum(dto.getDurum())
                .build();
    }


}
