package iym.common.model.entity.iym;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;

/**
 * Entity class for MAHKEME_AIDIYAT_TALEP table
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode

@Entity(name = "MahkemeAidiyatTalep")
@Table(name = "MAHKEME_AIDIYAT_TALEP")
public class MahkemeAidiyatTalep implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "MAHKEME_AIDIYAT_TALEP_SEQ")
    @SequenceGenerator(name = "MAHKEME_AIDIYAT_TALEP_SEQ", sequenceName = "MAHKEME_AIDIYAT_TALEP_SEQ", allocationSize = 1)
    private Long id;

    @Column(name = "MAHKEME_ID", nullable = false)
    @NotNull
    private Long mahkemeKararTalepId;

    @Column(name = "AIDIYAT_KOD", nullable = false, length = 25)
    @NotNull
    @Size(max = 25)
    private String aidiyatKod;

    @Column(name = "DURUMU", length = 10)
    @Size(max = 10)
    private String durumu;
}
