package iym.common.service.db;

import iym.common.model.entity.iym.CanakNumaralar;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Service interface for CanakNumaralar entity
 */
public interface DbCanakNumaralarService extends GenericDbService<CanakNumaralar, Long> {

    Optional<CanakNumaralar> findByCanakNo(String canakNo);
    
    List<CanakNumaralar> findByCanakNoContainingIgnoreCase(String canakNo);
    
    List<CanakNumaralar> findByKurumKod(String kurumKod);
    
    List<CanakNumaralar> findByEkleyenId(Long ekleyenId);
    
    List<CanakNumaralar> findByKutu(Long kutu);
    
    List<CanakNumaralar> findByEklemeTarihBetween(Date startDate, Date endDate);
    
    List<CanakNumaralar> findByAciklamaContainingIgnoreCase(String aciklama);
    
    List<CanakNumaralar> findByKurumKodAndKutu(String kurumKod, Long kutu);
    
    List<CanakNumaralar> findByCanakNoStartingWith(String prefix);
    
    boolean existsByCanakNo(String canakNo);
}
