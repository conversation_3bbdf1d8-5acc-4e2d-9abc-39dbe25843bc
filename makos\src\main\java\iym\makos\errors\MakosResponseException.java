package iym.makos.errors;

public class MakosResponseException extends RuntimeException {
    private final MakosResponseErrorCodes errorCode;
    private final String formattedMessage;

    public MakosResponseException(MakosResponseErrorCodes errorCode, Object... args) {
        super(errorCode.format(args)); // Exception mesajı loglarda net görünür
        this.errorCode = errorCode;
        this.formattedMessage = errorCode.format(args);
    }

    public MakosResponseErrorCodes getErrorCode() {
        return errorCode;
    }

    public String getFormattedMessage() {
        return formattedMessage;
    }
}