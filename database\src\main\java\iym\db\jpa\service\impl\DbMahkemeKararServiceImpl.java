package iym.db.jpa.service.impl;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.MahkemeKarar;
import iym.common.model.entity.iym.MahkemeKararTalep;
import iym.common.service.db.DbMahkemeKararService;
import iym.common.service.db.DbMahkemeKararTalepService;
import iym.db.jpa.dao.MahkemeKararRepo;
import iym.db.jpa.dao.MahkemeKararTalepRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Service implementation for MahkemeKararTalep entity
 */
@Service
public class DbMahkemeKararServiceImpl extends GenericDbServiceImpl<MahkemeKarar, Long> implements DbMahkemeKararService {

    private final MahkemeKararRepo mahkemeKararRepo;

    @Autowired
    public DbMahkemeKararServiceImpl(MahkemeKararRepo repository) {
        super(repository);
        this.mahkemeKararRepo = repository;
    }

    /*
    @Override
    @Transactional(readOnly = true)
    public Optional<MahkemeKarar> findById(Long evrakId) {
        return mahkemeKararRepo.findById(evrakId);
    }

     */

    @Override
    @Transactional(readOnly = true)
    public List<MahkemeKarar> findByEvrakId(Long evrakId){
        return mahkemeKararRepo.findByEvrakId(evrakId);
    }

    public Optional<MahkemeKarar> findBy(
            String mahkemeIlIlceKodu,
            String mahkemeKodu,
            String mahkemeKararNo,
            String sorusturmaNo
    ){
        return mahkemeKararRepo.findBy(mahkemeIlIlceKodu, mahkemeKodu, mahkemeKararNo, sorusturmaNo);
    }

}
