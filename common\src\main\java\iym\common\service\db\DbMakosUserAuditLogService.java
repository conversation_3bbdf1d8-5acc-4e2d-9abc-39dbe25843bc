package iym.common.service.db;

import iym.common.model.entity.makos.MakosUserAuditLog;
import iym.common.model.enums.MakosUserAuditType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Service interface for MakosUserAuditLog entity
 * Provides business logic methods for MAKOS user audit logging
 */
public interface DbMakosUserAuditLogService extends GenericDbService<MakosUserAuditLog, Long> {

    /**
     * Find audit logs by user audit type
     * @param userAuditType the type of audit operation
     * @return list of audit logs
     */
    List<MakosUserAuditLog> findByUserAuditType(MakosUserAuditType userAuditType);

    /**
     * Find audit logs by username
     * @param username the username to search for
     * @return list of audit logs
     */
    List<MakosUserAuditLog> findByUsername(String username);

    /**
     * Find audit logs by acting username
     * @param actingUsername the acting username to search for
     * @return list of audit logs
     */
    List<MakosUserAuditLog> findByActingUsername(String actingUsername);

    /**
     * Find audit logs by admin operated username
     * @param adminOperatedUsername the username that was operated on by admin
     * @return list of audit logs
     */
    List<MakosUserAuditLog> findByAdminOperatedUsername(String adminOperatedUsername);

    /**
     * Find audit logs by user IP
     * @param userIp the IP address to search for
     * @return list of audit logs
     */
    List<MakosUserAuditLog> findByUserIp(String userIp);

    /**
     * Find audit logs within a time range
     * @param startTime start of the time range
     * @param endTime end of the time range
     * @return list of audit logs
     */
    List<MakosUserAuditLog> findByRequestTimeBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * Find audit logs by username and audit type
     * @param username the username to search for
     * @param userAuditType the type of audit operation
     * @return list of audit logs
     */
    List<MakosUserAuditLog> findByUsernameAndUserAuditType(String username, MakosUserAuditType userAuditType);

    /**
     * Find audit logs by username within a time range
     * @param username the username to search for
     * @param startTime start of the time range
     * @param endTime end of the time range
     * @return list of audit logs
     */
    List<MakosUserAuditLog> findByUsernameAndRequestTimeBetween(String username, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * Find audit logs ordered by request time descending
     * @return list of audit logs ordered by most recent first
     */
    List<MakosUserAuditLog> findAllByOrderByRequestTimeDesc();

    /**
     * Find audit logs by username ordered by request time descending
     * @param username the username to search for
     * @return list of audit logs ordered by most recent first
     */
    List<MakosUserAuditLog> findByUsernameOrderByRequestTimeDesc(String username);

    /**
     * Find audit logs with pagination and filtering using specifications
     * @param spec the specification for filtering
     * @param pageable pagination information
     * @return page of audit logs
     */
    Page<MakosUserAuditLog> findAll(Specification<MakosUserAuditLog> spec, Pageable pageable);
}
