package iym.db.jpa.service.impl;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.EvrakKayit;
import iym.common.service.db.DbEvrakKayitService;
import iym.db.jpa.dao.EvrakKayitRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Service implementation for EvrakKayit entity
 */
@Service
public class DbEvrakKayitServiceImpl extends GenericDbServiceImpl<EvrakKayit, Long> implements DbEvrakKayitService {

    private final EvrakKayitRepo evrakKayitRepo;

    @Autowired
    public DbEvrakKayitServiceImpl(EvrakKayitRepo repository) {
        super(repository);
        this.evrakKayitRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByEvrakSiraNo(String evrakSiraNo) {
        return evrakKayitRepo.existsByEvrakSiraNo(evrakSiraNo);
    }



    @Override
    @Transactional(readOnly = true)
    public List<EvrakKayit> findByEvrakTipi(String evrakTipi) {
        return evrakKayitRepo.findByEvrakTipi(evrakTipi);
    }

    @Override
    @Transactional(readOnly = true)
    public List<EvrakKayit> findByGirisTarihBetween(Date startDate, Date endDate) {
        return evrakKayitRepo.findByGirisTarihBetween(startDate, endDate);
    }

    @Override
    @Transactional(readOnly = true)
    public List<EvrakKayit> findByDurumu(String durumu) {
        return evrakKayitRepo.findByDurumu(durumu);
    }

    @Override
    @Transactional(readOnly = true)
    public List<EvrakKayit> findByHavaleBirim(String havaleBirim) {
        return evrakKayitRepo.findByHavaleBirim(havaleBirim);
    }



    @Override
    @Transactional(readOnly = true)
    public List<EvrakKayit> findByAcilmi(String acilmi) {
        return evrakKayitRepo.findByAcilmi(acilmi);
    }
}
