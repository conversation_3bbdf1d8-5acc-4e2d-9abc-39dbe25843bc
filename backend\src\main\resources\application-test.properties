# Test Environment Configuration for Backend
server.servlet.context-path=/api

## default connection pool for test
spring.datasource.hikari.connectionTimeout=20000
spring.datasource.hikari.maximumPoolSize=5

## to avoid exception on startup: NoSuchFileException: mchange-commons-java-0.2.19.jar
server.tomcat.additional-tld-skip-patterns=*mchange-commons-java*.jar

# Test logging configuration
logging.level.*=INFO
logging.level.com.zaxxer.hikari.HikariConfig=ERROR
logging.level.com.zaxxer.hikari=ERROR
logging.level.org.hibernate=ERROR
logging.level.iym=DEBUG

# Swagger configuration for test
springdoc.swagger-ui.enabled=true
springdoc.api-docs.enabled=true

# JWT Configuration for test
app.jwtSecret=test-secret-key-asdRET567-asdHd-52sdauer-dfgZSDfas5sCd34df-123dFTH56HG-asd45FgbsdDd334-aasd456fdvb
# 1 hour for test (shorter for security)
app.jwtExpirationInSec=3600

# CORS Configuration for test
cors.allowed.origins=http://localhost:3000,http://localhost:4000,http://127.0.0.1:3000,http://127.0.0.1:4000

makos.api.base-url=${MAKOS_API_BASE_URL:http://localhost:5000/makosapi}
makos.api.username=${MAKOS_API_USERNAME:}
makos.api.password=${MAKOS_API_PASSWORD:}
makos.api.connect-timeout=${MAKOS_API_CONNECT_TIMEOUT:5000}
makos.api.read-timeout=${MAKOS_API_READ_TIMEOUT:30000}