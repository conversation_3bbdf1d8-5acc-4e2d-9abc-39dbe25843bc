package iym.makos.handler;

import iym.common.model.api.EvrakKurum;
import iym.common.model.api.EvrakTuru;
import iym.common.model.api.MahkemeKararTip;
import iym.common.model.entity.iym.*;
import iym.common.util.CommonUtils;
import iym.db.jpa.dao.*;
import iym.makos.errors.MakosResponseErrorCodes;
import iym.makos.errors.MakosResponseException;
import iym.makos.mapper.KararRequestMapper;
import iym.makos.model.api.EvrakDetay;
import iym.makos.model.reqrep.MahkemeKararRequest;
import iym.makos.utils.UtilService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Optional;

@Service
@Slf4j
public class MahkemeKararRequestCommonDbSaver {

    @Autowired
    private UtilService utilService;
    private KararRequestMapper kararRequestMapper;
    private EvrakKayitRepo evrakKayitReporo;
    private MahkemeBilgisiRepo mahkemeBilgisiRepo;
    private MahkemeKararTalepRepo mahkemeKararTalepRepo;
    private HtsMahkemeKararTalepRepo htsMahkemeKararTalepRepo;

    public MahkemeKararRequestCommonDbSaver(UtilService utilService
            , KararRequestMapper kararRequestMapper
            , EvrakKayitRepo evrakKayitReporo
            , MahkemeBilgisiRepo mahkemeBilgisiRepo
            , MahkemeKararTalepRepo mahkemeKararTalepRepo
            , HtsMahkemeKararTalepRepo htsMahkemeKararTalepRepo) {
        this.utilService = utilService;
        this.kararRequestMapper = kararRequestMapper;
        this.mahkemeBilgisiRepo = mahkemeBilgisiRepo;
        this.evrakKayitReporo = evrakKayitReporo;
        this.mahkemeKararTalepRepo = mahkemeKararTalepRepo;
        this.htsMahkemeKararTalepRepo = htsMahkemeKararTalepRepo;
    }

    public Long handleDbSave(MahkemeKararRequest kararRequest, Date kayitTarihi, Long kullaniciId) throws Exception {

        Long mahkemeKararTalepId = 0L;
        String fileName = "";
        String evrakGelenKurumKodu = kararRequest.getEvrakDetay().getEvrakKurumKodu();
        EvrakKurum evrakKurum = CommonUtils.getEvrakKurum(evrakGelenKurumKodu);

        MahkemeKararTip kararTipi = kararRequest.getMahkemeKararBilgisi().getMahkemeKararTipi();
        String evrakTipi = CommonUtils.evrakTipiBelirle(evrakKurum, kararTipi);

        EvrakKayit savedEvrak = saveEvrakBilgileri(kararRequest.getEvrakDetay(), evrakTipi, kayitTarihi, kullaniciId);
        Long savedEvrakId = savedEvrak.getId();

        //Evrak File Kaydet
        EvrakFiles evrakFile = kararRequestMapper.toEvrakFiles(savedEvrakId, fileName, false, 1);

        //Save Mahkeme Karar Talep
        String mahkemeKodu = kararRequest.getMahkemeKararBilgisi().getMahkemeKararDetay().getMahkemeKodu();
        Optional<MahkemeBilgi> mahkemeBilgi = mahkemeBilgisiRepo.findByMahkemeKodu(mahkemeKodu);
        if (mahkemeBilgi.isEmpty()) {
            throw new MakosResponseException(MakosResponseErrorCodes.MAHKEMEBILGISI_BULUNAMADI, mahkemeKodu);
        }

        if (kararRequest.getEvrakDetay().getEvrakTuru() == EvrakTuru.ILETISIMIN_TESPITI) {
            HtsMahkemeKararTalep mahkemeKararTalep = kararRequestMapper.toHTSMahkemeKararTalep(kararRequest.getMahkemeKararBilgisi(), savedEvrakId, kullaniciId, kayitTarihi, mahkemeBilgi.get().getMahkemeAdi());
            HtsMahkemeKararTalep savedMahkemeKararTalep = htsMahkemeKararTalepRepo.save(mahkemeKararTalep);
            mahkemeKararTalepId = savedMahkemeKararTalep.getId();
        } else if (kararRequest.getEvrakDetay().getEvrakTuru() == EvrakTuru.ILETISIMIN_DENETLENMESI || kararRequest.getEvrakDetay().getEvrakTuru() == EvrakTuru.GENEL_EVRAK) {
            MahkemeKararTalep mahkemeKararTalep = kararRequestMapper.toMahkemeKararTalep(kararRequest.getMahkemeKararBilgisi(), savedEvrakId, kullaniciId, kayitTarihi, mahkemeBilgi.get().getMahkemeAdi());
            MahkemeKararTalep savedMahkemeKararTalep = mahkemeKararTalepRepo.save(mahkemeKararTalep);
            mahkemeKararTalepId = savedMahkemeKararTalep.getId();
        }

        return mahkemeKararTalepId;
    }

    private EvrakKayit saveEvrakBilgileri(EvrakDetay evrakDetay, String evrakTipi, Date islemTarihi, Long kaydedenKullaniciId) throws Exception {

        String evrakGelenKurumKodu = evrakDetay.getEvrakKurumKodu();
        EvrakKurum evrakKurum = CommonUtils.getEvrakKurum(evrakGelenKurumKodu);

        //MahkemeKararTip kararTipi = kararRequest.getMahkemeKararDetayi().getMahkemeKararBilgisi().getMahkemeKararTipi();
        //String evrakTipi = CommonUtils.evrakTipiBelirle(evrakKurum, kararTipi);

        //Evrak sira numarasi
        String evrakSiraNo = utilService.getEvrakSiraNumarasi(evrakDetay.getEvrakKurumKodu(), evrakDetay.getEvrakTuru().name());
        if (CommonUtils.isNullOrEmpty(evrakSiraNo)) {
            throw new MakosResponseException(MakosResponseErrorCodes.EVRAK_KAYDETMEHATASI);
        }

        String gelenEvrakNo = evrakDetay.getEvrakNo();
        String evrakIlIlceKodu = evrakDetay.getGeldigiIlIlceKodu();
        String evrakKurumKodu = evrakDetay.getEvrakKurumKodu();

        //Bunu genel validatore koy
        Optional<EvrakKayit> ayniEvrak = evrakKayitReporo.findByEvrakNoAndGeldigiIlIlceKoduAndEvrakGeldigiKurumKodu(gelenEvrakNo, evrakIlIlceKodu, evrakKurumKodu);
        if (ayniEvrak.isPresent()) {
            throw new MakosResponseException(MakosResponseErrorCodes.EVRAK_ZATENVAR, gelenEvrakNo, evrakIlIlceKodu, evrakKurumKodu);
        }

        //Save Evrak
        EvrakKayit evrakKayit = kararRequestMapper.toEvrakKayit(evrakDetay, evrakSiraNo, evrakTipi, islemTarihi, kaydedenKullaniciId);
        return evrakKayitReporo.save(evrakKayit);
    }


}
