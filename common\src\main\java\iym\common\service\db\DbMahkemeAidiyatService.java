package iym.common.service.db;

import iym.common.model.entity.iym.MahkemeAidiyat;
import iym.common.model.entity.iym.MahkemeAidiyatTalep;

import java.util.List;
import java.util.Optional;

/**
 * Service interface for MahkemeAidiyatTalep entity
 */
public interface DbMahkemeAidiyatService extends GenericDbService<MahkemeAidiyat, Long> {

    List<MahkemeAidiyat> findByMahkemeKararId(Long mahkemeKararId);

    Optional<MahkemeAidiyat> findByMahkemeKararIdAndAidiyatKod(Long mahkemeKararId, String aidiyatKod);
}
