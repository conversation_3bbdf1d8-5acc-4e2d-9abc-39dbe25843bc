package iym.makos.dto.id;

import iym.makos.model.reqrep.MahkemeKararResponse;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class IDMahkemeKoduGuncellemeResponse extends MahkemeKararResponse {

    @NotNull
    private Long evrakId;

}