package iym.makos.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * DTO for CanakNumaralar entity
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Çanak Numaralar bilgilerini içerir")
public class CanakNumaralarDTO {

    @Schema(description = "Çanak numaralar ID")
    private Long id;

    @Schema(description = "Çanak numarası", example = "CANAK001")
    @NotNull(message = "Çanak numarası boş olamaz")
    @Size(max = 20, message = "Çanak numarası 20 karakterden fazla olamaz")
    private String canakNo;

    @Schema(description = "Kurum kodu", example = "01")
    @NotNull(message = "Kurum kodu boş olamaz")
    @Size(max = 2, message = "Kurum kodu 2 karakterden fazla olamaz")
    private String kurumKod;

    @Schema(description = "Ekleme tarihi", example = "2023-01-01T00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "Europe/Istanbul")
    private Date eklemeTarih;

    @Schema(description = "Kutu numarası", example = "1")
    private Long kutu;

    @Schema(description = "Açıklama", example = "Örnek çanak açıklaması")
    @Size(max = 300, message = "Açıklama 300 karakterden fazla olamaz")
    private String aciklama;

    @Schema(description = "Ekleyen kullanıcı ID")
    private Long ekleyenId;
}
