package iym.db.jpa.service.impl;


import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.IymUser;
import iym.common.model.enums.IymUserRoleType;
import iym.common.model.enums.UserStatusType;
import iym.common.service.db.DbIymUserService;
import iym.common.util.ExceptionUtils;
import iym.db.jpa.dao.IymUserRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.UUID;


@Service
public class DbIymUserServiceImpl extends GenericDbServiceImpl<IymUser, UUID> implements DbIymUserService {

    private final PasswordEncoder passwordEncoder;

    @Autowired
    public DbIymUserServiceImpl(IymUserRepo repository, @Lazy PasswordEncoder passwordEncoder) {
        super(repository);
        this.passwordEncoder = passwordEncoder;
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<IymUser> findByUsername(String name) {
        return getRepository().findByUsername(name);
    }

    @Override
    @Transactional(readOnly = true)
    public List<IymUser> findByStatus(UserStatusType status) {
        return getRepository().findByStatus(status);
    }

    @Override
    @Transactional(readOnly = true)
    public List<IymUser> findByRoleOrderByUsernameAsc(IymUserRoleType role) {
        return getRepository().findByRoleOrderByUsernameAsc(role);
    }

    @Override
    @Transactional(readOnly = true)
    public List<IymUser> findAllByOrderByUsernameAsc() {
        return getRepository().findAllByOrderByUsernameAsc();
    }

    @Override
    @Transactional
    public void activateUser(String username) {
        IymUser user = checkUser(username);
        user.setStatus(UserStatusType.ACTIVE);
        repository.save(user);
    }

    @Override
    @Transactional
    public void deactivateUser(String username) {
        IymUser user = checkUser(username);
        user.setStatus(UserStatusType.PASSIVE);
        repository.save(user);
    }

    @Override
    @Transactional
    public void updateUser(IymUser user) {
        IymUser userDb = checkUser(user.getUsername());
        userDb.setRole(user.getRole());
        userDb.setKurum(user.getKurum());

        // Changing password is optional
        if(user.getNewPassword() != null ){
            // check if new password is different then old password
            if(passwordEncoder.matches(user.getNewPassword(), userDb.getPassword())) {
                throw ExceptionUtils.newBadRequest("Yeni şifre eskisiyle aynı olamaz.");
            }

            // update user password
            userDb.setPassword(passwordEncoder.encode(user.getNewPassword()));
        }

        repository.save(userDb);
    }

    private IymUser checkUser(String username) {
        Optional<IymUser> userOptional = getRepository().findByUsername(username);
        IymUser user;

        if (userOptional.isEmpty()) {
            throw ExceptionUtils.USER_NOT_FOUND;
        } else {
            user = userOptional.get();
        }

        return user;
    }

    public IymUserRepo getRepository() {
        return (IymUserRepo) repository;
    }

}
