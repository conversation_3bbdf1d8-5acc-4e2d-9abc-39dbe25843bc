package iym.makos.dto.user;

import iym.common.model.api.ApiResponseBase;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

/**
 * Delete user response DTO for MAKOS user management
 */
@Data
@SuperBuilder
@ToString
@EqualsAndHashCode(callSuper = true)
public class DeleteUserResponse extends ApiResponseBase {
    // Inherits ApiResponse response from ApiResponseBase
} 