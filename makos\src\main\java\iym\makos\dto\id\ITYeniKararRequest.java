package iym.makos.dto.id;

import iym.common.model.api.KararTuru;
import iym.common.model.api.MahkemeKararTip;
import iym.common.util.CommonUtils;
import iym.common.validation.ValidationResult;
import iym.makos.model.api.HedefDetayID;
import iym.makos.model.reqrep.MahkemeKararRequest;
import iym.makos.validator.MakosRequestValid;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Jacksonized
@Data
@NoArgsConstructor
@SuperBuilder
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@MakosRequestValid
@Slf4j
public class ITYeniKararRequest extends MahkemeKararRequest {

    @NotNull
    @Size(min = 1)
    @Valid
    private List<HedefDetayID> hedefDetayListesi;

    private List<String> mahkemeAidiyatKodlari;

    private List<String> mahkemeSucTipiKodlari;

    @Override
    public ValidationResult isValid() {
        log.trace("Checking if IDYeniKararRequest is valid");
        ValidationResult validationResult = new ValidationResult(true);
        try {

        } catch (Exception e) {
            log.error("Validation failed", e);
            return new ValidationResult("Validation failed. Internal error");
        }
        return validationResult;
    }

    @Override
    protected void assignKararTuru() {
        this.kararTuru = KararTuru.ILETISIMIN_TESPITI;
    }
}

