package iym.db.jpa.dao;

import iym.common.model.entity.iym.MahkemeAidiyatTalep;
import jakarta.persistence.Column;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for MahkemeAidiyatTalep entity
 */
@Repository
public interface MahkemeAidiyatTalepRepo extends JpaRepository<MahkemeAidiyatTalep, Long> {

    Optional<MahkemeAidiyatTalep> findById(Long id);

    List<MahkemeAidiyatTalep> findByMahkemeKararTalepId(Long mahkemeKararTalepId);

    Optional<MahkemeAidiyatTalep> findByMahkemeKararTalepIdAndAidiyatKod(Long mahkemeKararTalepId, String aidiyatKodu);

}
