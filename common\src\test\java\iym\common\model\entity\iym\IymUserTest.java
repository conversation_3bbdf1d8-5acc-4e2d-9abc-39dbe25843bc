package iym.common.model.entity.iym;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import iym.common.model.api.KullaniciKurum;
import iym.common.model.enums.IymUserRoleType;
import iym.common.model.enums.UserStatusType;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Set;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;

class IymUserTest {

    private Validator validator;
    private ObjectMapper objectMapper;
    private IymUser validIymUser;

    @BeforeEach
    void setUp() {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
        objectMapper = new ObjectMapper();

        validIymUser = IymUser.builder()
                .id(UUID.randomUUID())
                .username("test_user")
                .password("test_password")
                .status(UserStatusType.ACTIVE)
                .role(IymUserRoleType.ROLE_ADMIN)
                .kurum(KullaniciKurum.BTK)
                .build();
    }

    @Test
    void validIymUser_shouldPassValidation() {
        // When
        Set<ConstraintViolation<IymUser>> violations = validator.validate(validIymUser);

        // Then
        assertThat(violations).isEmpty();
    }

    @Test
    void iymUser_withNullUsername_shouldFailValidation() {
        // Given
        IymUser iymUser = IymUser.builder()
                .id(UUID.randomUUID())
                .username(null)
                .password("test_password")
                .status(UserStatusType.ACTIVE)
                .role(IymUserRoleType.ROLE_ADMIN)
                .kurum(KullaniciKurum.BTK)
                .build();

        // When
        Set<ConstraintViolation<IymUser>> violations = validator.validate(iymUser);

        // Then
        assertThat(violations).hasSize(2); // @NotNull and @NotBlank both trigger
        assertThat(violations).allMatch(v -> v.getPropertyPath().toString().equals("username"));
    }

    @Test
    void iymUser_withBlankUsername_shouldFailValidation() {
        // Given
        IymUser iymUser = IymUser.builder()
                .id(UUID.randomUUID())
                .username("   ")
                .password("test_password")
                .status(UserStatusType.ACTIVE)
                .role(IymUserRoleType.ROLE_ADMIN)
                .kurum(KullaniciKurum.BTK)
                .build();

        // When
        Set<ConstraintViolation<IymUser>> violations = validator.validate(iymUser);

        // Then
        assertThat(violations).hasSize(2); // @NotBlank and @Size both trigger
        assertThat(violations).allMatch(v -> v.getPropertyPath().toString().equals("username"));
    }

    @Test
    void iymUser_withShortUsername_shouldFailValidation() {
        // Given
        IymUser iymUser = IymUser.builder()
                .id(UUID.randomUUID())
                .username("abc")
                .password("test_password")
                .status(UserStatusType.ACTIVE)
                .role(IymUserRoleType.ROLE_ADMIN)
                .kurum(KullaniciKurum.BTK)
                .build();

        // When
        Set<ConstraintViolation<IymUser>> violations = validator.validate(iymUser);

        // Then
        assertThat(violations).hasSize(1);
        assertThat(violations.iterator().next().getPropertyPath().toString()).isEqualTo("username");
    }

    @Test
    void iymUser_withLongUsername_shouldFailValidation() {
        // Given
        String longUsername = "a".repeat(101);
        IymUser iymUser = IymUser.builder()
                .id(UUID.randomUUID())
                .username(longUsername)
                .password("test_password")
                .status(UserStatusType.ACTIVE)
                .role(IymUserRoleType.ROLE_ADMIN)
                .kurum(KullaniciKurum.BTK)
                .build();

        // When
        Set<ConstraintViolation<IymUser>> violations = validator.validate(iymUser);

        // Then
        assertThat(violations).hasSize(1);
        assertThat(violations.iterator().next().getPropertyPath().toString()).isEqualTo("username");
    }

    @Test
    void iymUser_withNullPassword_shouldFailValidation() {
        // Given
        IymUser iymUser = IymUser.builder()
                .id(UUID.randomUUID())
                .username("test_user")
                .password(null)
                .status(UserStatusType.ACTIVE)
                .role(IymUserRoleType.ROLE_ADMIN)
                .kurum(KullaniciKurum.BTK)
                .build();

        // When
        Set<ConstraintViolation<IymUser>> violations = validator.validate(iymUser);

        // Then
        assertThat(violations).hasSize(2); // @NotNull and @NotBlank both trigger
        assertThat(violations).allMatch(v -> v.getPropertyPath().toString().equals("password"));
    }

    @Test
    void iymUser_withShortPassword_shouldFailValidation() {
        // Given
        IymUser iymUser = IymUser.builder()
                .id(UUID.randomUUID())
                .username("test_user")
                .password("1234")
                .status(UserStatusType.ACTIVE)
                .role(IymUserRoleType.ROLE_ADMIN)
                .kurum(KullaniciKurum.BTK)
                .build();

        // When
        Set<ConstraintViolation<IymUser>> violations = validator.validate(iymUser);

        // Then
        assertThat(violations).hasSize(1);
        assertThat(violations.iterator().next().getPropertyPath().toString()).isEqualTo("password");
    }

    @Test
    void iymUser_withNullStatus_shouldFailValidation() {
        // Given
        IymUser iymUser = IymUser.builder()
                .id(UUID.randomUUID())
                .username("test_user")
                .password("test_password")
                .status(null)
                .role(IymUserRoleType.ROLE_ADMIN)
                .kurum(KullaniciKurum.BTK)
                .build();

        // When
        Set<ConstraintViolation<IymUser>> violations = validator.validate(iymUser);

        // Then
        assertThat(violations).hasSize(1);
        assertThat(violations.iterator().next().getPropertyPath().toString()).isEqualTo("status");
    }

    @Test
    void iymUser_withNullRole_shouldFailValidation() {
        // Given
        IymUser iymUser = IymUser.builder()
                .id(UUID.randomUUID())
                .username("test_user")
                .password("test_password")
                .status(UserStatusType.ACTIVE)
                .role(null)
                .kurum(KullaniciKurum.BTK)
                .build();

        // When
        Set<ConstraintViolation<IymUser>> violations = validator.validate(iymUser);

        // Then
        assertThat(violations).hasSize(1);
        assertThat(violations.iterator().next().getPropertyPath().toString()).isEqualTo("role");
    }

    @Test
    void isActive_shouldReturnTrue_whenStatusIsActive() {
        // Given
        IymUser activeUser = IymUser.builder()
                .status(UserStatusType.ACTIVE)
                .build();

        // When
        boolean result = activeUser.isActive();

        // Then
        assertThat(result).isTrue();
    }

    @Test
    void isActive_shouldReturnFalse_whenStatusIsPassive() {
        // Given
        IymUser passiveUser = IymUser.builder()
                .status(UserStatusType.PASSIVE)
                .build();

        // When
        boolean result = passiveUser.isActive();

        // Then
        assertThat(result).isFalse();
    }

    @Test
    void isActive_shouldReturnFalse_whenStatusIsLocked() {
        // Given
        IymUser lockedUser = IymUser.builder()
                .status(UserStatusType.LOCKED)
                .build();

        // When
        boolean result = lockedUser.isActive();

        // Then
        assertThat(result).isFalse();
    }

    @Test
    void toString_shouldNotIncludePassword() {
        // When
        String result = validIymUser.toString();

        // Then
        assertThat(result).contains("IymUser{");
        assertThat(result).contains("id=" + validIymUser.getId());
        assertThat(result).contains("username='test_user'");
        assertThat(result).contains("status=ACTIVE");
        assertThat(result).doesNotContain("password");
        assertThat(result).doesNotContain("test_password");
    }

    @Test
    void jsonSerialization_shouldIgnorePassword() throws JsonProcessingException {
        // When
        String json = objectMapper.writeValueAsString(validIymUser);

        // Then
        assertThat(json).doesNotContain("password");
        assertThat(json).doesNotContain("test_password");
        assertThat(json).contains("username");
        assertThat(json).contains("test_user");
    }

    @Test
    void equalsAndHashCode_shouldWorkCorrectly() {
        // Given
        UUID sameId = UUID.randomUUID();
        IymUser user1 = IymUser.builder()
                .id(sameId)
                .username("user1")
                .password("password1")
                .status(UserStatusType.ACTIVE)
                .role(IymUserRoleType.ROLE_ADMIN)
                .kurum(KullaniciKurum.BTK)
                .build();

        IymUser user2 = IymUser.builder()
                .id(sameId)
                .username("user1")
                .password("password1")
                .status(UserStatusType.ACTIVE)
                .role(IymUserRoleType.ROLE_ADMIN)
                .kurum(KullaniciKurum.BTK)
                .build();

        IymUser user3 = IymUser.builder()
                .id(UUID.randomUUID())
                .username("user3")
                .password("password3")
                .status(UserStatusType.PASSIVE)
                .role(IymUserRoleType.ROLE_ADLI)
                .kurum(KullaniciKurum.ADLI)
                .build();

        // Then
        assertThat(user1).isEqualTo(user2);
        assertThat(user1).isNotEqualTo(user3);
        assertThat(user1.hashCode()).isEqualTo(user2.hashCode());
        assertThat(user1.hashCode()).isNotEqualTo(user3.hashCode());
    }
}
