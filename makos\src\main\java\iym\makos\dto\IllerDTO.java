package iym.makos.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for Iller entity
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "İl ve ilçe bilgilerini içerir")
public class IllerDTO {

    @Schema(description = "İl kodu", example = "0600")
    @NotNull(message = "İl kodu boş olamaz")
    @Size(max = 4, message = "İl kodu 4 karakterden fazla olamaz")
    private String ilKod;

    @Schema(description = "İl adı", example = "ANKARA")
    @Size(max = 50, message = "İl adı 50 karakterden fazla olamaz")
    private String ilAdi;

    @Schema(description = "İlçe adı", example = "MERKEZ")
    @Size(max = 50, message = "İlçe adı 50 karakterden fazla olamaz")
    private String ilceAdi;
}
