package iym.makos.service.makos;

import iym.common.service.db.DbMahkemeBilgiService;
import iym.makos.dto.MahkemeBilgiDTO;
import iym.makos.mapper.MahkemeBilgiMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

/**
 * Service for Iller operations
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class MahkemeBilgiService {

    private final DbMahkemeBilgiService dbMahkemeBilgiService;
    private final MahkemeBilgiMapper mahkemeBilgiMapper;

    public MahkemeBilgiDTO findByMahkemeKodu(String mahkemeKodu){
        return dbMahkemeBilgiService.findByMahkemeKodu(mahkemeKodu)
                .map(mahkemeBilgiMapper::toDto)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Mahkeme bilgisi bulunamadı"));
    }

}
