package iym.makos.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * DTO for DMahkemeKararTalep entity
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "DMahkeme Karar Talep bilgilerini içerir")
public class DMahkemeKararTalepDTO {

    @Schema(description = "Mahkeme karar talep ID")
    private Long id;



    @Schema(description = "Mahkeme Karar Talep ID")
    @NotNull(message = "Mahkeme Karar Talep ID boş olamaz")
    private Long mahkemeKararId;

    @Schema(description = "Evrak ID")
    @NotNull(message = "Evrak ID boş olamaz")
    private Long evrakId;

    @Schema(description = "Kullanıcı ID")
    @NotNull(message = "Kullanıcı ID boş olamaz")
    private Long kullaniciId;


    private Date kayitTarihi;


    private String durum;


    private Long iliskiliMahkemeKararId;


    private String kararTipDetay;


    private String mahkemeAdiDetay;


    private String mahkemeKararNoDetay;

    private String sorusturmaNoDetay;


    private String mahkemeIlIlceKoduDetay;


    private String mahkemeKoduDetay;


    private String aciklama;



}
