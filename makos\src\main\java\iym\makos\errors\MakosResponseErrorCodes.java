package iym.makos.errors;
/*
hata kodları sonra duzenlenecek
* */
public enum MakosResponseErrorCodes {
    BILINMEYEN_HATA(10, "Bilinmeyen Hata Oluştu"),
    MAHKEMEKARAR_BULUNAMADI(2010, "Mahkeme Karar Bulunamadı. Mahheme İl/İlçe Kodu: %s Mahkeme <PERSON>du: %s, Karar No: %s, Soruşturma No :%s"),
    HEDEF_BULUNAMADI(2010, "Hedef bulunmadı. Hedef No: %s, Hedef Tipi:%s "),
    CANAKNO_BOS_OLAMAZ(2010, "Çanak numarası boş olamaz"),
    CANAKNO_BOS_OLMALIDIR(2010, "Çanak numarası boş olmalıdır."),
    BILINMEYEN_MAHKEME_KARAR_TIPI(10, "Bilinmeyen mahkeme karar Tipi. <PERSON><PERSON><PERSON><PERSON> Kara<PERSON> Tipi: %s"),
    EVRAK_KAYDETMEHATASI(1000, "Evrak kayıt Hatası"),
    MAHKEMEBILGISI_BULUNAMADI(2000, "Mahkeme bilgisi bulunamadı. Mahkeme Kodu: %s"),
    EVRAK_SIRANO_ALINAMADI(1010, "Evrak Sıra No Alınamadı"),
    EVRAK_ZATENVAR(1020, "Evrak Zaten Kayıtlı. EvrakNo: %s Evrak Geldigi Il:%s Evrak Kurum:%s"),
    MAHKEMEKARARTALEP_KAYDETMEHATASI(2000, "Mahkeme karar kaydedilemedi"),
    HTSMAHKEMEKARARTALEP_KAYDETMEHATASI(2000, "HTS Mahkeme karar kaydedilemedi"),
    MAHKEMEKARARTALEP_SUCTIPI_KAYDETMEHATASI(2010, "Mahkeme karar talep suç tipi kaydetme hatası. Suç Tipi Kodu : %s"),
    MAHKEMEKARARTALEP_AIDIYAT_KAYDETMEHATASI(2010, "Mahkeme karar talep aidiyat kaydetme hatası. Aidiyat Kodu : %s"),
    MAHKEMEKARARTALEP_AIDIYAT_GUNCELLEMEMEHATASI(2010, "Mahkeme karar talep aidiyat güncellme hatası. Aidiyat Kodu : %s"),
    MAHKEMEKARARTALEP_HEDEF_KAYDETMEHATASI(2010, "Mahkeme karar talep hedef kaydetme hatası. Hedef No : %s Hedef Tipi :%s"),
    MAHKEMEKARARTALEP_HEDEFDETAY_KAYDETMEHATASI(2010, "Mahkeme karar talep hedef detay kaydetme hatası. Hedef No : %s Hedef Tipi :%s"),
    MAHKEMEKARARTALEP_DETAY_KAYDETMEHATASI(2010, "Mahkeme karar talep detay  kaydetme hatası. "),
    MAHKEMEKARARTALEP_MAHKEMEKODUDETAY_KAYDETMEHATASI(2010, "Mahkeme karar mahkeme kodu talep detay  kaydetme hatası. "),
    MAHKEMEKARARTALEP_HEDEFAIDIYAT_KAYDETMEHATASI(2010, "Hedef aidiyat kaydetme hatası. Hedef No: %s, Aidiyat Kodu: %s")


            ;

    private final int code;
    private final String messageTemplate;

    MakosResponseErrorCodes(int code, String messageTemplate) {
        this.code = code;
        this.messageTemplate = messageTemplate;
    }

    public int getCode() {
        return code;
    }

    public String getTemplate() {
        return messageTemplate;
    }

    public String format(Object... args) {
        return String.format(messageTemplate, args);
    }
}