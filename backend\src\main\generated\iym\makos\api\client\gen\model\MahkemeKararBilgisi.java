/*
 * MAKOS OpenAPI definition
 * MAKOS Application
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package iym.makos.api.client.gen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import iym.makos.api.client.gen.model.MahkemeKararDetay;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * Güncellemeyi talep eden  mahkeme karar bilgileri
 */
@JsonPropertyOrder({
  MahkemeKararBilgisi.JSON_PROPERTY_MAHKEME_KARAR_TIPI,
  MahkemeKararBilgisi.JSON_PROPERTY_MAHKEME_KARAR_DETAY,
  MahkemeKararBilgisi.JSON_PROPERTY_MAHKEME_KARAR_NO,
  MahkemeKararBilgisi.JSON_PROPERTY_SORUSTURMA_NO,
  MahkemeKararBilgisi.JSON_PROPERTY_MAHKEME_KODU,
  MahkemeKararBilgisi.JSON_PROPERTY_IL_ILCE_KODU,
  MahkemeKararBilgisi.JSON_PROPERTY_ACIKLAMA
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class MahkemeKararBilgisi {
  /**
   * Gets or Sets mahkemeKararTipi
   */
  public enum MahkemeKararTipiEnum {
    _100("100"),
    
    _150("150"),
    
    _151("151"),
    
    _200("200"),
    
    _300("300"),
    
    _350("350"),
    
    _400("400"),
    
    _410("410"),
    
    _450("450"),
    
    _510("510"),
    
    _520("520"),
    
    _530("530"),
    
    _600("600"),
    
    _700("700"),
    
    _710("710"),
    
    _720("720"),
    
    _730("730"),
    
    _800("800"),
    
    _900("900"),
    
    _910("910"),
    
    _920("920");

    private String value;

    MahkemeKararTipiEnum(String value) {
      this.value = value;
    }

    @JsonValue
    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static MahkemeKararTipiEnum fromValue(String value) {
      for (MahkemeKararTipiEnum b : MahkemeKararTipiEnum.values()) {
        if (b.value.equals(value)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + value + "'");
    }
  }

  public static final String JSON_PROPERTY_MAHKEME_KARAR_TIPI = "mahkemeKararTipi";
  private MahkemeKararTipiEnum mahkemeKararTipi;

  public static final String JSON_PROPERTY_MAHKEME_KARAR_DETAY = "mahkemeKararDetay";
  private MahkemeKararDetay mahkemeKararDetay;

  public static final String JSON_PROPERTY_MAHKEME_KARAR_NO = "mahkemeKararNo";
  private String mahkemeKararNo;

  public static final String JSON_PROPERTY_SORUSTURMA_NO = "sorusturmaNo";
  private String sorusturmaNo;

  public static final String JSON_PROPERTY_MAHKEME_KODU = "mahkemeKodu";
  private String mahkemeKodu;

  public static final String JSON_PROPERTY_IL_ILCE_KODU = "ilIlceKodu";
  private String ilIlceKodu;

  public static final String JSON_PROPERTY_ACIKLAMA = "aciklama";
  private String aciklama;

  public MahkemeKararBilgisi() {
  }

  public MahkemeKararBilgisi mahkemeKararTipi(MahkemeKararTipiEnum mahkemeKararTipi) {
    
    this.mahkemeKararTipi = mahkemeKararTipi;
    return this;
  }

   /**
   * Get mahkemeKararTipi
   * @return mahkemeKararTipi
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_TIPI)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public MahkemeKararTipiEnum getMahkemeKararTipi() {
    return mahkemeKararTipi;
  }


  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_TIPI)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setMahkemeKararTipi(MahkemeKararTipiEnum mahkemeKararTipi) {
    this.mahkemeKararTipi = mahkemeKararTipi;
  }


  public MahkemeKararBilgisi mahkemeKararDetay(MahkemeKararDetay mahkemeKararDetay) {
    
    this.mahkemeKararDetay = mahkemeKararDetay;
    return this;
  }

   /**
   * Get mahkemeKararDetay
   * @return mahkemeKararDetay
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_DETAY)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public MahkemeKararDetay getMahkemeKararDetay() {
    return mahkemeKararDetay;
  }


  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_DETAY)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setMahkemeKararDetay(MahkemeKararDetay mahkemeKararDetay) {
    this.mahkemeKararDetay = mahkemeKararDetay;
  }


  public MahkemeKararBilgisi mahkemeKararNo(String mahkemeKararNo) {
    
    this.mahkemeKararNo = mahkemeKararNo;
    return this;
  }

   /**
   * Get mahkemeKararNo
   * @return mahkemeKararNo
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_NO)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getMahkemeKararNo() {
    return mahkemeKararNo;
  }


  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_NO)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMahkemeKararNo(String mahkemeKararNo) {
    this.mahkemeKararNo = mahkemeKararNo;
  }


  public MahkemeKararBilgisi sorusturmaNo(String sorusturmaNo) {
    
    this.sorusturmaNo = sorusturmaNo;
    return this;
  }

   /**
   * Get sorusturmaNo
   * @return sorusturmaNo
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SORUSTURMA_NO)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getSorusturmaNo() {
    return sorusturmaNo;
  }


  @JsonProperty(JSON_PROPERTY_SORUSTURMA_NO)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSorusturmaNo(String sorusturmaNo) {
    this.sorusturmaNo = sorusturmaNo;
  }


  public MahkemeKararBilgisi mahkemeKodu(String mahkemeKodu) {
    
    this.mahkemeKodu = mahkemeKodu;
    return this;
  }

   /**
   * Get mahkemeKodu
   * @return mahkemeKodu
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MAHKEME_KODU)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getMahkemeKodu() {
    return mahkemeKodu;
  }


  @JsonProperty(JSON_PROPERTY_MAHKEME_KODU)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMahkemeKodu(String mahkemeKodu) {
    this.mahkemeKodu = mahkemeKodu;
  }


  public MahkemeKararBilgisi ilIlceKodu(String ilIlceKodu) {
    
    this.ilIlceKodu = ilIlceKodu;
    return this;
  }

   /**
   * Get ilIlceKodu
   * @return ilIlceKodu
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_IL_ILCE_KODU)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getIlIlceKodu() {
    return ilIlceKodu;
  }


  @JsonProperty(JSON_PROPERTY_IL_ILCE_KODU)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setIlIlceKodu(String ilIlceKodu) {
    this.ilIlceKodu = ilIlceKodu;
  }


  public MahkemeKararBilgisi aciklama(String aciklama) {
    
    this.aciklama = aciklama;
    return this;
  }

   /**
   * Get aciklama
   * @return aciklama
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ACIKLAMA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getAciklama() {
    return aciklama;
  }


  @JsonProperty(JSON_PROPERTY_ACIKLAMA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAciklama(String aciklama) {
    this.aciklama = aciklama;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    MahkemeKararBilgisi mahkemeKararBilgisi = (MahkemeKararBilgisi) o;
    return Objects.equals(this.mahkemeKararTipi, mahkemeKararBilgisi.mahkemeKararTipi) &&
        Objects.equals(this.mahkemeKararDetay, mahkemeKararBilgisi.mahkemeKararDetay) &&
        Objects.equals(this.mahkemeKararNo, mahkemeKararBilgisi.mahkemeKararNo) &&
        Objects.equals(this.sorusturmaNo, mahkemeKararBilgisi.sorusturmaNo) &&
        Objects.equals(this.mahkemeKodu, mahkemeKararBilgisi.mahkemeKodu) &&
        Objects.equals(this.ilIlceKodu, mahkemeKararBilgisi.ilIlceKodu) &&
        Objects.equals(this.aciklama, mahkemeKararBilgisi.aciklama);
  }

  @Override
  public int hashCode() {
    return Objects.hash(mahkemeKararTipi, mahkemeKararDetay, mahkemeKararNo, sorusturmaNo, mahkemeKodu, ilIlceKodu, aciklama);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MahkemeKararBilgisi {\n");
    sb.append("    mahkemeKararTipi: ").append(toIndentedString(mahkemeKararTipi)).append("\n");
    sb.append("    mahkemeKararDetay: ").append(toIndentedString(mahkemeKararDetay)).append("\n");
    sb.append("    mahkemeKararNo: ").append(toIndentedString(mahkemeKararNo)).append("\n");
    sb.append("    sorusturmaNo: ").append(toIndentedString(sorusturmaNo)).append("\n");
    sb.append("    mahkemeKodu: ").append(toIndentedString(mahkemeKodu)).append("\n");
    sb.append("    ilIlceKodu: ").append(toIndentedString(ilIlceKodu)).append("\n");
    sb.append("    aciklama: ").append(toIndentedString(aciklama)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

