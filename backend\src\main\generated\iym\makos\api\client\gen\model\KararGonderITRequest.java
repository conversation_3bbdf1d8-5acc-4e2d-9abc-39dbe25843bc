/*
 * MAKOS OpenAPI definition
 * MAKOS Application
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package iym.makos.api.client.gen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import iym.makos.api.client.gen.model.ITKararRequest;
import java.io.File;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * KararGonderITRequest
 */
@JsonPropertyOrder({
  KararGonderITRequest.JSON_PROPERTY_MAHKEME_KARAR_DOSYASI_I_T,
  KararGonderITRequest.JSON_PROPERTY_MAHKEME_KARAR_DETAY_I_T
})
@JsonTypeName("kararGonderIT_request")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class KararGonderITRequest {
  public static final String JSON_PROPERTY_MAHKEME_KARAR_DOSYASI_I_T = "mahkemeKararDosyasiIT";
  private File mahkemeKararDosyasiIT;

  public static final String JSON_PROPERTY_MAHKEME_KARAR_DETAY_I_T = "mahkemeKararDetayIT";
  private ITKararRequest mahkemeKararDetayIT;

  public KararGonderITRequest() {
  }

  public KararGonderITRequest mahkemeKararDosyasiIT(File mahkemeKararDosyasiIT) {
    
    this.mahkemeKararDosyasiIT = mahkemeKararDosyasiIT;
    return this;
  }

   /**
   * Get mahkemeKararDosyasiIT
   * @return mahkemeKararDosyasiIT
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_DOSYASI_I_T)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public File getMahkemeKararDosyasiIT() {
    return mahkemeKararDosyasiIT;
  }


  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_DOSYASI_I_T)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setMahkemeKararDosyasiIT(File mahkemeKararDosyasiIT) {
    this.mahkemeKararDosyasiIT = mahkemeKararDosyasiIT;
  }


  public KararGonderITRequest mahkemeKararDetayIT(ITKararRequest mahkemeKararDetayIT) {
    
    this.mahkemeKararDetayIT = mahkemeKararDetayIT;
    return this;
  }

   /**
   * Get mahkemeKararDetayIT
   * @return mahkemeKararDetayIT
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_DETAY_I_T)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public ITKararRequest getMahkemeKararDetayIT() {
    return mahkemeKararDetayIT;
  }


  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_DETAY_I_T)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setMahkemeKararDetayIT(ITKararRequest mahkemeKararDetayIT) {
    this.mahkemeKararDetayIT = mahkemeKararDetayIT;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    KararGonderITRequest kararGonderITRequest = (KararGonderITRequest) o;
    return Objects.equals(this.mahkemeKararDosyasiIT, kararGonderITRequest.mahkemeKararDosyasiIT) &&
        Objects.equals(this.mahkemeKararDetayIT, kararGonderITRequest.mahkemeKararDetayIT);
  }

  @Override
  public int hashCode() {
    return Objects.hash(mahkemeKararDosyasiIT, mahkemeKararDetayIT);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class KararGonderITRequest {\n");
    sb.append("    mahkemeKararDosyasiIT: ").append(toIndentedString(mahkemeKararDosyasiIT)).append("\n");
    sb.append("    mahkemeKararDetayIT: ").append(toIndentedString(mahkemeKararDetayIT)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

