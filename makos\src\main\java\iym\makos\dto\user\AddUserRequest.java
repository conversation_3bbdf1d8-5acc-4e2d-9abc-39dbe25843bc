package iym.makos.dto.user;

import iym.common.model.entity.makos.MakosUser;
import iym.common.validation.ValidationResult;
import iym.makos.model.MakosRequest;
import iym.makos.validator.MakosRequestValid;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;
import lombok.extern.slf4j.Slf4j;



/**
 * Add user request DTO for MAKOS user management
 */
@Data
@Builder
@Jacksonized
@ToString
@EqualsAndHashCode
@MakosRequestValid
@Slf4j
public class AddUserRequest implements MakosRequest {

    @NotNull
    private Long id;

    @NotNull
    @Valid
    private MakosUser user;

    @Override
    public ValidationResult isValid() {
        log.trace("Checking if AddUserRequest is valid");
        
        ValidationResult validationResult = new ValidationResult(true);
        
        try {
            if (id == null) {
                validationResult.addFailedReason("ID cannot be null");
            }
            
            if (user == null) {
                validationResult.addFailedReason("User cannot be null");
            } else {
                if (user.getUsername() == null || user.getUsername().trim().isEmpty()) {
                    validationResult.addFailedReason("Username cannot be null or empty");
                }
                
                if (user.getNewPassword() == null || user.getNewPassword().trim().isEmpty()) {
                    validationResult.addFailedReason("Password cannot be null or empty");
                }
            }
        } catch (Exception e) {
            log.error("Validation failed", e);
            validationResult.addFailedReason("Validation error: " + e.getMessage());
        }
        
        return validationResult;
    }
}
