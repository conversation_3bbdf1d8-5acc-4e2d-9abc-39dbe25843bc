package iym.makos.mapper;

import iym.common.model.entity.iym.MahkemeAidiyatDetayTalep;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import iym.makos.dto.MahkemeAidiyatDetayTalepDTO;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

class MahkemeAidiyatDetayTalepMapperTest {

    private MahkemeAidiyatDetayTalepMapper mahkemeAidiyatDetayTalepMapper;
    private MahkemeAidiyatDetayTalep mahkemeAidiyatDetayTalep;
    private MahkemeAidiyatDetayTalepDTO mahkemeAidiyatDetayTalepDTO;
    private Date testDate;

    @BeforeEach
    void setUp() {
        mahkemeAidiyatDetayTalepMapper = new MahkemeAidiyatDetayTalepMapper();
        testDate = new Date();

        mahkemeAidiyatDetayTalep = MahkemeAidiyatDetayTalep.builder()
                .id(1L)
                .iliskiliMahkemeKararId(100L)
                .mahkemeKararTalepId(200L)
                .mahkemeAidiyatKoduEkle("AIDIYAT-EKLE")
                .mahkemeAidiyatKoduCikar(null)
                .tarih(testDate)
                .durum("AKTIF")
                .mahkemeKararDetayTalepId(300L)
                .build();

        mahkemeAidiyatDetayTalepDTO = MahkemeAidiyatDetayTalepDTO.builder()
                .id(1L)
                .iliskiliMahkemeKararId(100L)
                .mahkemeKararId(200L)
                .mahkemeAidiyatKoduEkle("AIDIYAT-EKLE")
                .mahkemeAidiyatKoduCikar(null)
                .tarih(testDate)
                .durum("AKTIF")
                .mahkemeKararDetayId(300L)
                .build();
    }

    @Test
    void toDto_shouldMapEntityToDto() {
        // When
        MahkemeAidiyatDetayTalepDTO result = mahkemeAidiyatDetayTalepMapper.toDto(mahkemeAidiyatDetayTalep);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(mahkemeAidiyatDetayTalep.getId());
        assertThat(result.getIliskiliMahkemeKararId()).isEqualTo(mahkemeAidiyatDetayTalep.getIliskiliMahkemeKararId());
        assertThat(result.getMahkemeKararId()).isEqualTo(mahkemeAidiyatDetayTalep.getMahkemeKararTalepId());
        assertThat(result.getMahkemeAidiyatKoduEkle()).isEqualTo(mahkemeAidiyatDetayTalep.getMahkemeAidiyatKoduEkle());
        assertThat(result.getMahkemeAidiyatKoduCikar()).isEqualTo(mahkemeAidiyatDetayTalep.getMahkemeAidiyatKoduCikar());
        assertThat(result.getTarih()).isEqualTo(mahkemeAidiyatDetayTalep.getTarih());
        assertThat(result.getDurum()).isEqualTo(mahkemeAidiyatDetayTalep.getDurum());
        assertThat(result.getMahkemeKararDetayId()).isEqualTo(mahkemeAidiyatDetayTalep.getMahkemeKararDetayTalepId());
    }

    @Test
    void toEntity_shouldMapDtoToEntity() {
        // When
        MahkemeAidiyatDetayTalep result = mahkemeAidiyatDetayTalepMapper.toEntity(mahkemeAidiyatDetayTalepDTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(mahkemeAidiyatDetayTalepDTO.getId());
        assertThat(result.getIliskiliMahkemeKararId()).isEqualTo(mahkemeAidiyatDetayTalepDTO.getIliskiliMahkemeKararId());
        assertThat(result.getMahkemeKararTalepId()).isEqualTo(mahkemeAidiyatDetayTalepDTO.getMahkemeKararId());
        assertThat(result.getMahkemeAidiyatKoduEkle()).isEqualTo(mahkemeAidiyatDetayTalepDTO.getMahkemeAidiyatKoduEkle());
        assertThat(result.getMahkemeAidiyatKoduCikar()).isEqualTo(mahkemeAidiyatDetayTalepDTO.getMahkemeAidiyatKoduCikar());
        assertThat(result.getTarih()).isEqualTo(mahkemeAidiyatDetayTalepDTO.getTarih());
        assertThat(result.getDurum()).isEqualTo(mahkemeAidiyatDetayTalepDTO.getDurum());
        assertThat(result.getMahkemeKararDetayTalepId()).isEqualTo(mahkemeAidiyatDetayTalepDTO.getMahkemeKararDetayId());
    }

    @Test
    void updateEntityFromDto_shouldUpdateEntityWithDtoValues() {
        // Given
        MahkemeAidiyatDetayTalep existingEntity = MahkemeAidiyatDetayTalep.builder()
                .id(1L)
                .iliskiliMahkemeKararId(100L)
                .mahkemeKararTalepId(200L)
                .mahkemeAidiyatKoduEkle("AIDIYAT-EKLE")
                .mahkemeAidiyatKoduCikar(null)
                .tarih(testDate)
                .durum("AKTIF")
                .mahkemeKararDetayTalepId(300L)
                .build();

        Date newDate = new Date(testDate.getTime() + 86400000); // Add one day
        MahkemeAidiyatDetayTalepDTO updatedDto = MahkemeAidiyatDetayTalepDTO.builder()
                .id(1L)
                .iliskiliMahkemeKararId(101L)
                .mahkemeKararId(201L)
                .mahkemeAidiyatKoduEkle(null)
                .mahkemeAidiyatKoduCikar("AIDIYAT-CIKAR")
                .tarih(newDate)
                .durum("PASIF")
                .mahkemeKararDetayId(301L)
                .build();

        // When
        MahkemeAidiyatDetayTalep result = mahkemeAidiyatDetayTalepMapper.updateEntityFromDto(existingEntity, updatedDto);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(existingEntity.getId());
        assertThat(result.getIliskiliMahkemeKararId()).isEqualTo(updatedDto.getIliskiliMahkemeKararId());
        assertThat(result.getMahkemeKararTalepId()).isEqualTo(updatedDto.getMahkemeKararId());
        assertThat(result.getMahkemeAidiyatKoduEkle()).isEqualTo(updatedDto.getMahkemeAidiyatKoduEkle());
        assertThat(result.getMahkemeAidiyatKoduCikar()).isEqualTo(updatedDto.getMahkemeAidiyatKoduCikar());
        assertThat(result.getTarih()).isEqualTo(updatedDto.getTarih());
        assertThat(result.getDurum()).isEqualTo(updatedDto.getDurum());
        assertThat(result.getMahkemeKararDetayTalepId()).isEqualTo(updatedDto.getMahkemeKararDetayId());
    }

    @Test
    void toDtoList_shouldMapEntityListToDtoList() {
        // Given
        List<MahkemeAidiyatDetayTalep> entityList = Arrays.asList(
                mahkemeAidiyatDetayTalep,
                MahkemeAidiyatDetayTalep.builder()
                        .id(2L)
                        .iliskiliMahkemeKararId(102L)
                        .mahkemeKararTalepId(202L)
                        .mahkemeAidiyatKoduEkle(null)
                        .mahkemeAidiyatKoduCikar("AIDIYAT-CIKAR")
                        .tarih(testDate)
                        .durum("PASIF")
                        .mahkemeKararDetayTalepId(302L)
                        .build()
        );

        // When
        List<MahkemeAidiyatDetayTalepDTO> result = mahkemeAidiyatDetayTalepMapper.toDtoList(entityList);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(2);
        assertThat(result.get(0).getId()).isEqualTo(entityList.get(0).getId());
        assertThat(result.get(1).getId()).isEqualTo(entityList.get(1).getId());
        assertThat(result.get(0).getMahkemeAidiyatKoduEkle()).isEqualTo(entityList.get(0).getMahkemeAidiyatKoduEkle());
        assertThat(result.get(1).getMahkemeAidiyatKoduCikar()).isEqualTo(entityList.get(1).getMahkemeAidiyatKoduCikar());
    }

    @Test
    void toDto_shouldReturnNullWhenEntityIsNull() {
        // When
        MahkemeAidiyatDetayTalepDTO result = mahkemeAidiyatDetayTalepMapper.toDto(null);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void toEntity_shouldReturnNullWhenDtoIsNull() {
        // When
        MahkemeAidiyatDetayTalep result = mahkemeAidiyatDetayTalepMapper.toEntity(null);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void updateEntityFromDto_shouldReturnEntityWhenDtoIsNull() {
        // When
        MahkemeAidiyatDetayTalep result = mahkemeAidiyatDetayTalepMapper.updateEntityFromDto(mahkemeAidiyatDetayTalep, null);

        // Then
        assertThat(result).isEqualTo(mahkemeAidiyatDetayTalep);
    }

    @Test
    void updateEntityFromDto_shouldReturnEntityWhenEntityIsNull() {
        // When
        MahkemeAidiyatDetayTalep result = mahkemeAidiyatDetayTalepMapper.updateEntityFromDto(null, mahkemeAidiyatDetayTalepDTO);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void toDtoList_shouldReturnEmptyListWhenEntityListIsNull() {
        // When
        List<MahkemeAidiyatDetayTalepDTO> result = mahkemeAidiyatDetayTalepMapper.toDtoList(null);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEmpty();
    }
}
