package iym.db.jpa.service.impl;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.CanakNumaralar;
import iym.common.service.db.DbCanakNumaralarService;
import iym.db.jpa.dao.CanakNumaralarRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Service implementation for CanakNumaralar entity
 */
@Service
public class DbCanakNumaralarServiceImpl extends GenericDbServiceImpl<CanakNumaralar, Long> implements DbCanakNumaralarService {

    private final CanakNumaralarRepo canakNumaralarRepo;

    @Autowired
    public DbCanakNumaralarServiceImpl(CanakNumaralarRepo repository) {
        super(repository);
        this.canakNumaralarRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<CanakNumaralar> findByCanakNo(String canakNo) {
        return canakNumaralarRepo.findByCanakNo(canakNo);
    }

    @Override
    @Transactional(readOnly = true)
    public List<CanakNumaralar> findByCanakNoContainingIgnoreCase(String canakNo) {
        return canakNumaralarRepo.findByCanakNoContainingIgnoreCase(canakNo);
    }

    @Override
    @Transactional(readOnly = true)
    public List<CanakNumaralar> findByKurumKod(String kurumKod) {
        return canakNumaralarRepo.findByKurumKod(kurumKod);
    }

    @Override
    @Transactional(readOnly = true)
    public List<CanakNumaralar> findByEkleyenId(Long ekleyenId) {
        return canakNumaralarRepo.findByEkleyenId(ekleyenId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<CanakNumaralar> findByKutu(Long kutu) {
        return canakNumaralarRepo.findByKutu(kutu);
    }

    @Override
    @Transactional(readOnly = true)
    public List<CanakNumaralar> findByEklemeTarihBetween(Date startDate, Date endDate) {
        return canakNumaralarRepo.findByEklemeTarihBetween(startDate, endDate);
    }

    @Override
    @Transactional(readOnly = true)
    public List<CanakNumaralar> findByAciklamaContainingIgnoreCase(String aciklama) {
        return canakNumaralarRepo.findByAciklamaContainingIgnoreCase(aciklama);
    }

    @Override
    @Transactional(readOnly = true)
    public List<CanakNumaralar> findByKurumKodAndKutu(String kurumKod, Long kutu) {
        return canakNumaralarRepo.findByKurumKodAndKutu(kurumKod, kutu);
    }

    @Override
    @Transactional(readOnly = true)
    public List<CanakNumaralar> findByCanakNoStartingWith(String prefix) {
        return canakNumaralarRepo.findByCanakNoStartingWith(prefix);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByCanakNo(String canakNo) {
        return canakNumaralarRepo.existsByCanakNo(canakNo);
    }
}
