package iym.db.postgresql.dao;

import iym.common.model.entity.postgresql.Ulkeler;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for PostgreSQL Ulkeler entity
 * This repository is specifically configured for PostgreSQL database operations
 * 
 * Database connection: localhost:5432
 * Database: demo_db
 * User: demo_user
 * Password: demo_password
 */
@Repository
public interface UlkelerPostgreSQLRepo extends JpaRepository<Ulkeler, Long> {

    /**
     * Find by country code
     * @param code the country code to search for
     * @return Optional containing the country if found
     */
    Optional<Ulkeler> findByCode(String code);

    /**
     * Find by country name
     * @param name the country name to search for
     * @return Optional containing the country if found
     */
    Optional<Ulkeler> findByName(String name);

    /**
     * Find by country name containing (case insensitive)
     * @param name the country name to search for
     * @return List of countries with name containing the search term
     */
    List<Ulkeler> findByNameContainingIgnoreCase(String name);

    /**
     * Find by country code containing (case insensitive)
     * @param code the country code to search for
     * @return List of countries with code containing the search term
     */
    List<Ulkeler> findByCodeContainingIgnoreCase(String code);

    /**
     * Find all active countries (not deleted)
     * @return List of active countries
     */
    @Query("SELECT u FROM UlkelerPostgreSQL u WHERE u.isDeleted = false OR u.isDeleted IS NULL")
    List<Ulkeler> findAllActive();

    /**
     * Find all deleted countries
     * @return List of deleted countries
     */
    @Query("SELECT u FROM UlkelerPostgreSQL u WHERE u.isDeleted = true")
    List<Ulkeler> findAllDeleted();

    /**
     * Find active countries by name containing
     * @param name the name to search for
     * @return List of active countries with name containing the search term
     */
    @Query("SELECT u FROM UlkelerPostgreSQL u WHERE (u.isDeleted = false OR u.isDeleted IS NULL) AND LOWER(u.name) LIKE LOWER(CONCAT('%', :name, '%'))")
    List<Ulkeler> findActiveByNameContaining(@Param("name") String name);

    /**
     * Find active countries by code containing
     * @param code the code to search for
     * @return List of active countries with code containing the search term
     */
    @Query("SELECT u FROM UlkelerPostgreSQL u WHERE (u.isDeleted = false OR u.isDeleted IS NULL) AND LOWER(u.code) LIKE LOWER(CONCAT('%', :code, '%'))")
    List<Ulkeler> findActiveByCodeContaining(@Param("code") String code);

    /**
     * Find all countries ordered by name
     * @return List of all countries ordered by name
     */
    List<Ulkeler> findAllByOrderByNameAsc();

    /**
     * Find all active countries ordered by name
     * @return List of active countries ordered by name
     */
    @Query("SELECT u FROM UlkelerPostgreSQL u WHERE (u.isDeleted = false OR u.isDeleted IS NULL) ORDER BY u.name ASC")
    List<Ulkeler> findAllActiveOrderByNameAsc();

    /**
     * Find all countries ordered by code
     * @return List of all countries ordered by code
     */
    List<Ulkeler> findAllByOrderByCodeAsc();

    /**
     * Check if a country exists with the given code
     * @param code the country code to check
     * @return true if country exists, false otherwise
     */
    boolean existsByCode(String code);

    /**
     * Check if a country exists with the given name
     * @param name the country name to check
     * @return true if country exists, false otherwise
     */
    boolean existsByName(String name);

    /**
     * Check if an active country exists with the given code
     * @param code the country code to check
     * @return true if active country exists, false otherwise
     */
    @Query("SELECT COUNT(u) > 0 FROM UlkelerPostgreSQL u WHERE u.code = :code AND (u.isDeleted = false OR u.isDeleted IS NULL)")
    boolean existsActiveByCode(@Param("code") String code);

    /**
     * Check if an active country exists with the given name
     * @param name the country name to check
     * @return true if active country exists, false otherwise
     */
    @Query("SELECT COUNT(u) > 0 FROM UlkelerPostgreSQL u WHERE u.name = :name AND (u.isDeleted = false OR u.isDeleted IS NULL)")
    boolean existsActiveByName(@Param("name") String name);
}
