package iym.common.model.enums;

public enum IymUserRoleType {

    ROLE_ADMIN {
        @Override
        public boolean canChangeOthersPassword() {
            return true;
        }
    },
    ROLE_QUERY_ADMIN {
    },
    ROLE_ADLI_ADMIN {
        @Override
        public boolean canChangeOthersPassword() {
            return true;
        }
    },
    ROLE_ONLEYICI_ADMIN {
        @Override
        public boolean canChangeOthersPassword() {
            return true;
        }
    },
    ROLE_ADLI {

    },
    ROLE_ONLEYICI {

    },

    ROLE_MAKOS_QUERY_API_CLIENT {
        @Override
        public boolean canLogin() {
            return false;
        }
    };

    public boolean canLogin(){
        return true;
    }

    public boolean canUseImpersonateLogin(){
        return false;
    }

    public boolean canChangeOthersPassword(){
        return false;
    }

}
