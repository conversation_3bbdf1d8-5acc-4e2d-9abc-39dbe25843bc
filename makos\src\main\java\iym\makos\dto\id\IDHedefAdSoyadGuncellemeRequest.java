package iym.makos.dto.id;

import io.swagger.v3.oas.annotations.media.Schema;
import iym.common.model.api.HedefWithAdSoyad;
import iym.common.model.api.KararTuru;
import iym.common.model.api.MahkemeKararTip;
import iym.common.validation.ValidationResult;
import iym.makos.model.api.HedefAdSoyadGuncellemeKararDetay;
import iym.makos.model.api.MahkemeKararDetay;
import iym.makos.model.reqrep.MahkemeKararRequest;
import iym.makos.validator.MakosRequestValid;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Jacksonized
@Data
@NoArgsConstructor
@SuperBuilder
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@MakosRequestValid
@Slf4j
public class IDHedefAdSoyadGuncellemeRequest extends MahkemeKararRequest {

    @NotNull
    @Valid
    @Size(min = 1)
    @Schema(description = "Güncelleme yapılacak hedefler için mahkeme karar bilgisi ve karara ait güncellenecek ad, soyad bilgileri")
    private List<HedefAdSoyadGuncellemeKararDetay> hedefAdSoyadGuncellemeKararDetayListesi;

    @Override
    public ValidationResult isValid() {
        log.trace("Checking if HedefAdSoyadGuncellemeRequest is valid");

        try {
            ValidationResult validationResult = new ValidationResult(true);

            if (kararTuru != KararTuru.ILETISIMIN_DENETLENMESI_HEDEF_ADSOYAD_GUNCELLEME) {
                validationResult.addFailedReason("Karar türü: " + KararTuru.ILETISIMIN_DENETLENMESI_HEDEF_ADSOYAD_GUNCELLEME.name() + " olmalıdır");
                return validationResult;
            }

            MahkemeKararTip kararTip = mahkemeKararBilgisi.getMahkemeKararTipi();
            if (kararTip != MahkemeKararTip.HEDEF_AD_SOYAD_DEGISTIRME) {
                validationResult.addFailedReason("Mahkeme karar Tipi " + MahkemeKararTip.HEDEF_AD_SOYAD_DEGISTIRME.name() + " olmalıdır");
            }

            if (hedefAdSoyadGuncellemeKararDetayListesi == null || hedefAdSoyadGuncellemeKararDetayListesi.isEmpty()) {
                validationResult.addFailedReason("Güncellemeye konu olan en az bir detay girilmelidir!");
            } else {
                for (HedefAdSoyadGuncellemeKararDetay hedefGuncellemeKararDetay : hedefAdSoyadGuncellemeKararDetayListesi) {

                    MahkemeKararDetay iliskiliMahkemeKararDetay = hedefGuncellemeKararDetay.getMahkemeKararDetay();
                    if (iliskiliMahkemeKararDetay == null) {
                        validationResult.addFailedReason("Güncellemeye konu mahkeme karar bilgileri boş olamaz.!");
                    }
                    List<HedefWithAdSoyad> hedefListesi = hedefGuncellemeKararDetay.getHedefAdSoyadListesi();
                    if (hedefListesi == null || hedefListesi.isEmpty()) {
                        validationResult.addFailedReason("Güncelleme yapılacak hedef listesi boş olamaz!");
                    }
                }
            }

            return validationResult;
        } catch (Exception e) {
            log.error("Validation failed", e);
            return new ValidationResult("Validation failed. Internal error");
        }

    }

    @Override
    protected void assignKararTuru() {
        this.kararTuru = KararTuru.ILETISIMIN_DENETLENMESI_HEDEF_ADSOYAD_GUNCELLEME;
    }

}

