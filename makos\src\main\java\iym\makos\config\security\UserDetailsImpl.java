package iym.makos.config.security;

import com.fasterxml.jackson.annotation.JsonIgnore;
import iym.common.model.api.KullaniciKurum;
import iym.common.model.entity.makos.MakosUser;
import iym.common.model.enums.MakosUserRoleType;
import lombok.Getter;
import lombok.Setter;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.*;

public class UserDetailsImpl implements UserDetails {

    public static final String DEFAULT_ROLE_PREFIX = "ROLE_";

    @Getter
    private final Long userId;
    private final String username;

    @JsonIgnore
    private final String password;

    @Getter
    @Setter
    private String actingUserName;

    @Getter
    private MakosUserRoleType userRole;

    @Getter
    private final KullaniciKurum kullaniciKurum;

    private final Set<GrantedAuthority> grantedAuthorities = new HashSet<>();

    @Getter
    private final List<String> requestedTargetIdList = new ArrayList<>();


    public UserDetailsImpl(MakosUser makosUser) {
        this.userId = makosUser.getId();
        this.username = makosUser.getUsername();
        this.password = makosUser.getPassword();
        this.userRole = makosUser.getRole();
        this.kullaniciKurum = makosUser.getKurum();
        this.grantedAuthorities.add(new SimpleGrantedAuthority(makosUser.getRole().name()));
    }

    public UserDetailsImpl(Long userId, String username, String password, MakosUserRoleType userRole, KullaniciKurum kullaniciKurum) {
        this.userId = userId;
        this.username = username;
        this.password = password;
        this.userRole = userRole;
        this.kullaniciKurum = kullaniciKurum;
        this.grantedAuthorities.add(new SimpleGrantedAuthority(userRole.name()));
    }

    public void updateUserRole(MakosUserRoleType userRole) {
        // remove existing role
        this.grantedAuthorities.clear();

        // add new role
        this.grantedAuthorities.add(new SimpleGrantedAuthority(userRole.name()));
        this.userRole = userRole;
    }

    public Long getId() {
        return userId;
    }

    /**
     * @return 'Roles'
     * use hasAuthority() to check roles
     */
    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return grantedAuthorities;
    }

    @Override
    public String getPassword() {
        return password;
    }

    @Override
    public String getUsername() {
        if (actingUserName != null && !actingUserName.isEmpty())
            return username + "-" + actingUserName;

        return username;
    }

    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    public boolean isEnabled() {
        return true;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UserDetailsImpl that = (UserDetailsImpl) o;
        return Objects.equals(userId, that.userId) && Objects.equals(username, that.username);
    }

    @Override
    public int hashCode() {
        return Objects.hash(userId, getUsername());
    }

    public boolean hasRole(MakosUserRoleType userRole) {
        if (userRole == null) {
            return false;
        }

        for (GrantedAuthority grantedAuthority : grantedAuthorities) {
            if (grantedAuthority.getAuthority().equals(userRole.name())) {
                return true;
            }
        }

        return false;
    }

    public boolean hasAuthority(String authority) {
        if (authority == null || authority.trim().isEmpty()) {
            return false;
        }

        for (GrantedAuthority grantedAuthority : grantedAuthorities) {
            if (grantedAuthority.getAuthority().equals(authority)) {
                return true;
            }
        }

        return false;
    }

}
