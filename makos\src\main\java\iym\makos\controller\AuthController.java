package iym.makos.controller;

import iym.common.model.api.ApiResponse;
import iym.common.model.entity.makos.MakosUser;
import iym.common.model.enums.MakosUserRoleType;
import iym.common.model.enums.ResponseCode;
import iym.common.model.enums.UserStatusType;
import iym.common.service.db.DbMakosUserService;

import iym.makos.config.security.UserDetailsImpl;
import iym.makos.dto.auth.ChangePasswordRequest;
import iym.makos.dto.auth.LoginRequest;
import iym.makos.dto.auth.LoginResponse;
import iym.makos.dto.auth.RegisterRequest;
import iym.makos.dto.auth.RegisterResponse;
import iym.makos.dto.auth.ChangePasswordResponse;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.HashSet;
import java.util.List;

/**
 * Authentication Controller for MAKOS Module
 * Handles login, register and basic authentication operations
 */
@RestController
@RequestMapping("/auth")
@Slf4j
public class AuthController {

    @Autowired
    AuthenticationManager authenticationManager;

    @Autowired
    PasswordEncoder passwordEncoder;

    @Autowired
    private DbMakosUserService userService;

    /**
     * User login endpoint
     */
    @PermitAll
    @PostMapping("/login")
    public ResponseEntity<LoginResponse> login(@Valid @RequestBody LoginRequest request) {
        try {
            log.info("Got login request for user:{}", request.getUsername());
            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(request.getUsername(), request.getPassword()));

            if (authentication.isAuthenticated()) {
                UserDetailsImpl userDetailsImpl = (UserDetailsImpl) authentication.getPrincipal();

                if (!userDetailsImpl.getUserRole().canLogin()) {
                    log.error("User can't login! Invalid role:{}, requester:{}", 
                            userDetailsImpl.getUserRole(), request.getUsername());
                    return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                            .body(LoginResponse.builder()
                                    .response(ApiResponse.builder()
                                            .responseCode(ResponseCode.FAILED)
                                            .responseMessage("Login Not Allowed for User")
                                            .build())
                                    .build());
                }

                LoginResponse loginResponse = createLoginResponse(userDetailsImpl);
                log.info("User logging in {}", userDetailsImpl);
                return ResponseEntity.ok(loginResponse);
            } else {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(LoginResponse.builder()
                                .response(ApiResponse.builder()
                                        .responseCode(ResponseCode.FAILED)
                                        .responseMessage("Invalid Credentials")
                                        .build())
                                .build());
            }
        } catch (DisabledException ex) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(LoginResponse.builder()
                            .response(ApiResponse.builder()
                                    .responseCode(ResponseCode.FAILED)
                                    .responseMessage("User is disabled")
                                    .build())
                            .build());
        } catch (BadCredentialsException ex) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(LoginResponse.builder()
                            .response(ApiResponse.builder()
                                    .responseCode(ResponseCode.FAILED)
                                    .responseMessage("Invalid Credentials")
                                    .build())
                            .build());
        } catch (Exception ex) {
            log.error("Login failed for user:{}", request.getUsername(), ex);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(LoginResponse.builder()
                            .response(ApiResponse.builder()
                                    .responseCode(ResponseCode.FAILED)
                                    .responseMessage("Login failed (Internal Error)")
                                    .build())
                            .build());
        }
    }

    /**
     * User registration endpoint
     */
    @PermitAll
    @PostMapping("/register")
    public ResponseEntity<RegisterResponse> register(@Valid @RequestBody RegisterRequest request) {
        if (userService.findByUsername(request.getUserName()).isPresent()) {
            return ResponseEntity.badRequest()
                    .body(RegisterResponse.builder()
                            .response(ApiResponse.builder()
                                    .responseCode(ResponseCode.FAILED)
                                    .responseMessage("User already exist")
                                    .build())
                            .build());
        }

        MakosUser user = MakosUser.builder()
                .username(request.getUserName())
                .password(passwordEncoder.encode(request.getPassword()))
                .status(UserStatusType.ACTIVE)
                .role(request.getRole() != null ? request.getRole() : MakosUserRoleType.ROLE_ADMIN)
                .kurum(request.getKurum())
                .build();

        userService.save(user);

        try {
            Authentication authenticate = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(
                            request.getUserName(),
                            request.getPassword())
            );

            if (authenticate.isAuthenticated()) {
                SecurityContextHolder.getContext().setAuthentication(authenticate);
            }
        } catch (AuthenticationException e) {
            log.error(e.getMessage(), e);
        }

        return ResponseEntity.ok(RegisterResponse.builder()
                .response(ApiResponse.builder()
                        .responseCode(ResponseCode.SUCCESS)
                        .responseMessage("User registered successfully")
                        .build())
                .build());
    }

    /**
     * Change password endpoint
     */
    @PostMapping("/changePassword")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ChangePasswordResponse> changePassword(@Valid @RequestBody ChangePasswordRequest request, Authentication auth) {
        try {
            @SuppressWarnings("unchecked")
            Collection<SimpleGrantedAuthority> authorities = (Collection<SimpleGrantedAuthority>) auth.getAuthorities();
            String secureUserRole = authorities.iterator().next().getAuthority();
            
            // Get current user
            String username = auth.getName();
            MakosUser user = userService.findByUsername(username)
                    .orElseThrow(() -> new RuntimeException("User not found"));
            
            // Validate current password
            if (!passwordEncoder.matches(request.getCurrentPassword(), user.getPassword())) {
                return ResponseEntity.badRequest()
                        .body(ChangePasswordResponse.builder()
                                .response(ApiResponse.builder()
                                        .responseCode(ResponseCode.FAILED)
                                        .responseMessage("Current password is incorrect")
                                        .build())
                                .build());
            }
            
            // Validate new password confirmation
            if (!request.getNewPassword().equals(request.getConfirmPassword())) {
                return ResponseEntity.badRequest()
                        .body(ChangePasswordResponse.builder()
                                .response(ApiResponse.builder()
                                        .responseCode(ResponseCode.FAILED)
                                        .responseMessage("New password and confirmation do not match")
                                        .build())
                                .build());
            }
            
            // Update password
            user.setPassword(passwordEncoder.encode(request.getNewPassword()));
            userService.save(user);
            
            log.info("Password change requested by user: {}, role: {}", username, secureUserRole);
            
            return ResponseEntity.ok(ChangePasswordResponse.builder()
                    .response(ApiResponse.builder()
                            .responseCode(ResponseCode.SUCCESS)
                            .responseMessage("Password changed successfully")
                            .build())
                    .build());
        } catch (Exception ex) {
            log.error("Password change failed for user: {}", auth.getName(), ex);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ChangePasswordResponse.builder()
                            .response(ApiResponse.builder()
                                    .responseCode(ResponseCode.FAILED)
                                    .responseMessage("Password change failed")
                                    .build())
                            .build());
        }
    }

    /**
     * Create login response for basic authentication (makos uses basic auth, not JWT)
     */
    private LoginResponse createLoginResponse(UserDetailsImpl userDetails) {
        return LoginResponse.builder()
                .response(ApiResponse.builder()
                        .responseCode(ResponseCode.SUCCESS)
                        .responseMessage("Login successful")
                        .build())
                .token(null) // No token needed for basic auth
                .userId(userDetails.getId())
                .username(userDetails.getUsername())
                .actingUserName(userDetails.getActingUserName())
                .roles(new HashSet<>(List.of(userDetails.getUserRole().name())))
                .kurum(userDetails.getKullaniciKurum())
                .build();
    }
}
