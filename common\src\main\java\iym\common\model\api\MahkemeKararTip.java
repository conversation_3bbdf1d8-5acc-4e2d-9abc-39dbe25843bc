package iym.common.model.api;


import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum MahkemeKararTip {

    // TODO update and finalize below values

    ONLEYICI_HAKIM_KARARI(100),
    SINYAL_BILGI_DEGERLENDIRME_KARARI(150),
    ABONE_KUTUK_BILGILERI_KARARI(151),
    ONLEYICI_YAZILI_EMIR(200),
    ADLI_HAKIM_KARARI(300),
    ADLI_HAKIM_HTS_KARARI(350),
    ADLI_YAZILI_EMIR(400),
    ADLI_KHK_YAZILI_EMIR(410),
    ADLI_SAVCILIK_HTS_KARARI(450),
    HEDEF_AD_SOYAD_DEGISTIRME(510),
    MAHKEME_KODU_DEGISTIRME(520),
    MAHKEME_AIDIYAT_DEGISTIRME(530),
    ONLEYICI_SONLANDIRMA(600),
    ADLI_SONLANDIRMA(700),
    ADLI_SAVCILIK_SONLANDIRMA(710),
    ADLI_SAVCILIK_YER_TESPITI_SONLANDIRMA(720),
    ADLI_KHK_SONLANDIRMA(730),
    ADLI_ASKERI_HAKIM_KARARI(800),
    ADLI_ASKERI_SONLANDIRMA(900),
    ADLI_ASKERI_SAVCILIK_SONLANDIRMA(910),
    CANAK_NUMARA_DEGISTIRME(599),
    ADLI_ASKERI_YER_TESPITI_SONLANDIRMA(920);

    private final int kararKodu;

    MahkemeKararTip(int kararKodu) {
        this.kararKodu = kararKodu;
    }

    @JsonValue
    public int getKararKodu() {
        return kararKodu;
    }

    @JsonCreator
    public static MahkemeKararTip fromName(String name) {
        for (MahkemeKararTip mahkemeKararTip : MahkemeKararTip.values()) {
            if (mahkemeKararTip.name().equals(name)) {
                return mahkemeKararTip;
            }
        }
        throw new IllegalArgumentException("Gecersiz kararTipi: '" + name + "'");
    }

    //@JsonCreator
    public static MahkemeKararTip fromValue(int value) {
        for (MahkemeKararTip mahkemeKararTip : MahkemeKararTip.values()) {
            if (mahkemeKararTip.kararKodu == value) {
                return mahkemeKararTip;
            }
        }
        throw new IllegalArgumentException("Gecersiz kararTipi kodu: '" + value + "'");
    }
}
