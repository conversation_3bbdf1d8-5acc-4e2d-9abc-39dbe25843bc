-- H2 Test Database Schema for Makos Integration Tests
-- This schema is compatible with H2 database and avoids Oracle-specific syntax

-- Users table (simplified for tests)
CREATE TABLE IF NOT EXISTS kullanicilar (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    adi VARCHAR(20) NOT NULL,
    akademik_unvan VARCHAR(10),
    birimi VARCHAR(4),
    cid VARCHAR(32),
    durumu VARCHAR(20),
    ekstraguvenlik VARCHAR(1),
    fax VARCHAR(55),
    gorev_tanimi VARCHAR(300),
    gorevi VARCHAR(6),
    grup_kodu VARCHAR(10),
    imza_dosyasi VARCHAR(50),
    imza_yetkisi CHAR(1),
    kim_kul_id BIGINT,
    kullanici_adi VARCHAR(20) NOT NULL,
    parola_degisim_tarihi DATE,
    posta VARCHAR(55),
    resmi_kod VARCHAR(12),
    sifre CHAR(32) NOT NULL,  -- H2 compatible, no BYTE specification
    soyadi VARCHAR(40) NOT NULL,
    tck VARCHAR(11),
    tel VARCHAR(16),
    temsil_edilen_kurum VARCHAR(2),
    yetki BIGINT
);

-- Insert test data
INSERT INTO kullanicilar (adi, kullanici_adi, sifre, soyadi) VALUES 
('Test', 'testuser', 'testpassword123456789012345678', 'User');
