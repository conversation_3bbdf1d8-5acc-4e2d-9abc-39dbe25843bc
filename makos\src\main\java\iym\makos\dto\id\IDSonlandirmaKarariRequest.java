package iym.makos.dto.id;

import iym.common.model.api.KararTuru;
import iym.common.model.api.MahkemeKararTip;
import iym.common.util.CommonUtils;
import iym.common.validation.ValidationResult;
import iym.makos.model.api.HedefDetayID;
import iym.makos.model.reqrep.MahkemeKararRequest;
import iym.makos.validator.MakosRequestValid;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Jacksonized
@Data
@NoArgsConstructor
@SuperBuilder
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@MakosRequestValid
@Slf4j
public class IDSonlandirmaKarariRequest extends MahkemeKararRequest {

    @NotNull
    @Size(min = 1)
    @Valid
    private List<HedefDetayID> hedefDetayListesi;

    private List<String> mahkemeAidiyatKodlari;

    private List<String> mahkemeSucTipiKodlari;

    @Override
    public ValidationResult isValid() {
        log.trace("Checking if IDKararRequest is valid");

        try {
            ValidationResult validationResult = new ValidationResult(true);

            MahkemeKararTip mahkemeKararTipi = mahkemeKararBilgisi.getMahkemeKararTipi();

            if (kararTuru != KararTuru.ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI) {
                validationResult.addFailedReason("Karar türü: " + KararTuru.ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI.name() + " olmalıdır");
                return validationResult;
            }

            boolean sonlandirmaMahkemeKararTipinde = CommonUtils.sonlandirmaMahkemeKararTipi(mahkemeKararTipi);
            if (!sonlandirmaMahkemeKararTipinde) {
                validationResult.addFailedReason("Mahkeme karar tipi, sonlandırma kararı için uygun değildir!");
            }

            for (HedefDetayID hedefDetayID : hedefDetayListesi) {
                if (hedefDetayID.getIlgiliMahkemeKararDetayi() == null) {
                    validationResult.addFailedReason("Sonlandirma Kararinda ilgili mahkeme karari bos olamaz!");
                }

                if (!CommonUtils.isNullOrEmpty(hedefDetayID.getCanakNo())) {
                    validationResult.addFailedReason("Sonlandirma kararında CANAK numarası girilemez.");
                }

                if (hedefDetayID.getUzatmaSayisi() != null) {
                    validationResult.addFailedReason("Sonlandirma Kararinda uzatma sayisi dolu olamaz!");
                }
            }

            return validationResult;
        } catch (Exception e) {
            log.error("Validation failed", e);
            return new ValidationResult("Validation failed. Internal error");
        }

    }

    @Override
    protected void assignKararTuru() {
        this.kararTuru = KararTuru.ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI;
    }

}

