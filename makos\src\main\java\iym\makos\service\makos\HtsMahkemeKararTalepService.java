package iym.makos.service.makos;

import iym.common.model.entity.iym.EvrakKayit;
import iym.common.model.entity.iym.HtsMahkemeKararTalep;
import iym.common.service.db.DbEvrakKayitService;
import iym.common.service.db.DbHtsMahkemeKararTalepService;
import iym.makos.dto.HtsMahkemeKararTalepDTO;
import iym.makos.mapper.HtsMahkemeKararTalepMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Service for HtsMahkemeKararTalep operations
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class HtsMahkemeKararTalepService {

    private final DbHtsMahkemeKararTalepService dbHtsMahkemeKararTalepService;
    private final DbEvrakKayitService dbEvrakKayitService;
    private final HtsMahkemeKararTalepMapper htsMahkemeKararTalepMapper;

    /**
     * Get all HTS mahkeme karar talep records
     * @return List of HtsMahkemeKararTalepDTO
     */
    @Transactional(readOnly = true)
    public List<HtsMahkemeKararTalepDTO> findAll() {
        List<HtsMahkemeKararTalep> htsMahkemeKararTalepList = dbHtsMahkemeKararTalepService.findAll();
        return htsMahkemeKararTalepMapper.toDtoList(htsMahkemeKararTalepList);
    }

    /**
     * Get all HTS mahkeme karar talep records with pagination
     * @param pageable Pagination information
     * @return Page of HtsMahkemeKararTalepDTO
     */
    @Transactional(readOnly = true)
    public Page<HtsMahkemeKararTalepDTO> findAll(Pageable pageable) {
        Page<HtsMahkemeKararTalep> htsMahkemeKararTalepPage = dbHtsMahkemeKararTalepService.findAll(pageable);
        List<HtsMahkemeKararTalepDTO> dtoList = htsMahkemeKararTalepMapper.toDtoList(htsMahkemeKararTalepPage.getContent());
        return new PageImpl<>(dtoList, pageable, htsMahkemeKararTalepPage.getTotalElements());
    }

    /**
     * Get HTS mahkeme karar talep by id
     * @param id HtsMahkemeKararTalep id
     * @return HtsMahkemeKararTalepDTO
     */
    @Transactional(readOnly = true)
    public HtsMahkemeKararTalepDTO findById(Long id) {
        HtsMahkemeKararTalep htsMahkemeKararTalep = dbHtsMahkemeKararTalepService.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "HTS mahkeme karar talep bulunamadı: " + id));
        return htsMahkemeKararTalepMapper.toDto(htsMahkemeKararTalep);
    }

    /**
     * Get HTS mahkeme karar talep by evrak id
     * @param evrakId Evrak id
     * @return List of HtsMahkemeKararTalepDTO
     */
    @Transactional(readOnly = true)
    public List<HtsMahkemeKararTalepDTO> findByEvrakId(Long evrakId) {
        List<HtsMahkemeKararTalep> htsMahkemeKararTalepList = dbHtsMahkemeKararTalepService.findByEvrakId(evrakId);
        return htsMahkemeKararTalepMapper.toDtoList(htsMahkemeKararTalepList);
    }

    /**
     * Get HTS mahkeme karar talep by kullanici id
     * @param kullaniciId Kullanici id
     * @return List of HtsMahkemeKararTalepDTO
     */
    @Transactional(readOnly = true)
    public List<HtsMahkemeKararTalepDTO> findByKullaniciId(Long kullaniciId) {
        List<HtsMahkemeKararTalep> htsMahkemeKararTalepList = dbHtsMahkemeKararTalepService.findByKullaniciId(kullaniciId);
        return htsMahkemeKararTalepMapper.toDtoList(htsMahkemeKararTalepList);
    }

    /**
     * Get HTS mahkeme karar talep by durum
     * @param durum Durum
     * @return List of HtsMahkemeKararTalepDTO
     */
    @Transactional(readOnly = true)
    public List<HtsMahkemeKararTalepDTO> findByDurum(String durum) {
        List<HtsMahkemeKararTalep> htsMahkemeKararTalepList = dbHtsMahkemeKararTalepService.findByDurum(durum);
        return htsMahkemeKararTalepMapper.toDtoList(htsMahkemeKararTalepList);
    }

    /**
     * Get HTS mahkeme karar talep by karar tip
     * @param kararTip Karar tip
     * @return List of HtsMahkemeKararTalepDTO
     */
    @Transactional(readOnly = true)
    public List<HtsMahkemeKararTalepDTO> findByKararTip(String kararTip) {
        List<HtsMahkemeKararTalep> htsMahkemeKararTalepList = dbHtsMahkemeKararTalepService.findByKararTip(kararTip);
        return htsMahkemeKararTalepMapper.toDtoList(htsMahkemeKararTalepList);
    }

    /**
     * Get HTS mahkeme karar talep by hukuk birim
     * @param hukukBirim Hukuk birim
     * @return List of HtsMahkemeKararTalepDTO
     */
    @Transactional(readOnly = true)
    public List<HtsMahkemeKararTalepDTO> findByHukukBirim(String hukukBirim) {
        List<HtsMahkemeKararTalep> htsMahkemeKararTalepList = dbHtsMahkemeKararTalepService.findByHukukBirim(hukukBirim);
        return htsMahkemeKararTalepMapper.toDtoList(htsMahkemeKararTalepList);
    }

    /**
     * Get HTS mahkeme karar talep by mahkeme ili
     * @param mahkemeIli Mahkeme ili
     * @return List of HtsMahkemeKararTalepDTO
     */
    @Transactional(readOnly = true)
    public List<HtsMahkemeKararTalepDTO> findByMahkemeIli(String mahkemeIli) {
        List<HtsMahkemeKararTalep> htsMahkemeKararTalepList = dbHtsMahkemeKararTalepService.findByMahkemeIli(mahkemeIli);
        return htsMahkemeKararTalepMapper.toDtoList(htsMahkemeKararTalepList);
    }

    /**
     * Get HTS mahkeme karar talep by mahkeme kodu
     * @param mahkemeKodu Mahkeme kodu
     * @return List of HtsMahkemeKararTalepDTO
     */
    @Transactional(readOnly = true)
    public List<HtsMahkemeKararTalepDTO> findByMahkemeKodu(String mahkemeKodu) {
        List<HtsMahkemeKararTalep> htsMahkemeKararTalepList = dbHtsMahkemeKararTalepService.findByMahkemeKodu(mahkemeKodu);
        return htsMahkemeKararTalepMapper.toDtoList(htsMahkemeKararTalepList);
    }

    /**
     * Search HTS mahkeme karar talep by mahkeme adi
     * @param mahkemeAdi Mahkeme adi
     * @return List of HtsMahkemeKararTalepDTO
     */
    @Transactional(readOnly = true)
    public List<HtsMahkemeKararTalepDTO> findByMahkemeAdiContainingIgnoreCase(String mahkemeAdi) {
        List<HtsMahkemeKararTalep> htsMahkemeKararTalepList = dbHtsMahkemeKararTalepService.findByMahkemeAdiContainingIgnoreCase(mahkemeAdi);
        return htsMahkemeKararTalepMapper.toDtoList(htsMahkemeKararTalepList);
    }

    /**
     * Get HTS mahkeme karar talep by mahkeme karar no
     * @param mahkemeKararNo Mahkeme karar no
     * @return HtsMahkemeKararTalepDTO
     */
    @Transactional(readOnly = true)
    public HtsMahkemeKararTalepDTO findByMahkemeKararNo(String mahkemeKararNo) {
        HtsMahkemeKararTalep htsMahkemeKararTalep = dbHtsMahkemeKararTalepService.findByMahkemeKararNo(mahkemeKararNo)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "HTS mahkeme karar talep bulunamadı: " + mahkemeKararNo));
        return htsMahkemeKararTalepMapper.toDto(htsMahkemeKararTalep);
    }

    /**
     * Get HTS mahkeme karar talep by sorusturma no
     * @param sorusturmaNo Sorusturma no
     * @return List of HtsMahkemeKararTalepDTO
     */
    @Transactional(readOnly = true)
    public List<HtsMahkemeKararTalepDTO> findBySorusturmaNo(String sorusturmaNo) {
        List<HtsMahkemeKararTalep> htsMahkemeKararTalepList = dbHtsMahkemeKararTalepService.findBySorusturmaNo(sorusturmaNo);
        return htsMahkemeKararTalepMapper.toDtoList(htsMahkemeKararTalepList);
    }

    /**
     * Get HTS mahkeme karar talep by kayit tarihi between
     * @param startDate Start date
     * @param endDate End date
     * @return List of HtsMahkemeKararTalepDTO
     */
    @Transactional(readOnly = true)
    public List<HtsMahkemeKararTalepDTO> findByKayitTarihiBetween(Date startDate, Date endDate) {
        List<HtsMahkemeKararTalep> htsMahkemeKararTalepList = dbHtsMahkemeKararTalepService.findByKayitTarihiBetween(startDate, endDate);
        return htsMahkemeKararTalepMapper.toDtoList(htsMahkemeKararTalepList);
    }

    /**
     * Get HTS mahkeme karar talep by mahkeme ili and mahkeme kodu
     * @param mahkemeIli Mahkeme ili
     * @param mahkemeKodu Mahkeme kodu
     * @return List of HtsMahkemeKararTalepDTO
     */
    @Transactional(readOnly = true)
    public List<HtsMahkemeKararTalepDTO> findByMahkemeIliAndMahkemeKodu(String mahkemeIli, String mahkemeKodu) {
        List<HtsMahkemeKararTalep> htsMahkemeKararTalepList = dbHtsMahkemeKararTalepService.findByMahkemeIliAndMahkemeKodu(mahkemeIli, mahkemeKodu);
        return htsMahkemeKararTalepMapper.toDtoList(htsMahkemeKararTalepList);
    }

    /**
     * Get HTS mahkeme karar talep by karar tip and durum
     * @param kararTip Karar tip
     * @param durum Durum
     * @return List of HtsMahkemeKararTalepDTO
     */
    @Transactional(readOnly = true)
    public List<HtsMahkemeKararTalepDTO> findByKararTipAndDurum(String kararTip, String durum) {
        List<HtsMahkemeKararTalep> htsMahkemeKararTalepList = dbHtsMahkemeKararTalepService.findByKararTipAndDurum(kararTip, durum);
        return htsMahkemeKararTalepMapper.toDtoList(htsMahkemeKararTalepList);
    }

    /**
     * Check if mahkeme karar no exists
     * @param mahkemeKararNo Mahkeme karar no
     * @return true if exists, false otherwise
     */
    @Transactional(readOnly = true)
    public boolean existsByMahkemeKararNo(String mahkemeKararNo) {
        return dbHtsMahkemeKararTalepService.existsByMahkemeKararNo(mahkemeKararNo);
    }

    /**
     * Create new HTS mahkeme karar talep
     * @param htsMahkemeKararTalepDTO HtsMahkemeKararTalepDTO
     * @return Created HtsMahkemeKararTalepDTO
     */
    @Transactional
    public HtsMahkemeKararTalepDTO create(HtsMahkemeKararTalepDTO htsMahkemeKararTalepDTO) {
        // Check if evrak exists
        Optional<EvrakKayit> evrakKayit = dbEvrakKayitService.findById(htsMahkemeKararTalepDTO.getEvrakId());
        if (evrakKayit.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Evrak bulunamadı: " + htsMahkemeKararTalepDTO.getEvrakId());
        }

        // Check if mahkeme karar no already exists
        if (htsMahkemeKararTalepDTO.getMahkemeKararNo() != null && 
            !htsMahkemeKararTalepDTO.getMahkemeKararNo().isEmpty() && 
            dbHtsMahkemeKararTalepService.existsByMahkemeKararNo(htsMahkemeKararTalepDTO.getMahkemeKararNo())) {
            throw new ResponseStatusException(HttpStatus.CONFLICT, "Bu mahkeme karar numarası zaten mevcut: " + htsMahkemeKararTalepDTO.getMahkemeKararNo());
        }

        // Set kayit tarihi if not provided
        if (htsMahkemeKararTalepDTO.getKayitTarihi() == null) {
            htsMahkemeKararTalepDTO.setKayitTarihi(new Date());
        }

        // Set default durum if not provided
        if (htsMahkemeKararTalepDTO.getDurum() == null) {
            htsMahkemeKararTalepDTO.setDurum("AKTIF");
        }

        HtsMahkemeKararTalep htsMahkemeKararTalep = htsMahkemeKararTalepMapper.toEntity(htsMahkemeKararTalepDTO);
        dbHtsMahkemeKararTalepService.save(htsMahkemeKararTalep);
        log.info("HTS mahkeme karar talep oluşturuldu: {}", htsMahkemeKararTalep.getId());
        return htsMahkemeKararTalepMapper.toDto(htsMahkemeKararTalep);
    }

    /**
     * Update existing HTS mahkeme karar talep
     * @param id HtsMahkemeKararTalep id
     * @param htsMahkemeKararTalepDTO HtsMahkemeKararTalepDTO
     * @return Updated HtsMahkemeKararTalepDTO
     */
    @Transactional
    public HtsMahkemeKararTalepDTO update(Long id, HtsMahkemeKararTalepDTO htsMahkemeKararTalepDTO) {
        HtsMahkemeKararTalep existingHtsMahkemeKararTalep = dbHtsMahkemeKararTalepService.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "HTS mahkeme karar talep bulunamadı: " + id));

        // Check if mahkeme karar no already exists (if changed)
        if (htsMahkemeKararTalepDTO.getMahkemeKararNo() != null && 
            !htsMahkemeKararTalepDTO.getMahkemeKararNo().isEmpty() && 
            !htsMahkemeKararTalepDTO.getMahkemeKararNo().equals(existingHtsMahkemeKararTalep.getMahkemeKararNo()) && 
            dbHtsMahkemeKararTalepService.existsByMahkemeKararNo(htsMahkemeKararTalepDTO.getMahkemeKararNo())) {
            throw new ResponseStatusException(HttpStatus.CONFLICT, "Bu mahkeme karar numarası zaten mevcut: " + htsMahkemeKararTalepDTO.getMahkemeKararNo());
        }

        // Ensure evrakId and kullaniciId are not changed
        htsMahkemeKararTalepDTO.setEvrakId(existingHtsMahkemeKararTalep.getEvrakId());
        htsMahkemeKararTalepDTO.setKullaniciId(existingHtsMahkemeKararTalep.getKullaniciId());

        HtsMahkemeKararTalep updatedHtsMahkemeKararTalep = htsMahkemeKararTalepMapper.updateEntityFromDto(existingHtsMahkemeKararTalep, htsMahkemeKararTalepDTO);
        dbHtsMahkemeKararTalepService.update(updatedHtsMahkemeKararTalep);
        log.info("HTS mahkeme karar talep güncellendi: {}", updatedHtsMahkemeKararTalep.getId());
        return htsMahkemeKararTalepMapper.toDto(updatedHtsMahkemeKararTalep);
    }

    /**
     * Delete HTS mahkeme karar talep
     * @param id HtsMahkemeKararTalep id
     */
    @Transactional
    public void delete(Long id) {
        HtsMahkemeKararTalep htsMahkemeKararTalep = dbHtsMahkemeKararTalepService.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "HTS mahkeme karar talep bulunamadı: " + id));
        dbHtsMahkemeKararTalepService.delete(htsMahkemeKararTalep);
        log.info("HTS mahkeme karar talep silindi: {}", id);
    }
}
