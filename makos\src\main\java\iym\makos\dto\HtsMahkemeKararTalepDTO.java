package iym.makos.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * DTO for HtsMahkemeKararTalep entity
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "HTS Mahkeme Karar Talep bilgilerini içerir")
public class HtsMahkemeKararTalepDTO {

    @Schema(description = "HTS Mahkeme Karar Talep ID")
    private Long id;

    @Schema(description = "Evrak ID", example = "1")
    @NotNull(message = "Evrak ID boş olamaz")
    private Long evrakId;

    @Schema(description = "Kullanıcı ID", example = "1")
    @NotNull(message = "Kullanıcı ID boş olamaz")
    private Long kullaniciId;

    @Schema(description = "Kayıt tarihi", example = "2023-01-01T00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "Europe/Istanbul")
    @NotNull(message = "Kayıt tarihi boş olamaz")
    private Date kayitTarihi;

    @Schema(description = "Durum", example = "AKTIF")
    @Size(max = 100, message = "Durum 100 karakterden fazla olamaz")
    private String durum;

    @Schema(description = "Karar tipi", example = "ILETISIM_TESPITI")
    @NotNull(message = "Karar tipi boş olamaz")
    @Size(max = 100, message = "Karar tipi 100 karakterden fazla olamaz")
    private String kararTip;

    @Schema(description = "Hukuk birimi", example = "AĞIR CEZA")
    @NotNull(message = "Hukuk birimi boş olamaz")
    @Size(max = 100, message = "Hukuk birimi 100 karakterden fazla olamaz")
    private String hukukBirim;

    @Schema(description = "Mahkeme ili", example = "0600")
    @NotNull(message = "Mahkeme ili boş olamaz")
    @Size(max = 100, message = "Mahkeme ili 100 karakterden fazla olamaz")
    private String mahkemeIli;

    @Schema(description = "Mahkeme kodu", example = "ACM01")
    @NotNull(message = "Mahkeme kodu boş olamaz")
    @Size(max = 100, message = "Mahkeme kodu 100 karakterden fazla olamaz")
    private String mahkemeKodu;

    @Schema(description = "Mahkeme adı", example = "ANKARA 1. AĞIR CEZA MAHKEMESİ")
    @NotNull(message = "Mahkeme adı boş olamaz")
    @Size(max = 1000, message = "Mahkeme adı 1000 karakterden fazla olamaz")
    private String mahkemeAdi;

    @Schema(description = "Açıklama", example = "İletişim tespiti kararı")
    @Size(max = 1000, message = "Açıklama 1000 karakterden fazla olamaz")
    private String aciklama;

    @Schema(description = "Mahkeme karar numarası", example = "HTS-2023-001")
    @Size(max = 100, message = "Mahkeme karar numarası 100 karakterden fazla olamaz")
    private String mahkemeKararNo;

    @Schema(description = "Soruşturma numarası", example = "2023/125")
    @Size(max = 100, message = "Soruşturma numarası 100 karakterden fazla olamaz")
    private String sorusturmaNo;
}
