package iym.makos.mapper;

import iym.common.model.entity.iym.MahkemeAidiyatTalep;
import org.springframework.stereotype.Component;
import iym.makos.dto.MahkemeAidiyatTalepDTO;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper for MahkemeAidiyatTalep entity and DTO
 */
@Component
public class MahkemeAidiyatTalepMapper {

    /**
     * Convert entity to DTO
     * @param entity MahkemeAidiyatTalep entity
     * @return MahkemeAidiyatTalepDTO
     */
    public MahkemeAidiyatTalepDTO toDto(MahkemeAidiyatTalep entity) {
        if (entity == null) {
            return null;
        }

        return MahkemeAidiyatTalepDTO.builder()
                .id(entity.getId())
                .mahkemeKararTalepId(entity.getMahkemeKararTalepId())
                .aidiyatKod(entity.getAidiyatKod())
                .durumu(entity.getDurumu())
                .build();
    }

    /**
     * Convert DTO to entity
     * @param dto MahkemeAidiyatTalepDTO
     * @return MahkemeAidiyatTalep entity
     */
    public MahkemeAidiyatTalep toEntity(MahkemeAidiyatTalepDTO dto) {
        if (dto == null) {
            return null;
        }

        return MahkemeAidiyatTalep.builder()
                .id(dto.getId())
                .mahkemeKararTalepId(dto.getMahkemeKararTalepId())
                .aidiyatKod(dto.getAidiyatKod())
                .durumu(dto.getDurumu())
                .build();
    }

    /**
     * Update entity from DTO
     * @param entity MahkemeAidiyatTalep entity to update
     * @param dto MahkemeAidiyatTalepDTO with new values
     * @return Updated MahkemeAidiyatTalep entity
     */
    public MahkemeAidiyatTalep updateEntityFromDto(MahkemeAidiyatTalep entity, MahkemeAidiyatTalepDTO dto) {
        if (entity == null || dto == null) {
            return entity;
        }

        entity.setMahkemeKararTalepId(dto.getMahkemeKararTalepId());
        entity.setAidiyatKod(dto.getAidiyatKod());
        entity.setDurumu(dto.getDurumu());

        return entity;
    }

    /**
     * Convert list of entities to list of DTOs
     * @param entities List of MahkemeAidiyatTalep entities
     * @return List of MahkemeAidiyatTalepDTO
     */
    public List<MahkemeAidiyatTalepDTO> toDtoList(List<MahkemeAidiyatTalep> entities) {
        if (entities == null) {
            return List.of();
        }

        return entities.stream()
                .map(this::toDto)
                .collect(Collectors.toList());
    }
}
