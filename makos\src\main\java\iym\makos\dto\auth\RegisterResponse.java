package iym.makos.dto.auth;

import iym.common.model.api.ApiResponseBase;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

/**
 * Register response DTO for MAKOS authentication
 */
@Data
@SuperBuilder
@ToString
@EqualsAndHashCode(callSuper = true)
public class RegisterResponse extends ApiResponseBase {
    // Add any additional fields if needed
} 