-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;

-- Create sequence for HEDEF_TIPLERI if it doesn't exist
DECLARE
  seq_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO seq_exists FROM user_sequences WHERE sequence_name = 'HEDEF_TIPLERI_SEQ';
  IF seq_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE SEQUENCE iym.HEDEF_TIPLERI_SEQ
      START WITH 1
      INCREMENT BY 1
      NOCACHE
      NOCYCLE';
  END IF;
END;
/

-- Create HEDEF_TIPLERI table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'HEDEF_TIPLERI';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.HEDEF_TIPLERI (
      ID NUMBER,
      HEDEF_KODU NUMBER NOT NULL,
      HEDEF_TIPI VARCHAR2(25 BYTE),
      <PERSON>ONLANDIRMA<PERSON> CHAR(1 BYTE),
      <PERSON><PERSON><PERSON><PERSON><PERSON>I NUMBER,
      SNO NUMBER,
      H<PERSON>EF_TANIM VARCHAR2(16 BYTE),
      DURUM VARCHAR2(8 BYTE),
      HITAP_TIP VARCHAR2(8 BYTE),
      HITAP_ICERIK_TIP VARCHAR2(8 BYTE),
      HITAP_ICINDEMI VARCHAR2(8 BYTE),
      HITAP_EH CHAR(1 BYTE),
      MINL NUMBER,
      MAXL NUMBER,
      IMHA_YAPILSINMI VARCHAR2(8 BYTE),
      TASINABILIRMI VARCHAR2(1 BYTE),
      AKTIFMI NUMBER DEFAULT 1,
      HITAPA_GONDERILECEKMI NUMBER(1, 0) DEFAULT 0,
      CONSTRAINT HEDEF_TIPLERI_PRM PRIMARY KEY (HEDEF_KODU) ENABLE
    )';
    
    -- Create index
    EXECUTE IMMEDIATE 'CREATE INDEX iym.IND_HEDEF_TIPI_UPPER ON iym.HEDEF_TIPLERI (UPPER(HEDEF_TIPI) ASC)';
  END IF;
END;
/

-- Insert sample data if table is empty
DECLARE
  row_count NUMBER;
BEGIN
  SELECT COUNT(*) INTO row_count FROM iym.HEDEF_TIPLERI;
  IF row_count = 0 THEN
    -- Sample data 1 - Telefon
    INSERT INTO iym.HEDEF_TIPLERI (
      ID, HEDEF_KODU, HEDEF_TIPI, SONLANDIRMAMI, KARSILIGI, SNO, 
      HEDEF_TANIM, DURUM, HITAP_TIP, HITAP_ICERIK_TIP, HITAP_ICINDEMI, 
      HITAP_EH, MINL, MAXL, IMHA_YAPILSINMI, TASINABILIRMI, AKTIFMI, HITAPA_GONDERILECEKMI
    ) VALUES (
      iym.HEDEF_TIPLERI_SEQ.NEXTVAL, 1, 'TELEFON', 'E', 1, 1,
      'TEL', 'AKTIF', 'TEL', 'NUM', 'E',
      'E', 10, 10, 'E', 'E', 1, 1
    );
    
    -- Sample data 2 - E-posta
    INSERT INTO iym.HEDEF_TIPLERI (
      ID, HEDEF_KODU, HEDEF_TIPI, SONLANDIRMAMI, KARSILIGI, SNO, 
      HEDEF_TANIM, DURUM, HITAP_TIP, HITAP_ICERIK_TIP, HITAP_ICINDEMI, 
      HITAP_EH, MINL, MAXL, IMHA_YAPILSINMI, TASINABILIRMI, AKTIFMI, HITAPA_GONDERILECEKMI
    ) VALUES (
      iym.HEDEF_TIPLERI_SEQ.NEXTVAL, 2, 'E-POSTA', 'E', 2, 2,
      'MAIL', 'AKTIF', 'MAIL', 'MAIL', 'E',
      'E', 5, 50, 'E', 'E', 1, 1
    );
    
    -- Sample data 3 - IP Adresi
    INSERT INTO iym.HEDEF_TIPLERI (
      ID, HEDEF_KODU, HEDEF_TIPI, SONLANDIRMAMI, KARSILIGI, SNO, 
      HEDEF_TANIM, DURUM, HITAP_TIP, HITAP_ICERIK_TIP, HITAP_ICINDEMI, 
      HITAP_EH, MINL, MAXL, IMHA_YAPILSINMI, TASINABILIRMI, AKTIFMI, HITAPA_GONDERILECEKMI
    ) VALUES (
      iym.HEDEF_TIPLERI_SEQ.NEXTVAL, 3, 'IP', 'E', 3, 3,
      'IP', 'AKTIF', 'IP', 'IP', 'E',
      'E', 7, 15, 'E', 'E', 1, 1
    );

        -- Sample data 4 - GSM
    INSERT INTO iym.HEDEF_TIPLERI (
        ID, HEDEF_KODU, HEDEF_TIPI, SONLANDIRMAMI, KARSILIGI, SNO,
        HEDEF_TANIM, DURUM, HITAP_TIP, HITAP_ICERIK_TIP, HITAP_ICINDEMI,
        HITAP_EH, MINL, MAXL, IMHA_YAPILSINMI, TASINABILIRMI, AKTIFMI, HITAPA_GONDERILECEKMI
    ) VALUES (
        iym.HEDEF_TIPLERI_SEQ.NEXTVAL, 10, 'GSM', 'E', 2, 2,
        'GSM', 'AKTIF', 'GSM', 'GSM', 'E',
        'E', 5, 50, 'E', 'E', 1, 1
        );
  END IF;
END;
/

COMMIT;
