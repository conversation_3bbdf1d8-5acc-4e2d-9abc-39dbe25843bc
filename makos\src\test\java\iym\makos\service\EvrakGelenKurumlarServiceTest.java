package iym.makos.service;

import iym.common.model.entity.iym.EvrakGelenKurumlar;
import iym.common.service.db.DbEvrakGelenKurumlarService;
import iym.makos.dto.EvrakGelenKurumlarDTO;
import iym.makos.mapper.EvrakGelenKurumlarMapper;
import iym.makos.service.makos.EvrakGelenKurumlarService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.web.server.ResponseStatusException;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class EvrakGelenKurumlarServiceTest {

    @Mock
    private DbEvrakGelenKurumlarService dbEvrakGelenKurumlarService;

    @Mock
    private EvrakGelenKurumlarMapper evrakGelenKurumlarMapper;

    @InjectMocks
    private EvrakGelenKurumlarService evrakGelenKurumlarService;

    private EvrakGelenKurumlar evrakGelenKurumlar;
    private EvrakGelenKurumlarDTO evrakGelenKurumlarDTO;
    private List<EvrakGelenKurumlar> evrakGelenKurumlarList;
    private List<EvrakGelenKurumlarDTO> evrakGelenKurumlarDTOList;

    @BeforeEach
    void setUp() {
        evrakGelenKurumlar = EvrakGelenKurumlar.builder()
                .id(1L)
                .kurumKod("01")
                .kurumAdi("ADALET BAKANLIĞI")
                .kurum("ADALET BAKANLIĞI")
                .idx(1L)
                .build();

        EvrakGelenKurumlar evrakGelenKurumlar2 = EvrakGelenKurumlar.builder()
                .id(2L)
                .kurumKod("02")
                .kurumAdi("İÇİŞLERİ BAKANLIĞI")
                .kurum("İÇİŞLERİ BAKANLIĞI")
                .idx(2L)
                .build();

        evrakGelenKurumlarDTO = EvrakGelenKurumlarDTO.builder()
                .id(1L)
                .kurumKod("01")
                .kurumAdi("ADALET BAKANLIĞI")
                .kurum("ADALET BAKANLIĞI")
                .idx(1L)
                .build();

        EvrakGelenKurumlarDTO evrakGelenKurumlarDTO2 = EvrakGelenKurumlarDTO.builder()
                .id(2L)
                .kurumKod("02")
                .kurumAdi("İÇİŞLERİ BAKANLIĞI")
                .kurum("İÇİŞLERİ BAKANLIĞI")
                .idx(2L)
                .build();

        evrakGelenKurumlarList = Arrays.asList(evrakGelenKurumlar, evrakGelenKurumlar2);
        evrakGelenKurumlarDTOList = Arrays.asList(evrakGelenKurumlarDTO, evrakGelenKurumlarDTO2);
    }

    @Test
    void findAll_shouldReturnAllEvrakGelenKurumlar() {
        // Given
        when(dbEvrakGelenKurumlarService.findAll()).thenReturn(evrakGelenKurumlarList);
        when(evrakGelenKurumlarMapper.toDtoList(evrakGelenKurumlarList)).thenReturn(evrakGelenKurumlarDTOList);

        // When
        List<EvrakGelenKurumlarDTO> result = evrakGelenKurumlarService.findAll();

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(2);
        assertThat(result).isEqualTo(evrakGelenKurumlarDTOList);
        verify(dbEvrakGelenKurumlarService).findAll();
        verify(evrakGelenKurumlarMapper).toDtoList(evrakGelenKurumlarList);
    }

    @Test
    void findAllOrdered_shouldReturnAllEvrakGelenKurumlarOrdered() {
        // Given
        when(dbEvrakGelenKurumlarService.findAllByOrderByIdxAsc()).thenReturn(evrakGelenKurumlarList);
        when(evrakGelenKurumlarMapper.toDtoList(evrakGelenKurumlarList)).thenReturn(evrakGelenKurumlarDTOList);

        // When
        List<EvrakGelenKurumlarDTO> result = evrakGelenKurumlarService.findAllOrdered();

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(2);
        assertThat(result).isEqualTo(evrakGelenKurumlarDTOList);
        verify(dbEvrakGelenKurumlarService).findAllByOrderByIdxAsc();
        verify(evrakGelenKurumlarMapper).toDtoList(evrakGelenKurumlarList);
    }

    @Test
    void findAll_withPageable_shouldReturnPageOfEvrakGelenKurumlar() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);
        Page<EvrakGelenKurumlar> evrakGelenKurumlarPage = new PageImpl<>(evrakGelenKurumlarList, pageable, evrakGelenKurumlarList.size());

        when(dbEvrakGelenKurumlarService.findAll(pageable)).thenReturn(evrakGelenKurumlarPage);
        when(evrakGelenKurumlarMapper.toDtoList(evrakGelenKurumlarList)).thenReturn(evrakGelenKurumlarDTOList);

        // When
        Page<EvrakGelenKurumlarDTO> result = evrakGelenKurumlarService.findAll(pageable);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getContent()).isEqualTo(evrakGelenKurumlarDTOList);
        assertThat(result.getTotalElements()).isEqualTo(2);
        verify(dbEvrakGelenKurumlarService).findAll(pageable);
        verify(evrakGelenKurumlarMapper).toDtoList(evrakGelenKurumlarList);
    }

    @Test
    void findById_shouldReturnEvrakGelenKurumlar_whenExists() {
        // Given
        when(dbEvrakGelenKurumlarService.findById(1L)).thenReturn(Optional.of(evrakGelenKurumlar));
        when(evrakGelenKurumlarMapper.toDto(evrakGelenKurumlar)).thenReturn(evrakGelenKurumlarDTO);

        // When
        EvrakGelenKurumlarDTO result = evrakGelenKurumlarService.findById(1L);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEqualTo(evrakGelenKurumlarDTO);
        verify(dbEvrakGelenKurumlarService).findById(1L);
        verify(evrakGelenKurumlarMapper).toDto(evrakGelenKurumlar);
    }

    @Test
    void findById_shouldThrowException_whenNotExists() {
        // Given
        when(dbEvrakGelenKurumlarService.findById(1L)).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResponseStatusException.class, () -> evrakGelenKurumlarService.findById(1L));
        verify(dbEvrakGelenKurumlarService).findById(1L);
        verify(evrakGelenKurumlarMapper, never()).toDto(any());
    }

    @Test
    void findByKurumKod_shouldReturnEvrakGelenKurumlar_whenExists() {
        // Given
        when(dbEvrakGelenKurumlarService.findByKurumKod("01")).thenReturn(Optional.of(evrakGelenKurumlar));
        when(evrakGelenKurumlarMapper.toDto(evrakGelenKurumlar)).thenReturn(evrakGelenKurumlarDTO);

        // When
        EvrakGelenKurumlarDTO result = evrakGelenKurumlarService.findByKurumKod("01");

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEqualTo(evrakGelenKurumlarDTO);
        verify(dbEvrakGelenKurumlarService).findByKurumKod("01");
        verify(evrakGelenKurumlarMapper).toDto(evrakGelenKurumlar);
    }

    @Test
    void findByKurumKod_shouldThrowException_whenNotExists() {
        // Given
        when(dbEvrakGelenKurumlarService.findByKurumKod("01")).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResponseStatusException.class, () -> evrakGelenKurumlarService.findByKurumKod("01"));
        verify(dbEvrakGelenKurumlarService).findByKurumKod("01");
        verify(evrakGelenKurumlarMapper, never()).toDto(any());
    }

    @Test
    void create_shouldCreateEvrakGelenKurumlar() {
        // Given
        when(dbEvrakGelenKurumlarService.existsByKurumKod("01")).thenReturn(false);
        when(evrakGelenKurumlarMapper.toEntity(evrakGelenKurumlarDTO)).thenReturn(evrakGelenKurumlar);
        when(evrakGelenKurumlarMapper.toDto(evrakGelenKurumlar)).thenReturn(evrakGelenKurumlarDTO);

        // When
        EvrakGelenKurumlarDTO result = evrakGelenKurumlarService.create(evrakGelenKurumlarDTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEqualTo(evrakGelenKurumlarDTO);
        verify(dbEvrakGelenKurumlarService).existsByKurumKod("01");
        verify(evrakGelenKurumlarMapper).toEntity(evrakGelenKurumlarDTO);
        verify(dbEvrakGelenKurumlarService).save(evrakGelenKurumlar);
        verify(evrakGelenKurumlarMapper).toDto(evrakGelenKurumlar);
    }

    @Test
    void create_shouldThrowException_whenKurumKodAlreadyExists() {
        // Given
        when(dbEvrakGelenKurumlarService.existsByKurumKod("01")).thenReturn(true);

        // When/Then
        assertThrows(ResponseStatusException.class, () -> evrakGelenKurumlarService.create(evrakGelenKurumlarDTO));
        verify(dbEvrakGelenKurumlarService).existsByKurumKod("01");
        verify(evrakGelenKurumlarMapper, never()).toEntity(any());
        verify(dbEvrakGelenKurumlarService, never()).save(any());
        verify(evrakGelenKurumlarMapper, never()).toDto(any());
    }

    @Test
    void update_shouldUpdateEvrakGelenKurumlar() {
        // Given
        when(dbEvrakGelenKurumlarService.findById(1L)).thenReturn(Optional.of(evrakGelenKurumlar));
        when(evrakGelenKurumlarMapper.updateEntityFromDto(evrakGelenKurumlar, evrakGelenKurumlarDTO)).thenReturn(evrakGelenKurumlar);
        when(evrakGelenKurumlarMapper.toDto(evrakGelenKurumlar)).thenReturn(evrakGelenKurumlarDTO);

        // When
        EvrakGelenKurumlarDTO result = evrakGelenKurumlarService.update(1L, evrakGelenKurumlarDTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isEqualTo(evrakGelenKurumlarDTO);
        verify(dbEvrakGelenKurumlarService).findById(1L);
        verify(evrakGelenKurumlarMapper).updateEntityFromDto(evrakGelenKurumlar, evrakGelenKurumlarDTO);
        verify(dbEvrakGelenKurumlarService).update(evrakGelenKurumlar);
        verify(evrakGelenKurumlarMapper).toDto(evrakGelenKurumlar);
    }

    @Test
    void update_shouldThrowException_whenEvrakGelenKurumlarNotExists() {
        // Given
        when(dbEvrakGelenKurumlarService.findById(1L)).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResponseStatusException.class, () -> evrakGelenKurumlarService.update(1L, evrakGelenKurumlarDTO));
        verify(dbEvrakGelenKurumlarService).findById(1L);
        verify(evrakGelenKurumlarMapper, never()).updateEntityFromDto(any(), any());
        verify(dbEvrakGelenKurumlarService, never()).update(any());
        verify(evrakGelenKurumlarMapper, never()).toDto(any());
    }

    @Test
    void delete_shouldDeleteEvrakGelenKurumlar() {
        // Given
        when(dbEvrakGelenKurumlarService.findById(1L)).thenReturn(Optional.of(evrakGelenKurumlar));

        // When
        evrakGelenKurumlarService.delete(1L);

        // Then
        verify(dbEvrakGelenKurumlarService).findById(1L);
        verify(dbEvrakGelenKurumlarService).delete(evrakGelenKurumlar);
    }

    @Test
    void delete_shouldThrowException_whenEvrakGelenKurumlarNotExists() {
        // Given
        when(dbEvrakGelenKurumlarService.findById(1L)).thenReturn(Optional.empty());

        // When/Then
        assertThrows(ResponseStatusException.class, () -> evrakGelenKurumlarService.delete(1L));
        verify(dbEvrakGelenKurumlarService).findById(1L);
        verify(dbEvrakGelenKurumlarService, never()).delete(any());
    }
}
