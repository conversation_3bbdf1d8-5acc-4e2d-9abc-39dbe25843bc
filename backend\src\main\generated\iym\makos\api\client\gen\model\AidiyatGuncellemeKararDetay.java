/*
 * MAKOS OpenAPI definition
 * MAKOS Application
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package iym.makos.api.client.gen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import iym.makos.api.client.gen.model.AidiyatGuncellemeDetay;
import iym.makos.api.client.gen.model.MahkemeKararDetay;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * <PERSON><PERSON>ncelleme yapılacak mahkeme karar bilgisi ve karara ait güncellenecek aidiyat bilgileri
 */
@JsonPropertyOrder({
  AidiyatGuncellemeKararDetay.JSON_PROPERTY_MAHKEME_KARAR_DETAY,
  AidiyatGuncellemeKararDetay.JSON_PROPERTY_AIDIYAT_GUNCELLEME_DETAY_LIST
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class AidiyatGuncellemeKararDetay {
  public static final String JSON_PROPERTY_MAHKEME_KARAR_DETAY = "mahkemeKararDetay";
  private MahkemeKararDetay mahkemeKararDetay;

  public static final String JSON_PROPERTY_AIDIYAT_GUNCELLEME_DETAY_LIST = "aidiyatGuncellemeDetayList";
  private List<AidiyatGuncellemeDetay> aidiyatGuncellemeDetayList = new ArrayList<>();

  public AidiyatGuncellemeKararDetay() {
  }

  public AidiyatGuncellemeKararDetay mahkemeKararDetay(MahkemeKararDetay mahkemeKararDetay) {
    
    this.mahkemeKararDetay = mahkemeKararDetay;
    return this;
  }

   /**
   * Get mahkemeKararDetay
   * @return mahkemeKararDetay
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_DETAY)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public MahkemeKararDetay getMahkemeKararDetay() {
    return mahkemeKararDetay;
  }


  @JsonProperty(JSON_PROPERTY_MAHKEME_KARAR_DETAY)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setMahkemeKararDetay(MahkemeKararDetay mahkemeKararDetay) {
    this.mahkemeKararDetay = mahkemeKararDetay;
  }


  public AidiyatGuncellemeKararDetay aidiyatGuncellemeDetayList(List<AidiyatGuncellemeDetay> aidiyatGuncellemeDetayList) {
    
    this.aidiyatGuncellemeDetayList = aidiyatGuncellemeDetayList;
    return this;
  }

  public AidiyatGuncellemeKararDetay addAidiyatGuncellemeDetayListItem(AidiyatGuncellemeDetay aidiyatGuncellemeDetayListItem) {
    if (this.aidiyatGuncellemeDetayList == null) {
      this.aidiyatGuncellemeDetayList = new ArrayList<>();
    }
    this.aidiyatGuncellemeDetayList.add(aidiyatGuncellemeDetayListItem);
    return this;
  }

   /**
   * Get aidiyatGuncellemeDetayList
   * @return aidiyatGuncellemeDetayList
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_AIDIYAT_GUNCELLEME_DETAY_LIST)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public List<AidiyatGuncellemeDetay> getAidiyatGuncellemeDetayList() {
    return aidiyatGuncellemeDetayList;
  }


  @JsonProperty(JSON_PROPERTY_AIDIYAT_GUNCELLEME_DETAY_LIST)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setAidiyatGuncellemeDetayList(List<AidiyatGuncellemeDetay> aidiyatGuncellemeDetayList) {
    this.aidiyatGuncellemeDetayList = aidiyatGuncellemeDetayList;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    AidiyatGuncellemeKararDetay aidiyatGuncellemeKararDetay = (AidiyatGuncellemeKararDetay) o;
    return Objects.equals(this.mahkemeKararDetay, aidiyatGuncellemeKararDetay.mahkemeKararDetay) &&
        Objects.equals(this.aidiyatGuncellemeDetayList, aidiyatGuncellemeKararDetay.aidiyatGuncellemeDetayList);
  }

  @Override
  public int hashCode() {
    return Objects.hash(mahkemeKararDetay, aidiyatGuncellemeDetayList);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class AidiyatGuncellemeKararDetay {\n");
    sb.append("    mahkemeKararDetay: ").append(toIndentedString(mahkemeKararDetay)).append("\n");
    sb.append("    aidiyatGuncellemeDetayList: ").append(toIndentedString(aidiyatGuncellemeDetayList)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

