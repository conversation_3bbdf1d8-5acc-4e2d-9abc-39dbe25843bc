package iym.makos.mapper;

import iym.common.model.entity.iym.HtsMahkemeKararTalep;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import iym.makos.dto.HtsMahkemeKararTalepDTO;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

class HtsMahkemeKararTalepMapperTest {

    private HtsMahkemeKararTalepMapper htsMahkemeKararTalepMapper;
    private HtsMahkemeKararTalep htsMahkemeKararTalep;
    private HtsMahkemeKararTalepDTO htsMahkemeKararTalepDTO;
    private Date testDate;

    @BeforeEach
    void setUp() {
        htsMahkemeKararTalepMapper = new HtsMahkemeKararTalepMapper();
        testDate = new Date();

        htsMahkemeKararTalep = HtsMahkemeKararTalep.builder()
                .id(1L)
                .evrakId(100L)
                .kullaniciId(200L)
                .kayitTarihi(testDate)
                .durum("AKTIF")
                .kararTip("ILETISIM_TESPITI")
                .hukukBirim("AĞIR CEZA")
                .mahkemeIli("0600")
                .mahkemeKodu("ACM01")
                .mahkemeAdi("ANKARA 1. AĞIR CEZA MAHKEMESİ")
                .aciklama("Test açıklama")
                .mahkemeKararNo("HTS-2023-001")
                .sorusturmaNo("2023/125")
                .build();

        htsMahkemeKararTalepDTO = HtsMahkemeKararTalepDTO.builder()
                .id(1L)
                .evrakId(100L)
                .kullaniciId(200L)
                .kayitTarihi(testDate)
                .durum("AKTIF")
                .kararTip("ILETISIM_TESPITI")
                .hukukBirim("AĞIR CEZA")
                .mahkemeIli("0600")
                .mahkemeKodu("ACM01")
                .mahkemeAdi("ANKARA 1. AĞIR CEZA MAHKEMESİ")
                .aciklama("Test açıklama")
                .mahkemeKararNo("HTS-2023-001")
                .sorusturmaNo("2023/125")
                .build();
    }

    @Test
    void toDto_shouldMapEntityToDto() {
        // When
        HtsMahkemeKararTalepDTO result = htsMahkemeKararTalepMapper.toDto(htsMahkemeKararTalep);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(htsMahkemeKararTalep.getId());
        assertThat(result.getEvrakId()).isEqualTo(htsMahkemeKararTalep.getEvrakId());
        assertThat(result.getKullaniciId()).isEqualTo(htsMahkemeKararTalep.getKullaniciId());
        assertThat(result.getKayitTarihi()).isEqualTo(htsMahkemeKararTalep.getKayitTarihi());
        assertThat(result.getDurum()).isEqualTo(htsMahkemeKararTalep.getDurum());
        assertThat(result.getKararTip()).isEqualTo(htsMahkemeKararTalep.getKararTip());
        assertThat(result.getHukukBirim()).isEqualTo(htsMahkemeKararTalep.getHukukBirim());
        assertThat(result.getMahkemeIli()).isEqualTo(htsMahkemeKararTalep.getMahkemeIli());
        assertThat(result.getMahkemeKodu()).isEqualTo(htsMahkemeKararTalep.getMahkemeKodu());
        assertThat(result.getMahkemeAdi()).isEqualTo(htsMahkemeKararTalep.getMahkemeAdi());
        assertThat(result.getAciklama()).isEqualTo(htsMahkemeKararTalep.getAciklama());
        assertThat(result.getMahkemeKararNo()).isEqualTo(htsMahkemeKararTalep.getMahkemeKararNo());
        assertThat(result.getSorusturmaNo()).isEqualTo(htsMahkemeKararTalep.getSorusturmaNo());
    }

    @Test
    void toDto_shouldReturnNullWhenEntityIsNull() {
        // When
        HtsMahkemeKararTalepDTO result = htsMahkemeKararTalepMapper.toDto(null);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void toEntity_shouldMapDtoToEntity() {
        // When
        HtsMahkemeKararTalep result = htsMahkemeKararTalepMapper.toEntity(htsMahkemeKararTalepDTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(htsMahkemeKararTalepDTO.getId());
        assertThat(result.getEvrakId()).isEqualTo(htsMahkemeKararTalepDTO.getEvrakId());
        assertThat(result.getKullaniciId()).isEqualTo(htsMahkemeKararTalepDTO.getKullaniciId());
        assertThat(result.getKayitTarihi()).isEqualTo(htsMahkemeKararTalepDTO.getKayitTarihi());
        assertThat(result.getDurum()).isEqualTo(htsMahkemeKararTalepDTO.getDurum());
        assertThat(result.getKararTip()).isEqualTo(htsMahkemeKararTalepDTO.getKararTip());
        assertThat(result.getHukukBirim()).isEqualTo(htsMahkemeKararTalepDTO.getHukukBirim());
        assertThat(result.getMahkemeIli()).isEqualTo(htsMahkemeKararTalepDTO.getMahkemeIli());
        assertThat(result.getMahkemeKodu()).isEqualTo(htsMahkemeKararTalepDTO.getMahkemeKodu());
        assertThat(result.getMahkemeAdi()).isEqualTo(htsMahkemeKararTalepDTO.getMahkemeAdi());
        assertThat(result.getAciklama()).isEqualTo(htsMahkemeKararTalepDTO.getAciklama());
        assertThat(result.getMahkemeKararNo()).isEqualTo(htsMahkemeKararTalepDTO.getMahkemeKararNo());
        assertThat(result.getSorusturmaNo()).isEqualTo(htsMahkemeKararTalepDTO.getSorusturmaNo());
    }

    @Test
    void toEntity_shouldReturnNullWhenDtoIsNull() {
        // When
        HtsMahkemeKararTalep result = htsMahkemeKararTalepMapper.toEntity(null);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void updateEntityFromDto_shouldUpdateEntityWithDtoValues() {
        // Given
        HtsMahkemeKararTalep existingEntity = HtsMahkemeKararTalep.builder()
                .id(1L)
                .evrakId(100L)
                .kullaniciId(200L)
                .kayitTarihi(testDate)
                .durum("AKTIF")
                .kararTip("ILETISIM_TESPITI")
                .hukukBirim("AĞIR CEZA")
                .mahkemeIli("0600")
                .mahkemeKodu("ACM01")
                .mahkemeAdi("ANKARA 1. AĞIR CEZA MAHKEMESİ")
                .aciklama("Test açıklama")
                .mahkemeKararNo("HTS-2023-001")
                .sorusturmaNo("2023/125")
                .build();

        HtsMahkemeKararTalepDTO updatedDto = HtsMahkemeKararTalepDTO.builder()
                .id(1L)
                .evrakId(101L) // Should not be updated
                .kullaniciId(201L) // Should not be updated
                .kayitTarihi(testDate)
                .durum("PASIF")
                .kararTip("SINYAL_BILGILERI")
                .hukukBirim("SULH CEZA")
                .mahkemeIli("0601")
                .mahkemeKodu("SCM01")
                .mahkemeAdi("ANKARA 1. SULH CEZA MAHKEMESİ")
                .aciklama("Updated açıklama")
                .mahkemeKararNo("HTS-2023-002")
                .sorusturmaNo("2023/126")
                .build();

        // When
        HtsMahkemeKararTalep result = htsMahkemeKararTalepMapper.updateEntityFromDto(existingEntity, updatedDto);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(1L);
        assertThat(result.getEvrakId()).isEqualTo(100L); // Should not be updated
        assertThat(result.getKullaniciId()).isEqualTo(200L); // Should not be updated
        assertThat(result.getKayitTarihi()).isEqualTo(testDate);
        assertThat(result.getDurum()).isEqualTo("PASIF");
        assertThat(result.getKararTip()).isEqualTo("SINYAL_BILGILERI");
        assertThat(result.getHukukBirim()).isEqualTo("SULH CEZA");
        assertThat(result.getMahkemeIli()).isEqualTo("0601");
        assertThat(result.getMahkemeKodu()).isEqualTo("SCM01");
        assertThat(result.getMahkemeAdi()).isEqualTo("ANKARA 1. SULH CEZA MAHKEMESİ");
        assertThat(result.getAciklama()).isEqualTo("Updated açıklama");
        assertThat(result.getMahkemeKararNo()).isEqualTo("HTS-2023-002");
        assertThat(result.getSorusturmaNo()).isEqualTo("2023/126");
    }

    @Test
    void updateEntityFromDto_shouldReturnEntityWhenDtoIsNull() {
        // Given
        HtsMahkemeKararTalep existingEntity = HtsMahkemeKararTalep.builder()
                .id(1L)
                .evrakId(100L)
                .kullaniciId(200L)
                .kayitTarihi(testDate)
                .durum("AKTIF")
                .kararTip("ILETISIM_TESPITI")
                .hukukBirim("AĞIR CEZA")
                .mahkemeIli("0600")
                .mahkemeKodu("ACM01")
                .mahkemeAdi("ANKARA 1. AĞIR CEZA MAHKEMESİ")
                .aciklama("Test açıklama")
                .mahkemeKararNo("HTS-2023-001")
                .sorusturmaNo("2023/125")
                .build();

        // When
        HtsMahkemeKararTalep result = htsMahkemeKararTalepMapper.updateEntityFromDto(existingEntity, null);

        // Then
        assertThat(result).isEqualTo(existingEntity);
    }

    @Test
    void updateEntityFromDto_shouldReturnNullWhenEntityIsNull() {
        // When
        HtsMahkemeKararTalep result = htsMahkemeKararTalepMapper.updateEntityFromDto(null, htsMahkemeKararTalepDTO);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void toDtoList_shouldMapEntityListToDtoList() {
        // Given
        List<HtsMahkemeKararTalep> entityList = Arrays.asList(htsMahkemeKararTalep, htsMahkemeKararTalep);

        // When
        List<HtsMahkemeKararTalepDTO> result = htsMahkemeKararTalepMapper.toDtoList(entityList);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(2);
        assertThat(result.get(0).getId()).isEqualTo(htsMahkemeKararTalep.getId());
        assertThat(result.get(1).getId()).isEqualTo(htsMahkemeKararTalep.getId());
    }

    @Test
    void toDtoList_shouldReturnNullWhenEntityListIsNull() {
        // When
        List<HtsMahkemeKararTalepDTO> result = htsMahkemeKararTalepMapper.toDtoList(null);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void toEntityList_shouldMapDtoListToEntityList() {
        // Given
        List<HtsMahkemeKararTalepDTO> dtoList = Arrays.asList(htsMahkemeKararTalepDTO, htsMahkemeKararTalepDTO);

        // When
        List<HtsMahkemeKararTalep> result = htsMahkemeKararTalepMapper.toEntityList(dtoList);

        // Then
        assertThat(result).isNotNull();
        assertThat(result).hasSize(2);
        assertThat(result.get(0).getId()).isEqualTo(htsMahkemeKararTalepDTO.getId());
        assertThat(result.get(1).getId()).isEqualTo(htsMahkemeKararTalepDTO.getId());
    }

    @Test
    void toEntityList_shouldReturnNullWhenDtoListIsNull() {
        // When
        List<HtsMahkemeKararTalep> result = htsMahkemeKararTalepMapper.toEntityList(null);

        // Then
        assertThat(result).isNull();
    }
}
