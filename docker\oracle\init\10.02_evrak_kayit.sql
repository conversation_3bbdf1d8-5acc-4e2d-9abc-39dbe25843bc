-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;

-- Create sequence for EVRAK_KAYIT if it doesn't exist
DECLARE
  seq_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO seq_exists FROM user_sequences WHERE sequence_name = 'EVR<PERSON><PERSON>_KAYIT_SEQ';
  IF seq_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE SEQUENCE iym.EVRAK_KAYIT_SEQ
      START WITH 1
      INCREMENT BY 1
      NOCACHE
      NOCYCLE';
  END IF;
END;
/

-- Create EVRAK_KAYIT table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'EVRAK_KAYIT';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.EVRAK_KAYIT (
      ID NUMBER NOT NULL,
      EVRAK_SIRA_NO VARCHAR2(30 BYTE),
      EVRAK_NO VARCHAR2(50 BYTE),
      GIRIS_TARIH DATE NOT NULL,
      EVRAK_TARIHI DATE NOT NULL,
      EVRAK_GELDIGI_KURUM VARCHAR2(10 BYTE),
      KAY_KULLANICI NUMBER,
      EVRAK_TIPI VARCHAR2(10 BYTE),
      HAVALE_BIRIM VARCHAR2(10 BYTE),
      ACIKLAMA VARCHAR2(4000 BYTE),
      GEL_IL VARCHAR2(4 BYTE),
      EVRAK_KONUSU VARCHAR2(200 BYTE),
      ARSIV_DOSYA_NO VARCHAR2(20 BYTE),
      DURUMU VARCHAR2(20 BYTE),
      EVRAK_YONU VARCHAR2(100 BYTE),
      ONAY_TARIHI DATE,
      ACILMI CHAR(1 BYTE),
      SORUSTURMA_NO VARCHAR2(20 BYTE),
      MAHKEME_KARAR_NO VARCHAR2(20 BYTE),
      UNIQ_COL NUMBER,
      ELDEN_TESLIM VARCHAR2(1 BYTE),
      TEKITMI CHAR(1 BYTE),
      ASIL_EVRAK CHAR(1 BYTE) DEFAULT ''H'',
      ONCELIK VARCHAR2(50 BYTE) DEFAULT NULL,
      EVRAK_GELDIGI_KURUM_ESKI VARCHAR2(10 BYTE),
      EVRAK_GRUP VARCHAR2(300 BYTE),
      ONAMA VARCHAR2(1 BYTE)
    )';
    
    -- Create indexes
    EXECUTE IMMEDIATE 'CREATE INDEX iym.EVRAK_DURUM ON iym.EVRAK_KAYIT (DURUMU ASC)';
    EXECUTE IMMEDIATE 'CREATE INDEX iym.EVRAK_GEL_KURUM ON iym.EVRAK_KAYIT (EVRAK_GELDIGI_KURUM ASC)';
    EXECUTE IMMEDIATE 'CREATE INDEX iym.EVRAK_KAYIT_ACIL_GIRIS_TAR_IDX ON iym.EVRAK_KAYIT (ACILMI ASC, GIRIS_TARIH ASC)';
    EXECUTE IMMEDIATE 'CREATE INDEX iym.EVRAK_KAYIT_GELID_DRM_IDX ON iym.EVRAK_KAYIT (ID ASC)';
    EXECUTE IMMEDIATE 'CREATE INDEX iym.EVRAK_KAYIT_GELID_SORNO_IDX ON iym.EVRAK_KAYIT (SORUSTURMA_NO ASC, GEL_IL ASC)';
    EXECUTE IMMEDIATE 'CREATE INDEX iym.EVRAK_KAYIT_HAV_BIR ON iym.EVRAK_KAYIT (HAVALE_BIRIM ASC)';
    EXECUTE IMMEDIATE 'CREATE INDEX iym.EVRAK_KAYIT_SNO_IDX ON iym.EVRAK_KAYIT (SUBSTR(EVRAK_SIRA_NO, -10, 10) ASC)';
    EXECUTE IMMEDIATE 'CREATE INDEX iym.EVRAK_KAYIT_TRH_DRM_IDX ON iym.EVRAK_KAYIT (GIRIS_TARIH ASC, DURUMU ASC)';
    EXECUTE IMMEDIATE 'CREATE UNIQUE INDEX iym.EVRAK_NO_TARIH_KURUM_ILD_IDX ON iym.EVRAK_KAYIT (EVRAK_NO ASC, EVRAK_TARIHI ASC, GEL_IL ASC, EVRAK_GELDIGI_KURUM ASC, DURUMU ASC)';
    EXECUTE IMMEDIATE 'CREATE UNIQUE INDEX iym.EVRAK_SNO_IDX ON iym.EVRAK_KAYIT (EVRAK_SIRA_NO ASC)';
    EXECUTE IMMEDIATE 'CREATE INDEX iym.IDX_ID_GIRIS_TAR ON iym.EVRAK_KAYIT (ID ASC, GIRIS_TARIH ASC)';
  END IF;
END;
/

-- Insert sample data if table is empty
DECLARE
  row_count NUMBER;
BEGIN
  SELECT COUNT(*) INTO row_count FROM iym.EVRAK_KAYIT;
  IF row_count = 0 THEN
    -- Make sure we have users in KULLANICILAR table
    DECLARE
      user_count NUMBER;
    BEGIN
      SELECT COUNT(*) INTO user_count FROM iym.KULLANICILAR;
      
      IF user_count > 0 THEN
        -- Get the ID of the admin user
        DECLARE
          admin_id NUMBER;
        BEGIN
          SELECT ID INTO admin_id FROM iym.KULLANICILAR WHERE KULLANICI_ADI = 'admin';
          
          -- Sample data 1 - İletişimin Denetlenmesi Talebi
          INSERT INTO iym.EVRAK_KAYIT (
            ID, EVRAK_SIRA_NO, EVRAK_NO, GIRIS_TARIH, EVRAK_TARIHI,
            EVRAK_GELDIGI_KURUM, KAY_KULLANICI, EVRAK_TIPI, HAVALE_BIRIM,
            ACIKLAMA, GEL_IL, EVRAK_KONUSU, DURUMU, EVRAK_YONU, ACILMI,
            SORUSTURMA_NO, MAHKEME_KARAR_NO, ASIL_EVRAK, EVRAK_GRUP, ONAMA
          ) VALUES (
            iym.EVRAK_KAYIT_SEQ.NEXTVAL, 'TEST-2025-001', 'IDB-2025-12345',
            TO_DATE('2023-05-15 10:30:00', 'YYYY-MM-DD HH24:MI:SS'),
            TO_DATE('2023-05-10 14:00:00', 'YYYY-MM-DD HH24:MI:SS'),
            '02', admin_id, '401.01.00', 'HUKUK',
            'İletişimin denetlenmesi talebi örnek açıklama', '0600', 'İletişimin Denetlenmesi Talebi',
            'AKTIF', 'ILETISIMIN_DENETLENMESI', 'H',
            '2025/001', 'MK-2025-001', 'H', 'İletişim Denetleme', 'E'
          );
          
          -- Sample data 2 - Mahkeme Kararı
          INSERT INTO iym.EVRAK_KAYIT (
            ID, EVRAK_SIRA_NO, EVRAK_NO, GIRIS_TARIH, EVRAK_TARIHI,
            EVRAK_GELDIGI_KURUM, KAY_KULLANICI, EVRAK_TIPI, HAVALE_BIRIM,
            ACIKLAMA, GEL_IL, EVRAK_KONUSU, DURUMU, EVRAK_YONU, ACILMI,
            SORUSTURMA_NO, MAHKEME_KARAR_NO, ASIL_EVRAK, EVRAK_GRUP, ONAMA
          ) VALUES (
            iym.EVRAK_KAYIT_SEQ.NEXTVAL, 'TEST-2025-002', 'IDB-2025-12346',
            TO_DATE('2023-05-16 11:30:00', 'YYYY-MM-DD HH24:MI:SS'),
            TO_DATE('2023-05-11 15:00:00', 'YYYY-MM-DD HH24:MI:SS'),
            '02', admin_id, '401.01.00', 'HUKUK',
            'İletişimin denetlenmesi talebi örnek açıklama', '0600', 'Mahkeme Kararı Talebi',
            'AKTIF', 'ILETISIMIN_DENETLENMESI', 'H',
            '2025/002', 'MK-2025-002', 'H', 'Mahkeme Kararları', 'E'
          );
          
          -- Sample data 3 - İletişimin Tespiti
          INSERT INTO iym.EVRAK_KAYIT (
            ID, EVRAK_SIRA_NO, EVRAK_NO, GIRIS_TARIH, EVRAK_TARIHI,
            EVRAK_GELDIGI_KURUM, KAY_KULLANICI, EVRAK_TIPI, HAVALE_BIRIM,
            ACIKLAMA, GEL_IL, EVRAK_KONUSU, DURUMU, EVRAK_YONU, ACILMI,
            SORUSTURMA_NO, MAHKEME_KARAR_NO, ASIL_EVRAK, EVRAK_GRUP, ONAMA
          ) VALUES (
            iym.EVRAK_KAYIT_SEQ.NEXTVAL, 'TEST-2025-003', 'E-2025-12347',
            TO_DATE('2023-05-17 12:30:00', 'YYYY-MM-DD HH24:MI:SS'),
            TO_DATE('2023-05-12 16:00:00', 'YYYY-MM-DD HH24:MI:SS'),
            '02', admin_id, '401.01.00', 'HUKUK',
            'İletişimin denetlenmesi talebi örnek açıklama', '0100', 'İletişimin Denetlenmesş Talebi',
            'AKTIF', 'ILETISIMIN_DENETLENMESI', 'H',
            '2025/003', 'MK-2025-003', 'H', 'İletişim Tespiti', 'E'
          );
        EXCEPTION
          WHEN NO_DATA_FOUND THEN
            NULL; -- Admin user not found, skip all insertions
        END;
      END IF;
    END;
  END IF;
END;
/

COMMIT;
