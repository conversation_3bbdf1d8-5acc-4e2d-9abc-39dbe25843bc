package iym.backend.util;


import com.fasterxml.jackson.databind.DeserializationFeature;
import lombok.extern.slf4j.Slf4j;
import org.apache.hc.client5.http.classic.HttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClientBuilder;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager;
import org.apache.hc.client5.http.socket.ConnectionSocketFactory;
import org.apache.hc.client5.http.socket.PlainConnectionSocketFactory;
import org.apache.hc.client5.http.ssl.NoopHostnameVerifier;
import org.apache.hc.client5.http.ssl.SSLConnectionSocketFactory;
import org.apache.hc.core5.http.URIScheme;

import org.apache.hc.core5.http.config.Registry;
import org.apache.hc.core5.http.config.RegistryBuilder;
import org.apache.hc.core5.ssl.SSLContexts;
import org.apache.hc.core5.ssl.TrustStrategy;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.SSLContext;
import java.time.Duration;

@Slf4j
public class RestUtils {

    // Private constructor to hide the implicit public one
    private RestUtils() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    public static RestTemplate getRestTemplate(String endPointUri, int connectTimeout, int readTimeout) {
        if (endPointUri.startsWith("https"))
            return getRestTemplateHttps(connectTimeout, readTimeout);
        else if (endPointUri.startsWith("http")) {
            return getRestTemplateHttp(connectTimeout, readTimeout);
        }
        throw new IllegalArgumentException("Invalid protocol:" + endPointUri);
    }

    public static RestTemplate getRestTemplateHttp(int connectTimeout, int readTimeout) {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(connectTimeout);
        factory.setReadTimeout(readTimeout);
        return new RestTemplate(factory);
    }

    public static RestTemplate getRestTemplateHttps(int connectTimeout, int readTimeout) {
        try {

            TrustStrategy acceptingTrustStrategy = (cert, authType) -> true;
            SSLContext sslContext = SSLContexts.custom()
                    .loadTrustMaterial(null, acceptingTrustStrategy)
                    .build();
            SSLConnectionSocketFactory socketFactory = new SSLConnectionSocketFactory(sslContext, NoopHostnameVerifier.INSTANCE);

            Registry<ConnectionSocketFactory> socketRegistry = RegistryBuilder.<ConnectionSocketFactory>create()
                    .register(URIScheme.HTTPS.getId(), socketFactory)
                    .register(URIScheme.HTTP.getId(), new PlainConnectionSocketFactory())
                    .build();

            HttpClient httpClient = HttpClientBuilder.create()
                    .setConnectionManager(new PoolingHttpClientConnectionManager(socketRegistry))
                    .setConnectionManagerShared(true)
                    .build();

            ClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory(httpClient);

            RestTemplate restTemplate = new RestTemplateBuilder()
                    .setConnectTimeout(Duration.ofMillis(connectTimeout))
                    .setReadTimeout(Duration.ofMillis(readTimeout))
                    .build();

            restTemplate.setRequestFactory(requestFactory);

            //updateObjectMapperProps(restTemplate);
            return restTemplate;

        } catch (Exception e) {
            log.error("RestTemplate Https init failed", e);
            throw new RuntimeException(e.getMessage());
        }
    }

    public static void acceptSingleValueAsArray(RestTemplate restTemplate) {
        try {
            for (Object converter : restTemplate.getMessageConverters()) {
                if (converter instanceof MappingJackson2HttpMessageConverter jacksonConverter) {
                    jacksonConverter.getObjectMapper().enable(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY);
                }
            }
        } catch (Exception e) {
            log.error("ObjectMapper update failed", e);
        }
    }

}
