package iym.common.model.api;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum HedefTip {
	
	GSM(10),
	SABIT(20),
	UYDU(30),
	YURT_DISI(40),
	UMTH_MSISDN(41),
	UMTH_USERNAME(42),
	UMTH_IP(43),
	UMTH_PINCODE(44),
	EPOSTA(50),
	IP_TAKIP(51),
	URL_WEB_ADRESI_TAKIP(52),
	ADSL_ABONE_TAKIP(53),
	GPRS(54),
	IP_ENGELLEME(55),
	DOMAIN_ENGELLEME(56),
	IMEI(60),
	IMS<PERSON>(70),
	GPRS_<PERSON>MSI(71),
	XDSL_MSISDN(80),
	XDSL_TEMOSNO(81),
	XDSL_USERNAME(82),
	XDSL_IP(83),
	GPRS_GSM(90),
	GPRS_<PERSON>ME<PERSON>(91),
	GPRS_YURT_<PERSON>ISI(92),
	<PERSON><PERSON><PERSON><PERSON>(99),
	
	
	/*
	GSM_SONLANDIRMA(110),
	SABIT_SONLANDIRMA(120),
	UYDU_SONLANDIRMA(130),
	YURT_DISI_SONLANDIRMA(140),
	UMTH_MSISDN_SONLANDIRMA(141),
	UMTH_IP_SONLANDIRMA(143),
	EPOSTA_SONLANDIRMA(150),
	IP_TAKIP_SONLANDIRMA(151),
	URL_WEB_ADRESI_TAKIP_SONLANDIRMA(152),
	IMEI_SONLANDIRMA(160),
	IMSI_SONLANDIRMA(170),
	XDSL_MSISDN_SONLANDIRMA(180),
	XDSL_TEMOSNO_SONLANDIRMA(181),
	XDSL_USERNAME_SONLANDIRMA(182),
	XDSL_IP_SONLANDIRMA(183),
	GPRS_GSM_SONLANDIRMA(190),
	GPRS_IMEI_SONLANDIRMA(191),
	GPRS_YURT_DISI_SONLANDIRMA(192),
	TRUNK_SONLANDIRMA(199),
	*/

	GSM_YER_TESPITI(200),
	GSM_YER_TESPITI_SONLANDIRMA(210),
	YURTDISI_YER_TESPITI(201),
	YURTDISI_YER_TESPITI_SONLANDIRMA(211);
	
	private final int hedefKodu;
	
	HedefTip(int hedefKodu){
		this.hedefKodu = hedefKodu;
	}

	@JsonValue
	public int getHedefKodu(){
		return this.hedefKodu;
	}

	@JsonCreator
	public static HedefTip fromName(String name) {
		for (HedefTip hedefTip : HedefTip.values()) {
			if (hedefTip.name().equals(name)) {
				return hedefTip;
			}
		}
		throw new IllegalArgumentException("Gecersiz hedefTip: '" + name + "'");
	}

	//@JsonCreator
	public static HedefTip fromValue(int value) {
		for (HedefTip hedefTip : HedefTip.values()) {
			if (hedefTip.hedefKodu == value) {
				return hedefTip;
			}
		}
		throw new IllegalArgumentException("Gecersiz hedefKodu: '" + value + "'");
	}
}
