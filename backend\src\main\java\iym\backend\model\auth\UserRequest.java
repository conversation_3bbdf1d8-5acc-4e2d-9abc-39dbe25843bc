package iym.backend.model.auth;

import iym.common.model.api.KullaniciKurum;
import iym.common.model.enums.IymUserRoleType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * User registration/update request model
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserRequest {

    @NotNull
    @NotBlank
    private String userName;

    @NotNull
    @NotBlank
    private String password;

    private IymUserRoleType role;
    
    private KullaniciKurum kurum;
}
