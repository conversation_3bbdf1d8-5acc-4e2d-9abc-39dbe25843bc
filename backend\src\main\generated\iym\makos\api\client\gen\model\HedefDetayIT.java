/*
 * MAKOS OpenAPI definition
 * MAKOS Application
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package iym.makos.api.client.gen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import iym.makos.api.client.gen.model.Hedef;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * HedefDetayIT
 */
@JsonPropertyOrder({
  HedefDetayIT.JSON_PROPERTY_SORGU_TIPI,
  HedefDetayIT.JSON_PROPERTY_HEDEF,
  HedefDetayIT.JSON_PROPERTY_KARSI_HEDEF,
  HedefDetayIT.JSON_PROPERTY_BASLAMA_TARIHI,
  HedefDetayIT.JSON_PROPERTY_BITIS_TARIHI,
  HedefDetayIT.JSON_PROPERTY_TESPIT_TURU,
  HedefDetayIT.JSON_PROPERTY_TESPIT_TURU_DETAY,
  HedefDetayIT.JSON_PROPERTY_ACIKLAMA
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class HedefDetayIT {
  /**
   * Gets or Sets sorguTipi
   */
  public enum SorguTipiEnum {
    _1("1"),
    
    _2("2"),
    
    _3("3");

    private String value;

    SorguTipiEnum(String value) {
      this.value = value;
    }

    @JsonValue
    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static SorguTipiEnum fromValue(String value) {
      for (SorguTipiEnum b : SorguTipiEnum.values()) {
        if (b.value.equals(value)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + value + "'");
    }
  }

  public static final String JSON_PROPERTY_SORGU_TIPI = "sorguTipi";
  private SorguTipiEnum sorguTipi;

  public static final String JSON_PROPERTY_HEDEF = "hedef";
  private Hedef hedef;

  public static final String JSON_PROPERTY_KARSI_HEDEF = "karsiHedef";
  private Hedef karsiHedef;

  public static final String JSON_PROPERTY_BASLAMA_TARIHI = "baslamaTarihi";
  private LocalDateTime baslamaTarihi;

  public static final String JSON_PROPERTY_BITIS_TARIHI = "bitisTarihi";
  private LocalDateTime bitisTarihi;

  public static final String JSON_PROPERTY_TESPIT_TURU = "tespitTuru";
  private String tespitTuru;

  public static final String JSON_PROPERTY_TESPIT_TURU_DETAY = "tespitTuruDetay";
  private String tespitTuruDetay;

  public static final String JSON_PROPERTY_ACIKLAMA = "aciklama";
  private String aciklama;

  public HedefDetayIT() {
  }

  public HedefDetayIT sorguTipi(SorguTipiEnum sorguTipi) {
    
    this.sorguTipi = sorguTipi;
    return this;
  }

   /**
   * Get sorguTipi
   * @return sorguTipi
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_SORGU_TIPI)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public SorguTipiEnum getSorguTipi() {
    return sorguTipi;
  }


  @JsonProperty(JSON_PROPERTY_SORGU_TIPI)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setSorguTipi(SorguTipiEnum sorguTipi) {
    this.sorguTipi = sorguTipi;
  }


  public HedefDetayIT hedef(Hedef hedef) {
    
    this.hedef = hedef;
    return this;
  }

   /**
   * Get hedef
   * @return hedef
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_HEDEF)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public Hedef getHedef() {
    return hedef;
  }


  @JsonProperty(JSON_PROPERTY_HEDEF)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setHedef(Hedef hedef) {
    this.hedef = hedef;
  }


  public HedefDetayIT karsiHedef(Hedef karsiHedef) {
    
    this.karsiHedef = karsiHedef;
    return this;
  }

   /**
   * Get karsiHedef
   * @return karsiHedef
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_KARSI_HEDEF)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Hedef getKarsiHedef() {
    return karsiHedef;
  }


  @JsonProperty(JSON_PROPERTY_KARSI_HEDEF)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setKarsiHedef(Hedef karsiHedef) {
    this.karsiHedef = karsiHedef;
  }


  public HedefDetayIT baslamaTarihi(LocalDateTime baslamaTarihi) {
    
    this.baslamaTarihi = baslamaTarihi;
    return this;
  }

   /**
   * Get baslamaTarihi
   * @return baslamaTarihi
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_BASLAMA_TARIHI)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public LocalDateTime getBaslamaTarihi() {
    return baslamaTarihi;
  }


  @JsonProperty(JSON_PROPERTY_BASLAMA_TARIHI)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setBaslamaTarihi(LocalDateTime baslamaTarihi) {
    this.baslamaTarihi = baslamaTarihi;
  }


  public HedefDetayIT bitisTarihi(LocalDateTime bitisTarihi) {
    
    this.bitisTarihi = bitisTarihi;
    return this;
  }

   /**
   * Get bitisTarihi
   * @return bitisTarihi
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_BITIS_TARIHI)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public LocalDateTime getBitisTarihi() {
    return bitisTarihi;
  }


  @JsonProperty(JSON_PROPERTY_BITIS_TARIHI)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setBitisTarihi(LocalDateTime bitisTarihi) {
    this.bitisTarihi = bitisTarihi;
  }


  public HedefDetayIT tespitTuru(String tespitTuru) {
    
    this.tespitTuru = tespitTuru;
    return this;
  }

   /**
   * Get tespitTuru
   * @return tespitTuru
  **/
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_TESPIT_TURU)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getTespitTuru() {
    return tespitTuru;
  }


  @JsonProperty(JSON_PROPERTY_TESPIT_TURU)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setTespitTuru(String tespitTuru) {
    this.tespitTuru = tespitTuru;
  }


  public HedefDetayIT tespitTuruDetay(String tespitTuruDetay) {
    
    this.tespitTuruDetay = tespitTuruDetay;
    return this;
  }

   /**
   * Get tespitTuruDetay
   * @return tespitTuruDetay
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TESPIT_TURU_DETAY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getTespitTuruDetay() {
    return tespitTuruDetay;
  }


  @JsonProperty(JSON_PROPERTY_TESPIT_TURU_DETAY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTespitTuruDetay(String tespitTuruDetay) {
    this.tespitTuruDetay = tespitTuruDetay;
  }


  public HedefDetayIT aciklama(String aciklama) {
    
    this.aciklama = aciklama;
    return this;
  }

   /**
   * Get aciklama
   * @return aciklama
  **/
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ACIKLAMA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getAciklama() {
    return aciklama;
  }


  @JsonProperty(JSON_PROPERTY_ACIKLAMA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAciklama(String aciklama) {
    this.aciklama = aciklama;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    HedefDetayIT hedefDetayIT = (HedefDetayIT) o;
    return Objects.equals(this.sorguTipi, hedefDetayIT.sorguTipi) &&
        Objects.equals(this.hedef, hedefDetayIT.hedef) &&
        Objects.equals(this.karsiHedef, hedefDetayIT.karsiHedef) &&
        Objects.equals(this.baslamaTarihi, hedefDetayIT.baslamaTarihi) &&
        Objects.equals(this.bitisTarihi, hedefDetayIT.bitisTarihi) &&
        Objects.equals(this.tespitTuru, hedefDetayIT.tespitTuru) &&
        Objects.equals(this.tespitTuruDetay, hedefDetayIT.tespitTuruDetay) &&
        Objects.equals(this.aciklama, hedefDetayIT.aciklama);
  }

  @Override
  public int hashCode() {
    return Objects.hash(sorguTipi, hedef, karsiHedef, baslamaTarihi, bitisTarihi, tespitTuru, tespitTuruDetay, aciklama);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class HedefDetayIT {\n");
    sb.append("    sorguTipi: ").append(toIndentedString(sorguTipi)).append("\n");
    sb.append("    hedef: ").append(toIndentedString(hedef)).append("\n");
    sb.append("    karsiHedef: ").append(toIndentedString(karsiHedef)).append("\n");
    sb.append("    baslamaTarihi: ").append(toIndentedString(baslamaTarihi)).append("\n");
    sb.append("    bitisTarihi: ").append(toIndentedString(bitisTarihi)).append("\n");
    sb.append("    tespitTuru: ").append(toIndentedString(tespitTuru)).append("\n");
    sb.append("    tespitTuruDetay: ").append(toIndentedString(tespitTuruDetay)).append("\n");
    sb.append("    aciklama: ").append(toIndentedString(aciklama)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

